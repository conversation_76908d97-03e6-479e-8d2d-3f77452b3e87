/**
 * EFFICIENT MAINNET MONITOR
 * 
 * This version focuses on monitoring with minimal API calls
 * to avoid rate limits while still detecting new tokens
 */

import { Connection, PublicKey } from '@solana/web3.js';

// Efficient configuration
const CONFIG = {
  SOLANA_RPC: 'https://mainnet.helius-rpc.com/?api-key=7467031f-f99b-4f0d-b90e-21a70a6cdacf',
  SOLANA_WS: 'wss://mainnet.helius-rpc.com/?api-key=7467031f-f99b-4f0d-b90e-21a70a6cdacf',
  RAYDIUM_PROGRAM_ID: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
};

// Simple connection without extra API calls
const connection = new Connection(CONFIG.SOLANA_RPC, {
  commitment: 'confirmed',
  wsEndpoint: CONFIG.SOLANA_WS,
});

console.log('🎯 EFFICIENT MAINNET MONITOR');
console.log('=' .repeat(40));
console.log('🌐 Network: MAINNET');
console.log('📡 RPC: Helius');
console.log('⚡ Mode: Minimal API calls');
console.log('=' .repeat(40));

let activityCount = 0;
let raydiumCount = 0;
let tokenCount = 0;

/**
 * Start efficient monitoring
 */
async function startEfficientMonitoring() {
  try {
    console.log('\n🚀 Starting efficient monitoring...');
    
    // Monitor Raydium program changes (most likely to show new tokens)
    const raydiumSub = connection.onProgramAccountChange(
      new PublicKey(CONFIG.RAYDIUM_PROGRAM_ID),
      (accountInfo, context) => {
        raydiumCount++;
        console.log(`\n🏊 RAYDIUM ACTIVITY #${raydiumCount}`);
        console.log(`   📊 Slot: ${context.slot}`);

        // Safe account ID handling
        const accountId = context.accountId || context.pubkey;
        if (accountId) {
          console.log(`   🏦 Account: ${accountId.toBase58().slice(0, 16)}...`);
        } else {
          console.log(`   🏦 Account: [Unknown]`);
        }

        console.log(`   ⏰ Time: ${new Date().toLocaleTimeString()}`);
        console.log(`   📝 Data size: ${accountInfo.data?.length || 0} bytes`);

        // This could be a new pool/market creation
        if (accountInfo.data && accountInfo.data.length > 0) {
          console.log('   🎯 Potential new pool/market detected!');

          // Try to extract token information from the data
          if (accountInfo.data.length >= 32) {
            try {
              // Extract potential token mints from the account data
              const data = accountInfo.data;
              console.log(`   🔍 Analyzing ${data.length} bytes of pool data...`);

              // Look for token mint addresses (32 bytes each)
              for (let i = 0; i < Math.min(data.length - 32, 200); i += 32) {
                const potentialMint = data.slice(i, i + 32);
                if (potentialMint.every(byte => byte !== 0)) {
                  const mintPubkey = new PublicKey(potentialMint);
                  console.log(`   🪙 Potential token mint: ${mintPubkey.toBase58()}`);
                }
              }
            } catch (error) {
              console.log('   ⚠️ Error parsing pool data');
            }
          }
        }
      },
      'confirmed'
    );
    
    console.log(`✅ Monitoring Raydium program (Sub ID: ${raydiumSub})`);
    console.log('👀 Waiting for new activity...');
    
    // Simple status updates (no API calls)
    setInterval(() => {
      activityCount++;
      console.log(`\n📊 [${new Date().toLocaleTimeString()}] Status Update #${activityCount}`);
      console.log(`   🏊 Raydium activities: ${raydiumCount}`);
      console.log(`   🆕 Token activities: ${tokenCount}`);
      console.log(`   ✅ Monitoring active...`);
    }, 60000); // Every 60 seconds
    
  } catch (error) {
    console.error('❌ Failed to start monitoring:', error);
  }
}

// Start monitoring
startEfficientMonitoring();

// Keep process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping monitor...');
  process.exit(0);
});
