import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/hooks/useAuth";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { globalErrorHandler } from "@/lib/error/globalHandler";
import { Suspense, lazy } from "react";

// Lazy load pages for code splitting
const Index = lazy(() => import("./pages/Index"));
const Dashboard = lazy(() => import("./pages/Dashboard"));
const Settings = lazy(() => import("./pages/Settings"));
const SimpleSniperSettings = lazy(() => import("./pages/SimpleSniperSettings"));
const Auth = lazy(() => import("./pages/Auth"));
const WalletPage = lazy(() => import("./pages/Wallet"));
const About = lazy(() => import("./pages/About"));
const NotFound = lazy(() => import("./pages/NotFound"));
import { RefreshCw } from "lucide-react";

// Configure QueryClient with error handling
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error && typeof error === 'object' && 'status' in error) {
          const status = (error as any).status;
          if (status >= 400 && status < 500) {
            return false;
          }
        }
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      onError: (error) => {
        globalErrorHandler.handleApiError(
          error as Error,
          'query',
          'GET'
        );
      },
    },
    mutations: {
      retry: false,
      onError: (error) => {
        globalErrorHandler.handleApiError(
          error as Error,
          'mutation',
          'POST'
        );
      },
    },
  },
});

// Loading component
const LoadingFallback = () => (
  <div className="min-h-screen bg-background flex items-center justify-center">
    <div className="text-foreground flex items-center gap-2">
      <RefreshCw className="w-4 h-4 animate-spin" />
      Loading...
    </div>
  </div>
);

const App = () => (
  <ErrorBoundary level="critical" onError={(error, errorInfo) => {
    globalErrorHandler.handleError(error, 'app-root', 'error', 'ui', { errorInfo });
  }}>
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary level="page" onError={(error, errorInfo) => {
        globalErrorHandler.handleError(error, 'auth-provider', 'error', 'auth', { errorInfo });
      }}>
        <AuthProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <ErrorBoundary level="page" onError={(error, errorInfo) => {
              globalErrorHandler.handleError(error, 'router', 'error', 'ui', { errorInfo });
            }}>
              <BrowserRouter>
                <Suspense fallback={<LoadingFallback />}>
                  <Routes>
                    <Route path="/" element={
                      <ErrorBoundary level="page">
                        <Index />
                      </ErrorBoundary>
                    } />
                    <Route path="/dashboard" element={
                      <ErrorBoundary level="page">
                        <Dashboard />
                      </ErrorBoundary>
                    } />
                    <Route path="/wallet" element={
                      <ErrorBoundary level="page">
                        <WalletPage />
                      </ErrorBoundary>
                    } />
                    <Route path="/settings" element={
                      <ErrorBoundary level="page">
                        <Settings />
                      </ErrorBoundary>
                    } />

                    <Route path="/sniper-settings" element={
                      <ErrorBoundary level="page">
                        <SimpleSniperSettings />
                      </ErrorBoundary>
                    } />
                    <Route path="/about" element={
                      <ErrorBoundary level="page">
                        <About />
                      </ErrorBoundary>
                    } />
                    <Route path="/auth" element={
                      <ErrorBoundary level="page">
                        <Auth />
                      </ErrorBoundary>
                    } />
                    {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                    <Route path="*" element={
                      <ErrorBoundary level="page">
                        <NotFound />
                      </ErrorBoundary>
                    } />
                  </Routes>
                </Suspense>
              </BrowserRouter>
            </ErrorBoundary>
          </TooltipProvider>
        </AuthProvider>
      </ErrorBoundary>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
