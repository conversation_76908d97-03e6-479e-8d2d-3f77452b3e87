// Environment configuration management
export interface AppConfig {
  solana: {
    network: 'mainnet' | 'devnet' | 'testnet';
    rpcUrl: string;
    wsUrl: string;
    rpcUrls: {
      mainnet: string;
      devnet: string;
      testnet: string;
    };
    wsUrls: {
      mainnet: string;
      devnet: string;
      testnet: string;
    };
  };
  jupiter: {
    apiUrl: string;
    priceApiUrl: string;
  };
  apis: {
    dexscreener: string;
    coingecko: string;
    birdeye?: string | null;
  };
  trading: {
    defaultSlippage: number;
    maxSlippage: number;
    defaultPriorityFee: number;
    maxPriorityFee: number;
  };
  security: {
    encryptionKey: string;
    appSecret: string;
  };
  supabase: {
    url: string;
    anonKey: string;
  };
  rateLimit: {
    requestsPerMinute: number;
    burst: number;
  };
  tokenDetection: {
    minLiquiditySol: number;
    minHolders: number;
    maxSupply: number;
  };
  riskManagement: {
    maxPositionSizeSol: number;
    stopLossPercentage: number;
    takeProfitPercentage: number;
  };
  development: {
    debugMode: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
}

// Get environment variable with fallback
const getEnvVar = (key: string, fallback?: string): string => {
  const value = import.meta.env[key];
  // Check if value exists and is not empty string
  if (!value && fallback === undefined) {
    throw new Error(`Environment variable ${key} is required`);
  }
  // Return value if it exists and is not empty, otherwise return fallback
  return (value && value.trim() !== '') ? value : (fallback || '');
};

// Get numeric environment variable
const getEnvNumber = (key: string, fallback: number): number => {
  const value = import.meta.env[key];
  return value ? parseFloat(value) : fallback;
};

// Get boolean environment variable
const getEnvBoolean = (key: string, fallback: boolean): boolean => {
  const value = import.meta.env[key];
  return value ? value.toLowerCase() === 'true' : fallback;
};

// Create configuration object
export const config: AppConfig = {
  solana: {
    network: (getEnvVar('VITE_SOLANA_NETWORK', 'devnet') as 'mainnet' | 'devnet' | 'testnet'),
    rpcUrl: getEnvVar('VITE_SOLANA_RPC_URL', 'https://api.devnet.solana.com'),
    wsUrl: getEnvVar('VITE_SOLANA_WS_URL', 'wss://api.devnet.solana.com'),
    rpcUrls: {
      mainnet: getEnvVar('VITE_SOLANA_RPC_URL_MAINNET', 'https://api.mainnet-beta.solana.com'),
      devnet: getEnvVar('VITE_SOLANA_RPC_URL_DEVNET', 'https://api.devnet.solana.com'),
      testnet: getEnvVar('VITE_SOLANA_RPC_URL_TESTNET', 'https://api.testnet.solana.com'),
    },
    wsUrls: {
      mainnet: getEnvVar('VITE_SOLANA_WS_URL_MAINNET', 'wss://api.mainnet-beta.solana.com'),
      devnet: getEnvVar('VITE_SOLANA_WS_URL', 'wss://api.devnet.solana.com'),
      testnet: getEnvVar('VITE_SOLANA_WS_URL_TESTNET', 'wss://api.testnet.solana.com'),
    },
  },
  jupiter: {
    apiUrl: getEnvVar('VITE_JUPITER_API_URL', 'https://quote-api.jup.ag/v6'),
    priceApiUrl: getEnvVar('VITE_JUPITER_PRICE_API_URL', 'https://price.jup.ag/v4'),
  },
  apis: {
    dexscreener: getEnvVar('VITE_DEXSCREENER_API_URL', 'https://api.dexscreener.com/latest'),
    coingecko: getEnvVar('VITE_COINGECKO_API_URL', 'https://api.coingecko.com/api/v3'),
    birdeye: import.meta.env.VITE_BIRDEYE_API_URL || null, // Optional - only use if explicitly set
  },
  trading: {
    defaultSlippage: getEnvNumber('VITE_DEFAULT_SLIPPAGE', 1),
    maxSlippage: getEnvNumber('VITE_MAX_SLIPPAGE', 10),
    defaultPriorityFee: getEnvNumber('VITE_DEFAULT_PRIORITY_FEE', 0.0001),
    maxPriorityFee: getEnvNumber('VITE_MAX_PRIORITY_FEE', 0.01),
  },
  security: {
    encryptionKey: getEnvVar('VITE_ENCRYPTION_KEY', 'swift-sniper-fi-encryption-key-32'),
    appSecret: getEnvVar('VITE_APP_SECRET', 'swift-sniper-fi-app-secret-key'),
  },
  supabase: {
    url: getEnvVar('VITE_SUPABASE_URL'),
    anonKey: getEnvVar('VITE_SUPABASE_ANON_KEY'),
  },
  rateLimit: {
    requestsPerMinute: getEnvNumber('VITE_RATE_LIMIT_REQUESTS_PER_MINUTE', 60),
    burst: getEnvNumber('VITE_RATE_LIMIT_BURST', 10),
  },
  tokenDetection: {
    minLiquiditySol: getEnvNumber('VITE_MIN_LIQUIDITY_SOL', 1),
    minHolders: getEnvNumber('VITE_MIN_HOLDERS', 10),
    maxSupply: getEnvNumber('VITE_MAX_SUPPLY', 1000000000),
  },
  riskManagement: {
    maxPositionSizeSol: getEnvNumber('VITE_MAX_POSITION_SIZE_SOL', 10),
    stopLossPercentage: getEnvNumber('VITE_STOP_LOSS_PERCENTAGE', 20),
    takeProfitPercentage: getEnvNumber('VITE_TAKE_PROFIT_PERCENTAGE', 100),
  },
  development: {
    debugMode: getEnvBoolean('VITE_DEBUG_MODE', true),
    logLevel: (getEnvVar('VITE_LOG_LEVEL', 'debug') as 'debug' | 'info' | 'warn' | 'error'),
  },
};

// Export individual config sections for convenience
export const solanaConfig = config.solana;
export const jupiterConfig = config.jupiter;
export const tradingConfig = config.trading;
export const securityConfig = config.security;
export const supabaseConfig = config.supabase;

// Validation function
export const validateConfig = (): void => {
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
  ];

  const missing = requiredVars.filter(key => !import.meta.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate network
  if (!['mainnet', 'devnet', 'testnet'].includes(config.solana.network)) {
    throw new Error('VITE_SOLANA_NETWORK must be one of: mainnet, devnet, testnet');
  }

  // Validate slippage
  if (config.trading.defaultSlippage > config.trading.maxSlippage) {
    throw new Error('Default slippage cannot be greater than max slippage');
  }

  console.log('✅ Configuration validated successfully');
};

// Initialize and validate config on import
try {
  validateConfig();
} catch (error) {
  console.error('❌ Configuration validation failed:', error);
  throw error;
}
