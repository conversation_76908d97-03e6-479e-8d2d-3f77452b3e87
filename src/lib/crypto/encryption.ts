import * as CryptoJ<PERSON> from 'crypto-js';
import config from '@/config';

// Encryption configuration
const ENCRYPTION_CONFIG = {
  algorithm: 'AES',
  mode: CryptoJS.mode.GCM,
  padding: CryptoJS.pad.NoPadding,
  keySize: 256 / 32, // 256 bits
  ivSize: 96 / 8, // 96 bits for GCM
  tagSize: 128 / 8, // 128 bits
  iterations: 10000, // PBKDF2 iterations
};

/**
 * Generate a random salt
 */
export const generateSalt = (): string => {
  return CryptoJS.lib.WordArray.random(128 / 8).toString();
};

/**
 * Generate a random IV
 */
export const generateIV = (): string => {
  return CryptoJS.lib.WordArray.random(ENCRYPTION_CONFIG.ivSize).toString();
};

/**
 * Derive key from password using PBKDF2
 */
export const deriveKey = (password: string, salt: string): CryptoJS.lib.WordArray => {
  return CryptoJS.PBKDF2(password, salt, {
    keySize: ENCRYPTION_CONFIG.keySize,
    iterations: ENCRYPTION_CONFIG.iterations,
    hasher: CryptoJS.algo.SHA256,
  });
};

/**
 * Encrypt data using AES-GCM
 */
export const encrypt = (data: string, password: string): string => {
  try {
    const salt = generateSalt();
    const iv = generateIV();
    const key = deriveKey(password, salt);

    const encrypted = CryptoJS.AES.encrypt(data, key, {
      iv: CryptoJS.enc.Hex.parse(iv),
      mode: ENCRYPTION_CONFIG.mode,
      padding: ENCRYPTION_CONFIG.padding,
    });

    // Combine salt, iv, and encrypted data
    const result = {
      salt,
      iv,
      encrypted: encrypted.toString(),
      tag: encrypted.tag?.toString() || '',
    };

    return btoa(JSON.stringify(result));
  } catch (error) {
    console.error('Encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
};

/**
 * Decrypt data using AES-GCM
 */
export const decrypt = (encryptedData: string, password: string): string => {
  try {
    const data = JSON.parse(atob(encryptedData));
    const { salt, iv, encrypted, tag } = data;

    const key = deriveKey(password, salt);

    const decrypted = CryptoJS.AES.decrypt(encrypted, key, {
      iv: CryptoJS.enc.Hex.parse(iv),
      mode: ENCRYPTION_CONFIG.mode,
      padding: ENCRYPTION_CONFIG.padding,
    });

    const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
    
    if (!decryptedString) {
      throw new Error('Invalid password or corrupted data');
    }

    return decryptedString;
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Failed to decrypt data');
  }
};

/**
 * Encrypt private key with user password
 */
export const encryptPrivateKey = (privateKey: string, password: string): string => {
  if (!privateKey || !password) {
    throw new Error('Private key and password are required');
  }

  return encrypt(privateKey, password);
};

/**
 * Decrypt private key with user password
 */
export const decryptPrivateKey = (encryptedPrivateKey: string, password: string): string => {
  if (!encryptedPrivateKey || !password) {
    throw new Error('Encrypted private key and password are required');
  }

  return decrypt(encryptedPrivateKey, password);
};

/**
 * Encrypt sensitive data using app secret
 */
export const encryptWithAppSecret = (data: string): string => {
  const appSecret = config.security.appSecret;
  if (!appSecret) {
    throw new Error('App secret not configured');
  }

  return encrypt(data, appSecret);
};

/**
 * Decrypt sensitive data using app secret
 */
export const decryptWithAppSecret = (encryptedData: string): string => {
  const appSecret = config.security.appSecret;
  if (!appSecret) {
    throw new Error('App secret not configured');
  }

  return decrypt(encryptedData, appSecret);
};

/**
 * Generate a secure random password
 */
export const generateSecurePassword = (length: number = 32): string => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  
  return password;
};

/**
 * Hash password for storage (one-way)
 */
export const hashPassword = (password: string): string => {
  const salt = generateSalt();
  const hash = CryptoJS.PBKDF2(password, salt, {
    keySize: 256 / 32,
    iterations: ENCRYPTION_CONFIG.iterations,
    hasher: CryptoJS.algo.SHA256,
  });

  return `${salt}:${hash.toString()}`;
};

/**
 * Verify password against hash
 */
export const verifyPassword = (password: string, hash: string): boolean => {
  try {
    const [salt, storedHash] = hash.split(':');
    const computedHash = CryptoJS.PBKDF2(password, salt, {
      keySize: 256 / 32,
      iterations: ENCRYPTION_CONFIG.iterations,
      hasher: CryptoJS.algo.SHA256,
    });

    return computedHash.toString() === storedHash;
  } catch (error) {
    console.error('Password verification failed:', error);
    return false;
  }
};

/**
 * Generate a secure mnemonic phrase (simplified)
 */
export const generateMnemonic = (): string => {
  // This is a simplified version. In production, use a proper BIP39 library
  const words = [
    'abandon', 'ability', 'able', 'about', 'above', 'absent', 'absorb', 'abstract',
    'absurd', 'abuse', 'access', 'accident', 'account', 'accuse', 'achieve', 'acid',
    'acoustic', 'acquire', 'across', 'act', 'action', 'actor', 'actress', 'actual',
    // ... add more BIP39 words
  ];

  const mnemonic = [];
  for (let i = 0; i < 12; i++) {
    const randomIndex = Math.floor(Math.random() * words.length);
    mnemonic.push(words[randomIndex]);
  }

  return mnemonic.join(' ');
};

/**
 * Validate mnemonic phrase
 */
export const validateMnemonic = (mnemonic: string): boolean => {
  // Simplified validation - check word count and basic format
  const words = mnemonic.trim().split(/\s+/);
  return words.length === 12 || words.length === 24;
};

/**
 * Secure memory cleanup (best effort)
 */
export const secureCleanup = (sensitiveData: string): void => {
  // In JavaScript, we can't truly clear memory, but we can overwrite the string
  // This is more of a symbolic gesture than actual security
  try {
    if (typeof sensitiveData === 'string') {
      // Overwrite with random data
      const randomData = generateSecurePassword(sensitiveData.length);
      sensitiveData = randomData;
    }
  } catch (error) {
    // Ignore cleanup errors
  }
};

/**
 * Validate encryption strength
 */
export const validateEncryptionStrength = (password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} => {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) score += 1;
  else feedback.push('Password should be at least 8 characters long');

  if (password.length >= 12) score += 1;
  else feedback.push('Password should be at least 12 characters long for better security');

  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('Password should contain lowercase letters');

  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('Password should contain uppercase letters');

  if (/[0-9]/.test(password)) score += 1;
  else feedback.push('Password should contain numbers');

  if (/[^a-zA-Z0-9]/.test(password)) score += 1;
  else feedback.push('Password should contain special characters');

  return {
    isValid: score >= 4,
    score,
    feedback,
  };
};
