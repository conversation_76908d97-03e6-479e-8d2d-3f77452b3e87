import { Keypair, VersionedTransaction, TransactionMessage, PublicKey } from '@solana/web3.js';
import { getConnection } from '../solana/connection';
import { transactionManager } from '../solana/transactions';
import { jupiterAPI, JupiterQuoteRequest, JupiterQuoteResponse, JupiterSwapRequest } from './api';
import { NATIVE_SOL_MINT, TOKEN_DECIMALS } from '../solana/constants';
import config from '@/config';

export interface SwapParams {
  inputMint: string;
  outputMint: string;
  amount: number; // In UI amount (e.g., 1.5 SOL, not lamports)
  slippageBps?: number;
  userKeypair: Keypair;
  priorityFee?: number;
  maxRetries?: number;
}

export interface SwapQuote {
  inputAmount: number;
  outputAmount: number;
  minimumOutputAmount: number;
  priceImpact: number;
  fee: number;
  route: string[];
  quote: JupiterQuoteResponse;
}

export interface SwapResult {
  success: boolean;
  signature?: string;
  error?: string;
  inputAmount?: number;
  outputAmount?: number;
  priceImpact?: number;
  actualSlippage?: number;
  executionTime?: number;
  attempts?: number;
}

export class JupiterSwapManager {
  private connection = getConnection();

  /**
   * Get swap quote
   */
  async getSwapQuote(params: SwapParams): Promise<SwapQuote> {
    try {
      const { inputMint, outputMint, amount, slippageBps } = params;

      // Get token decimals
      const inputDecimals = this.getTokenDecimals(inputMint);
      const outputDecimals = this.getTokenDecimals(outputMint);

      // Convert UI amount to raw amount
      const rawAmount = this.parseAmount(amount, inputDecimals);

      // Create quote request
      const quoteRequest: JupiterQuoteRequest = {
        inputMint,
        outputMint,
        amount: rawAmount,
        slippageBps: slippageBps || config.trading.defaultSlippage * 100,
        swapMode: 'ExactIn',
        onlyDirectRoutes: false,
        asLegacyTransaction: false,
        maxAccounts: 64,
      };

      // Get quote from Jupiter
      const quote = await jupiterAPI.getQuote(quoteRequest);

      // Calculate output amounts
      const outputAmount = this.formatAmount(quote.outAmount, outputDecimals);
      const minimumOutputAmount = this.formatAmount(quote.otherAmountThreshold, outputDecimals);
      const priceImpact = parseFloat(quote.priceImpactPct);

      // Extract route information
      const route = quote.routePlan.map(plan => plan.swapInfo.label);

      // Calculate fee (simplified)
      const fee = quote.platformFee ? parseFloat(quote.platformFee.amount) : 0;

      return {
        inputAmount: amount,
        outputAmount,
        minimumOutputAmount,
        priceImpact,
        fee,
        route,
        quote,
      };
    } catch (error) {
      console.error('Failed to get swap quote:', error);
      throw new Error(`Swap quote failed: ${error}`);
    }
  }

  /**
   * Execute swap transaction with comprehensive error handling and MEV protection
   */
  async executeSwap(params: SwapParams): Promise<SwapResult> {
    const startTime = Date.now();
    let attempts = 0;
    const maxAttempts = params.maxRetries || 3;

    while (attempts < maxAttempts) {
      attempts++;

      try {
        console.log(`🔄 Swap attempt ${attempts}/${maxAttempts} for ${params.amount} ${params.inputMint.slice(0, 8)}...`);

        // Get fresh quote for each attempt
        const swapQuote = await this.getSwapQuote(params);

        // Validate quote
        if (swapQuote.priceImpact > config.trading.maxSlippage) {
          throw new Error(`Price impact too high: ${swapQuote.priceImpact}% (max: ${config.trading.maxSlippage}%)`);
        }

        // Additional safety checks
        if (swapQuote.outputAmount <= 0) {
          throw new Error('Invalid output amount from quote');
        }

        // Create swap request with MEV protection
        const swapRequest: JupiterSwapRequest = {
          quoteResponse: swapQuote.quote,
          userPublicKey: params.userKeypair.publicKey.toBase58(),
          wrapAndUnwrapSol: true,
          useSharedAccounts: true,
          computeUnitPriceMicroLamports: params.priorityFee || 5000,
          asLegacyTransaction: false,
          dynamicComputeUnitLimit: true, // Enable dynamic compute units
          prioritizationFeeLamports: params.priorityFee || 5000,
        };

        console.log(`📊 Quote: ${swapQuote.inputAmount} → ${swapQuote.outputAmount} (Impact: ${swapQuote.priceImpact}%)`);

        // Get swap transaction
        const swapResponse = await jupiterAPI.getSwapTransaction(swapRequest);

        if (!swapResponse.swapTransaction) {
          throw new Error('No swap transaction returned from Jupiter');
        }

        // Deserialize and validate transaction
        const transaction = jupiterAPI.deserializeTransaction(swapResponse.swapTransaction);

        if (!transaction) {
          throw new Error('Failed to deserialize transaction');
        }

        // Get recent blockhash for transaction
        const { blockhash, lastValidBlockHeight } = await this.connection.getLatestBlockhash('confirmed');
        transaction.recentBlockhash = blockhash;

        // Sign transaction
        transaction.sign([params.userKeypair]);

        // Verify transaction is properly signed
        if (!transaction.signature) {
          throw new Error('Transaction signing failed');
        }

        console.log(`📝 Transaction signed: ${transaction.signature.toString('base64').slice(0, 16)}...`);

        // Send transaction with optimized settings
        const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
          skipPreflight: false,
          preflightCommitment: 'confirmed',
          maxRetries: 0, // We handle retries ourselves
        });

        console.log(`🚀 Transaction sent: ${signature}`);

        // Confirm transaction with timeout
        const confirmationPromise = this.connection.confirmTransaction({
          signature,
          blockhash,
          lastValidBlockHeight,
        }, 'confirmed');

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Transaction confirmation timeout')), 30000);
        });

        const confirmation = await Promise.race([confirmationPromise, timeoutPromise]) as any;

        if (confirmation.value.err) {
          const errorMsg = this.parseTransactionError(confirmation.value.err);
          throw new Error(`Transaction failed: ${errorMsg}`);
        }

        // Calculate actual slippage
        const actualSlippage = this.calculateActualSlippage(swapQuote, confirmation);

        const executionTime = Date.now() - startTime;
        console.log(`✅ Swap completed in ${executionTime}ms (Slippage: ${actualSlippage?.toFixed(2)}%)`);

        return {
          success: true,
          signature,
          inputAmount: swapQuote.inputAmount,
          outputAmount: swapQuote.outputAmount,
          priceImpact: swapQuote.priceImpact,
          actualSlippage,
          executionTime,
          attempts,
        };

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn(`❌ Swap attempt ${attempts} failed: ${errorMessage}`);

        // Check if we should retry
        if (attempts >= maxAttempts) {
          console.error('🚫 All swap attempts failed');
          return {
            success: false,
            error: errorMessage,
            attempts,
            executionTime: Date.now() - startTime,
          };
        }

        // Wait before retry (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempts - 1), 5000);
        console.log(`⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // This should never be reached, but TypeScript requires it
    return {
      success: false,
      error: 'Maximum retry attempts exceeded',
      attempts: maxAttempts,
      executionTime: Date.now() - startTime,
    };
  }

  /**
   * Simulate swap before execution
   */
  async simulateSwap(params: SwapParams): Promise<{
    success: boolean;
    quote?: SwapQuote;
    error?: string;
    warnings?: string[];
  }> {
    try {
      const quote = await this.getSwapQuote(params);
      const warnings: string[] = [];

      // Check for warnings
      if (quote.priceImpact > 5) {
        warnings.push(`High price impact: ${quote.priceImpact.toFixed(2)}%`);
      }

      if (quote.route.length > 3) {
        warnings.push(`Complex route with ${quote.route.length} hops may increase slippage`);
      }

      return {
        success: true,
        quote,
        warnings,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Simulation failed',
      };
    }
  }

  /**
   * Get best route for swap
   */
  async getBestRoute(
    inputMint: string,
    outputMint: string,
    amount: number
  ): Promise<{
    directRoute?: SwapQuote;
    bestRoute?: SwapQuote;
    alternatives: SwapQuote[];
  }> {
    try {
      const baseParams = {
        inputMint,
        outputMint,
        amount,
        userKeypair: Keypair.generate(), // Dummy keypair for quote
      };

      // Get direct route
      const directQuote = await this.getSwapQuote({
        ...baseParams,
        slippageBps: config.trading.defaultSlippage * 100,
      });

      // Get alternative routes with different settings
      const alternatives: SwapQuote[] = [];
      
      // Try different slippage tolerances
      for (const slippage of [0.5, 1, 2, 5]) {
        try {
          const quote = await this.getSwapQuote({
            ...baseParams,
            slippageBps: slippage * 100,
          });
          alternatives.push(quote);
        } catch (error) {
          // Ignore failed quotes
        }
      }

      // Find best route (highest output amount with acceptable price impact)
      const bestRoute = alternatives.reduce((best, current) => {
        if (current.priceImpact <= config.trading.maxSlippage) {
          if (!best || current.outputAmount > best.outputAmount) {
            return current;
          }
        }
        return best;
      }, undefined as SwapQuote | undefined);

      return {
        directRoute: directQuote,
        bestRoute: bestRoute || directQuote,
        alternatives,
      };
    } catch (error) {
      console.error('Failed to get best route:', error);
      throw error;
    }
  }

  /**
   * Calculate swap impact on portfolio
   */
  async calculateSwapImpact(
    params: SwapParams,
    currentBalances: Record<string, number>
  ): Promise<{
    beforeSwap: Record<string, number>;
    afterSwap: Record<string, number>;
    netChange: Record<string, number>;
    portfolioValue: {
      before: number;
      after: number;
      change: number;
    };
  }> {
    try {
      const quote = await this.getSwapQuote(params);
      
      const beforeSwap = { ...currentBalances };
      const afterSwap = { ...currentBalances };
      
      // Update balances after swap
      afterSwap[params.inputMint] = (afterSwap[params.inputMint] || 0) - params.amount;
      afterSwap[params.outputMint] = (afterSwap[params.outputMint] || 0) + quote.outputAmount;
      
      // Calculate net changes
      const netChange: Record<string, number> = {};
      for (const mint of Object.keys({ ...beforeSwap, ...afterSwap })) {
        netChange[mint] = (afterSwap[mint] || 0) - (beforeSwap[mint] || 0);
      }

      // Calculate portfolio values (simplified - would need price data)
      const portfolioValue = {
        before: 0, // Would calculate based on token prices
        after: 0,  // Would calculate based on token prices
        change: 0,
      };

      return {
        beforeSwap,
        afterSwap,
        netChange,
        portfolioValue,
      };
    } catch (error) {
      console.error('Failed to calculate swap impact:', error);
      throw error;
    }
  }

  /**
   * Helper methods
   */
  private getTokenDecimals(mint: string): number {
    if (mint === NATIVE_SOL_MINT.toBase58()) {
      return TOKEN_DECIMALS.SOL;
    }
    // For other tokens, you'd typically fetch this from the mint account
    // For now, assume 6 decimals (common for many SPL tokens)
    return 6;
  }

  private parseAmount(amount: number, decimals: number): string {
    return Math.floor(amount * Math.pow(10, decimals)).toString();
  }

  private formatAmount(amount: string, decimals: number): number {
    return parseInt(amount) / Math.pow(10, decimals);
  }

  /**
   * Parse transaction error for user-friendly message
   */
  private parseTransactionError(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.InstructionError) {
      const [index, instructionError] = error.InstructionError;

      if (instructionError?.Custom) {
        const errorCode = instructionError.Custom;

        // Common Jupiter/Solana error codes
        switch (errorCode) {
          case 6000:
            return 'Slippage tolerance exceeded';
          case 6001:
            return 'Invalid calculation';
          case 6002:
            return 'Missing oracle account';
          case 6003:
            return 'Invalid oracle account';
          case 6004:
            return 'Invalid token account';
          case 6005:
            return 'Invalid mint';
          case 6006:
            return 'Invalid token program';
          case 6007:
            return 'Invalid amount';
          case 6008:
            return 'Invalid fee';
          case 6009:
            return 'Invalid slippage';
          case 6010:
            return 'Insufficient funds';
          default:
            return `Transaction error (code: ${errorCode})`;
        }
      }

      if (instructionError?.InsufficientFunds) {
        return 'Insufficient funds for transaction';
      }

      if (instructionError?.InvalidAccountData) {
        return 'Invalid account data';
      }

      return `Instruction error at index ${index}`;
    }

    return JSON.stringify(error);
  }

  /**
   * Calculate actual slippage from transaction result
   */
  private calculateActualSlippage(quote: SwapQuote, confirmation: any): number | undefined {
    try {
      // This would require parsing the transaction logs to get actual amounts
      // For now, return undefined as we'd need more complex log parsing
      return undefined;
    } catch (error) {
      console.warn('Failed to calculate actual slippage:', error);
      return undefined;
    }
  }

  /**
   * Check if transaction should be retried based on error
   */
  private shouldRetryTransaction(error: Error): boolean {
    const retryableErrors = [
      'blockhash not found',
      'transaction was not confirmed',
      'timeout',
      'network error',
      'rpc error',
      'connection error',
      'insufficient funds', // Sometimes temporary
    ];

    const errorMessage = error.message.toLowerCase();
    return retryableErrors.some(retryableError => errorMessage.includes(retryableError));
  }

  /**
   * Estimate transaction fee
   */
  async estimateTransactionFee(params: SwapParams): Promise<number> {
    try {
      const quote = await this.getSwapQuote(params);

      // Base transaction fee (5000 lamports = 0.000005 SOL)
      let estimatedFee = 5000;

      // Add priority fee if specified
      if (params.priorityFee) {
        estimatedFee += params.priorityFee;
      }

      // Add compute unit fee based on transaction complexity
      const complexityMultiplier = quote.route.length; // More hops = more complex
      estimatedFee += complexityMultiplier * 1000;

      return estimatedFee;
    } catch (error) {
      console.warn('Failed to estimate transaction fee:', error);
      return 10000; // Default estimate
    }
  }

  /**
   * Validate swap parameters
   */
  validateSwapParams(params: SwapParams): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!params.inputMint || !params.outputMint) {
      errors.push('Input and output mints are required');
    }

    if (params.inputMint === params.outputMint) {
      errors.push('Input and output mints cannot be the same');
    }

    if (params.amount <= 0) {
      errors.push('Amount must be greater than 0');
    }

    if (params.slippageBps && (params.slippageBps < 0 || params.slippageBps > 5000)) {
      errors.push('Slippage must be between 0 and 50%');
    }

    if (!params.userKeypair) {
      errors.push('User keypair is required');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const jupiterSwapManager = new JupiterSwapManager();

// Export convenience functions
export const getSwapQuote = (params: SwapParams) => jupiterSwapManager.getSwapQuote(params);
export const executeSwap = (params: SwapParams) => jupiterSwapManager.executeSwap(params);
export const simulateSwap = (params: SwapParams) => jupiterSwapManager.simulateSwap(params);
export const getBestRoute = (inputMint: string, outputMint: string, amount: number) => 
  jupiterSwapManager.getBestRoute(inputMint, outputMint, amount);
