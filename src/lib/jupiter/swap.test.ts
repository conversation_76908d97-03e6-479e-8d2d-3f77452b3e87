import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { jupiterSwapManager } from './swap';
import { jupiterAPI } from './api';
import { createMockKeypair, createMockTransaction, createMockConnection } from '@/test/setup';

// Mock Jupiter API
vi.mock('./api', () => ({
  jupiterAPI: {
    getQuote: vi.fn(),
    getSwapTransaction: vi.fn(),
    deserializeTransaction: vi.fn(),
    getTokens: vi.fn(),
    getPrice: vi.fn(),
    validateRoute: vi.fn(),
  },
}));

// Mock Solana connection
vi.mock('@/lib/solana/connection', () => ({
  getConnection: vi.fn(() => createMockConnection()),
}));

describe('JupiterSwapManager', () => {
  const mockKeypair = createMockKeypair();
  const mockTransaction = createMockTransaction();

  const mockQuoteResponse = {
    inputMint: 'So11111111111111111111111111111111111111112',
    outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    inAmount: '1000000000',
    outAmount: '1000000',
    otherAmountThreshold: '950000',
    swapMode: 'ExactIn',
    slippageBps: 100,
    platformFee: null,
    priceImpactPct: '0.1',
    routePlan: [],
  };

  const mockSwapParams = {
    inputMint: 'So11111111111111111111111111111111111111112',
    outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    amount: 1,
    slippageBps: 100,
    userKeypair: mockKeypair,
    priorityFee: 5000,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getSwapQuote', () => {
    it('should get swap quote successfully', async () => {
      (jupiterAPI.getQuote as any).mockResolvedValue(mockQuoteResponse);

      const result = await jupiterSwapManager.getSwapQuote(mockSwapParams);

      expect(result.success).toBe(true);
      expect(result.inputAmount).toBe(1);
      expect(result.outputAmount).toBe(0.001); // 1000000 / 1e9
      expect(result.priceImpact).toBe(0.1);
      expect(jupiterAPI.getQuote).toHaveBeenCalledWith({
        inputMint: mockSwapParams.inputMint,
        outputMint: mockSwapParams.outputMint,
        amount: '1000000000', // 1 * 1e9
        slippageBps: mockSwapParams.slippageBps,
      });
    });

    it('should handle quote errors', async () => {
      (jupiterAPI.getQuote as any).mockRejectedValue(new Error('Quote failed'));

      const result = await jupiterSwapManager.getSwapQuote(mockSwapParams);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Quote failed');
    });

    it('should validate swap parameters', async () => {
      const invalidParams = {
        ...mockSwapParams,
        inputMint: '',
      };

      const result = await jupiterSwapManager.getSwapQuote(invalidParams);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Input and output mints are required');
    });

    it('should reject same input and output mints', async () => {
      const invalidParams = {
        ...mockSwapParams,
        outputMint: mockSwapParams.inputMint,
      };

      const result = await jupiterSwapManager.getSwapQuote(invalidParams);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Input and output mints cannot be the same');
    });

    it('should reject invalid amounts', async () => {
      const invalidParams = {
        ...mockSwapParams,
        amount: 0,
      };

      const result = await jupiterSwapManager.getSwapQuote(invalidParams);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Amount must be greater than 0');
    });
  });

  describe('executeSwap', () => {
    const mockSwapResponse = {
      swapTransaction: 'base64-encoded-transaction',
      lastValidBlockHeight: 123456789,
    };

    beforeEach(() => {
      (jupiterAPI.getQuote as any).mockResolvedValue(mockQuoteResponse);
      (jupiterAPI.getSwapTransaction as any).mockResolvedValue(mockSwapResponse);
      (jupiterAPI.deserializeTransaction as any).mockReturnValue(mockTransaction);
    });

    it('should execute swap successfully', async () => {
      const mockConnection = createMockConnection();
      mockConnection.getLatestBlockhash.mockResolvedValue({
        blockhash: 'test-blockhash',
        lastValidBlockHeight: 123456789,
      });
      mockConnection.sendRawTransaction.mockResolvedValue('test-signature');
      mockConnection.confirmTransaction.mockResolvedValue({
        value: { err: null },
      });

      const result = await jupiterSwapManager.executeSwap(mockSwapParams);

      expect(result.success).toBe(true);
      expect(result.signature).toBe('test-signature');
      expect(result.inputAmount).toBe(1);
      expect(result.outputAmount).toBe(0.001);
      expect(result.attempts).toBe(1);
    });

    it('should retry on failure', async () => {
      const mockConnection = createMockConnection();
      mockConnection.getLatestBlockhash.mockResolvedValue({
        blockhash: 'test-blockhash',
        lastValidBlockHeight: 123456789,
      });
      
      // First attempt fails, second succeeds
      mockConnection.sendRawTransaction
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce('test-signature');
      
      mockConnection.confirmTransaction.mockResolvedValue({
        value: { err: null },
      });

      const result = await jupiterSwapManager.executeSwap({
        ...mockSwapParams,
        maxRetries: 2,
      });

      expect(result.success).toBe(true);
      expect(result.attempts).toBe(2);
      expect(mockConnection.sendRawTransaction).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      const mockConnection = createMockConnection();
      mockConnection.getLatestBlockhash.mockResolvedValue({
        blockhash: 'test-blockhash',
        lastValidBlockHeight: 123456789,
      });
      mockConnection.sendRawTransaction.mockRejectedValue(new Error('Persistent error'));

      const result = await jupiterSwapManager.executeSwap({
        ...mockSwapParams,
        maxRetries: 2,
      });

      expect(result.success).toBe(false);
      expect(result.attempts).toBe(2);
      expect(result.error).toContain('Persistent error');
    });

    it('should handle transaction confirmation errors', async () => {
      const mockConnection = createMockConnection();
      mockConnection.getLatestBlockhash.mockResolvedValue({
        blockhash: 'test-blockhash',
        lastValidBlockHeight: 123456789,
      });
      mockConnection.sendRawTransaction.mockResolvedValue('test-signature');
      mockConnection.confirmTransaction.mockResolvedValue({
        value: { err: { InstructionError: [0, { Custom: 6000 }] } },
      });

      const result = await jupiterSwapManager.executeSwap(mockSwapParams);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Slippage tolerance exceeded');
    });

    it('should reject high price impact', async () => {
      const highImpactQuote = {
        ...mockQuoteResponse,
        priceImpactPct: '15', // 15% impact
      };
      (jupiterAPI.getQuote as any).mockResolvedValue(highImpactQuote);

      const result = await jupiterSwapManager.executeSwap(mockSwapParams);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Price impact too high');
    });
  });

  describe('simulateSwap', () => {
    it('should simulate swap without executing', async () => {
      (jupiterAPI.getQuote as any).mockResolvedValue(mockQuoteResponse);

      const result = await jupiterSwapManager.simulateSwap(mockSwapParams);

      expect(result.success).toBe(true);
      expect(result.quote).toBeDefined();
      expect(jupiterAPI.getQuote).toHaveBeenCalled();
      expect(jupiterAPI.getSwapTransaction).not.toHaveBeenCalled();
    });
  });

  describe('validateSwapParams', () => {
    it('should validate correct parameters', () => {
      const result = jupiterSwapManager.validateSwapParams(mockSwapParams);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should catch missing mints', () => {
      const invalidParams = {
        ...mockSwapParams,
        inputMint: '',
        outputMint: '',
      };

      const result = jupiterSwapManager.validateSwapParams(invalidParams);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Input and output mints are required');
    });

    it('should catch same input/output mints', () => {
      const invalidParams = {
        ...mockSwapParams,
        outputMint: mockSwapParams.inputMint,
      };

      const result = jupiterSwapManager.validateSwapParams(invalidParams);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Input and output mints cannot be the same');
    });

    it('should catch invalid amounts', () => {
      const invalidParams = {
        ...mockSwapParams,
        amount: -1,
      };

      const result = jupiterSwapManager.validateSwapParams(invalidParams);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Amount must be greater than 0');
    });

    it('should catch invalid slippage', () => {
      const invalidParams = {
        ...mockSwapParams,
        slippageBps: 6000, // 60% slippage
      };

      const result = jupiterSwapManager.validateSwapParams(invalidParams);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Slippage must be between 0 and 50%');
    });

    it('should catch missing keypair', () => {
      const invalidParams = {
        ...mockSwapParams,
        userKeypair: undefined as any,
      };

      const result = jupiterSwapManager.validateSwapParams(invalidParams);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('User keypair is required');
    });
  });

  describe('estimateTransactionFee', () => {
    it('should estimate transaction fee', async () => {
      (jupiterAPI.getQuote as any).mockResolvedValue({
        ...mockQuoteResponse,
        routePlan: [{ swapInfo: {} }, { swapInfo: {} }], // 2 hops
      });

      const fee = await jupiterSwapManager.estimateTransactionFee(mockSwapParams);

      expect(fee).toBeGreaterThan(5000); // Base fee + priority fee + complexity
      expect(fee).toBeLessThan(20000); // Reasonable upper bound
    });

    it('should return default fee on error', async () => {
      (jupiterAPI.getQuote as any).mockRejectedValue(new Error('Quote failed'));

      const fee = await jupiterSwapManager.estimateTransactionFee(mockSwapParams);

      expect(fee).toBe(10000); // Default estimate
    });
  });
});
