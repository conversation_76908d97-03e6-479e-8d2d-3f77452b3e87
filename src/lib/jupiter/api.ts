import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { PublicKey, VersionedTransaction } from '@solana/web3.js';
import { config } from '@/config';

// Jupiter API interfaces
export interface JupiterQuoteRequest {
  inputMint: string;
  outputMint: string;
  amount: string;
  slippageBps?: number;
  swapMode?: 'ExactIn' | 'ExactOut';
  dexes?: string[];
  excludeDexes?: string[];
  restrictIntermediateTokens?: boolean;
  onlyDirectRoutes?: boolean;
  asLegacyTransaction?: boolean;
  platformFeeBps?: number;
  maxAccounts?: number;
}

export interface JupiterQuoteResponse {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: string;
  slippageBps: number;
  platformFee?: {
    amount: string;
    feeBps: number;
  };
  priceImpactPct: string;
  routePlan: RoutePlan[];
  contextSlot: number;
  timeTaken: number;
}

export interface RoutePlan {
  swapInfo: {
    ammKey: string;
    label: string;
    inputMint: string;
    outputMint: string;
    inAmount: string;
    outAmount: string;
    feeAmount: string;
    feeMint: string;
  };
  percent: number;
}

export interface JupiterSwapRequest {
  quoteResponse: JupiterQuoteResponse;
  userPublicKey: string;
  wrapAndUnwrapSol?: boolean;
  useSharedAccounts?: boolean;
  feeAccount?: string;
  trackingAccount?: string;
  computeUnitPriceMicroLamports?: number;
  prioritizationFeeLamports?: number;
  asLegacyTransaction?: boolean;
  useTokenLedger?: boolean;
  destinationTokenAccount?: string;
}

export interface JupiterSwapResponse {
  swapTransaction: string;
  lastValidBlockHeight: number;
  prioritizationFeeLamports?: number;
  computeUnitLimit?: number;
  prioritizationFeeDetails?: {
    computeUnitLimit: number;
    computeUnitPriceMicroLamports: number;
    prioritizationFeeLamports: number;
  };
}

export interface TokenInfo {
  address: string;
  chainId: number;
  decimals: number;
  name: string;
  symbol: string;
  logoURI?: string;
  tags?: string[];
  extensions?: {
    coingeckoId?: string;
  };
}

export interface PriceInfo {
  id: string;
  mintSymbol: string;
  vsToken: string;
  vsTokenSymbol: string;
  price: number;
  extraInfo?: {
    lastSwappedPrice?: {
      lastJupiterSellAt: number;
      lastJupiterSellPrice: number;
      lastJupiterBuyAt: number;
      lastJupiterBuyPrice: number;
    };
    quotedPrice?: {
      buyPrice: number;
      buyAt: number;
      sellPrice: number;
      sellAt: number;
    };
  };
}

export class JupiterAPI {
  private api: AxiosInstance;
  private priceApi: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: config.jupiter.apiUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.priceApi = axios.create({
      baseURL: config.jupiter.priceApiUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        if (config.development?.debugMode) {
          console.log('Jupiter API Request:', config);
        }
        return config;
      },
      (error) => {
        console.error('Jupiter API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        if (config.development.debugMode) {
          console.log('Jupiter API Response:', response.data);
        }
        return response;
      },
      (error) => {
        console.error('Jupiter API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get quote for token swap
   */
  async getQuote(params: JupiterQuoteRequest): Promise<JupiterQuoteResponse> {
    try {
      const response = await this.api.get('/quote', { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get Jupiter quote:', error);
      throw new Error(`Jupiter quote failed: ${error}`);
    }
  }

  /**
   * Get swap transaction
   */
  async getSwapTransaction(request: JupiterSwapRequest): Promise<JupiterSwapResponse> {
    try {
      const response = await this.api.post('/swap', request);
      return response.data;
    } catch (error) {
      console.error('Failed to get Jupiter swap transaction:', error);
      throw new Error(`Jupiter swap transaction failed: ${error}`);
    }
  }

  /**
   * Get all supported tokens
   */
  async getTokens(): Promise<TokenInfo[]> {
    try {
      const response = await this.api.get('/tokens');
      return response.data;
    } catch (error) {
      console.error('Failed to get Jupiter tokens:', error);
      throw new Error(`Jupiter tokens fetch failed: ${error}`);
    }
  }

  /**
   * Get token price
   */
  async getPrice(tokenMint: string, vsToken: string = 'SOL'): Promise<PriceInfo> {
    try {
      const response = await this.priceApi.get('/price', {
        params: {
          ids: tokenMint,
          vsToken,
        },
      });
      
      const priceData = response.data.data[tokenMint];
      if (!priceData) {
        throw new Error(`Price not found for token: ${tokenMint}`);
      }
      
      return priceData;
    } catch (error) {
      console.error('Failed to get token price:', error);
      throw new Error(`Price fetch failed: ${error}`);
    }
  }

  /**
   * Get multiple token prices
   */
  async getPrices(tokenMints: string[], vsToken: string = 'SOL'): Promise<Record<string, PriceInfo>> {
    try {
      const response = await this.priceApi.get('/price', {
        params: {
          ids: tokenMints.join(','),
          vsToken,
        },
      });
      
      return response.data.data;
    } catch (error) {
      console.error('Failed to get token prices:', error);
      throw new Error(`Prices fetch failed: ${error}`);
    }
  }

  /**
   * Get indexed route map
   */
  async getIndexedRouteMap(): Promise<Record<string, string[]>> {
    try {
      const response = await this.api.get('/indexed-route-map');
      return response.data;
    } catch (error) {
      console.error('Failed to get indexed route map:', error);
      throw new Error(`Route map fetch failed: ${error}`);
    }
  }

  /**
   * Validate if a route exists between two tokens
   */
  async validateRoute(inputMint: string, outputMint: string): Promise<boolean> {
    try {
      const routeMap = await this.getIndexedRouteMap();
      const routes = routeMap[inputMint];
      return routes ? routes.includes(outputMint) : false;
    } catch (error) {
      console.warn('Failed to validate route:', error);
      return false; // Assume route exists if validation fails
    }
  }

  /**
   * Get swap instructions (for advanced users)
   */
  async getSwapInstructions(request: JupiterSwapRequest): Promise<{
    tokenLedgerInstruction?: string;
    computeBudgetInstructions?: string[];
    setupInstructions?: string[];
    swapInstruction: string;
    cleanupInstruction?: string;
    addressLookupTableAddresses?: string[];
  }> {
    try {
      const response = await this.api.post('/swap-instructions', request);
      return response.data;
    } catch (error) {
      console.error('Failed to get swap instructions:', error);
      throw new Error(`Swap instructions failed: ${error}`);
    }
  }

  /**
   * Helper method to create a complete swap quote request
   */
  createQuoteRequest(
    inputMint: string,
    outputMint: string,
    amount: string,
    options: Partial<JupiterQuoteRequest> = {}
  ): JupiterQuoteRequest {
    return {
      inputMint,
      outputMint,
      amount,
      slippageBps: options.slippageBps || config.trading.defaultSlippage * 100,
      swapMode: options.swapMode || 'ExactIn',
      onlyDirectRoutes: options.onlyDirectRoutes || false,
      asLegacyTransaction: options.asLegacyTransaction || false,
      maxAccounts: options.maxAccounts || 64,
      ...options,
    };
  }

  /**
   * Helper method to create a complete swap request
   */
  createSwapRequest(
    quoteResponse: JupiterQuoteResponse,
    userPublicKey: string,
    options: Partial<JupiterSwapRequest> = {}
  ): JupiterSwapRequest {
    return {
      quoteResponse,
      userPublicKey,
      wrapAndUnwrapSol: options.wrapAndUnwrapSol !== false, // Default to true
      useSharedAccounts: options.useSharedAccounts || true,
      computeUnitPriceMicroLamports: options.computeUnitPriceMicroLamports,
      prioritizationFeeLamports: options.prioritizationFeeLamports,
      asLegacyTransaction: options.asLegacyTransaction || false,
      ...options,
    };
  }

  /**
   * Deserialize transaction from base64 string
   */
  deserializeTransaction(transactionBase64: string): VersionedTransaction {
    try {
      const transactionBuffer = Buffer.from(transactionBase64, 'base64');
      return VersionedTransaction.deserialize(transactionBuffer);
    } catch (error) {
      console.error('Failed to deserialize transaction:', error);
      throw new Error(`Transaction deserialization failed: ${error}`);
    }
  }

  /**
   * Calculate price impact percentage
   */
  calculatePriceImpact(quote: JupiterQuoteResponse): number {
    return parseFloat(quote.priceImpactPct);
  }

  /**
   * Calculate minimum output amount considering slippage
   */
  calculateMinimumOutput(quote: JupiterQuoteResponse): string {
    const outputAmount = parseInt(quote.outAmount);
    const slippageMultiplier = (10000 - quote.slippageBps) / 10000;
    const minimumOutput = Math.floor(outputAmount * slippageMultiplier);
    return minimumOutput.toString();
  }

  /**
   * Format amount for display
   */
  formatAmount(amount: string, decimals: number): number {
    return parseInt(amount) / Math.pow(10, decimals);
  }

  /**
   * Parse amount for API
   */
  parseAmount(amount: number, decimals: number): string {
    return Math.floor(amount * Math.pow(10, decimals)).toString();
  }
}

// Export singleton instance
export const jupiterAPI = new JupiterAPI();

// Export convenience functions
export const getQuote = (params: JupiterQuoteRequest) => jupiterAPI.getQuote(params);
export const getSwapTransaction = (request: JupiterSwapRequest) => jupiterAPI.getSwapTransaction(request);
export const getTokenPrice = (tokenMint: string, vsToken?: string) => jupiterAPI.getPrice(tokenMint, vsToken);
export const getTokenPrices = (tokenMints: string[], vsToken?: string) => jupiterAPI.getPrices(tokenMints, vsToken);
export const validateRoute = (inputMint: string, outputMint: string) => jupiterAPI.validateRoute(inputMint, outputMint);
