/**
 * Advanced caching system for Swift Sniper Fi
 */

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  hits: number;
  size: number; // Approximate size in bytes
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in bytes
  defaultTTL: number; // Default TTL in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  enablePersistence: boolean; // Whether to persist to localStorage
  compressionThreshold: number; // Compress entries larger than this size
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  totalHits: number;
  totalMisses: number;
  oldestEntry: number;
  newestEntry: number;
}

class CacheManager {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private stats = {
    hits: 0,
    misses: 0,
  };
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 50 * 1024 * 1024, // 50MB default
      defaultTTL: 5 * 60 * 1000, // 5 minutes default
      cleanupInterval: 60 * 1000, // 1 minute cleanup
      enablePersistence: true,
      compressionThreshold: 1024, // 1KB
      ...config,
    };

    this.startCleanupTimer();
    this.loadFromPersistence();
  }

  /**
   * Get item from cache
   */
  get<T = any>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update hit count and stats
    entry.hits++;
    this.stats.hits++;

    return entry.data;
  }

  /**
   * Set item in cache
   */
  set<T = any>(key: string, data: T, ttl?: number): void {
    const entryTTL = ttl || this.config.defaultTTL;
    const size = this.calculateSize(data);

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: entryTTL,
      hits: 0,
      size,
    };

    // Check if we need to make space
    this.ensureSpace(size);

    // Compress large entries if needed
    if (size > this.config.compressionThreshold) {
      entry.data = this.compress(data);
    }

    this.cache.set(key, entry);
    this.persistToDisk();
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete item from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.persistToDisk();
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.persistToDisk();
  }

  /**
   * Get or set with a factory function
   */
  async getOrSet<T = any>(
    key: string,
    factory: () => Promise<T> | T,
    ttl?: number
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const data = await factory();
    this.set(key, data, ttl);
    return data;
  }

  /**
   * Get multiple keys at once
   */
  getMany<T = any>(keys: string[]): Record<string, T | null> {
    const result: Record<string, T | null> = {};
    for (const key of keys) {
      result[key] = this.get<T>(key);
    }
    return result;
  }

  /**
   * Set multiple key-value pairs at once
   */
  setMany<T = any>(entries: Record<string, T>, ttl?: number): void {
    for (const [key, value] of Object.entries(entries)) {
      this.set(key, value, ttl);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
    const timestamps = entries.map(entry => entry.timestamp);

    return {
      totalEntries: this.cache.size,
      totalSize,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      missRate: this.stats.misses / (this.stats.hits + this.stats.misses) || 0,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0,
    };
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache entries sorted by various criteria
   */
  getEntriesSorted(sortBy: 'timestamp' | 'hits' | 'size' = 'timestamp'): Array<{ key: string; entry: CacheEntry }> {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({ key, entry }));

    return entries.sort((a, b) => {
      switch (sortBy) {
        case 'timestamp':
          return b.entry.timestamp - a.entry.timestamp;
        case 'hits':
          return b.entry.hits - a.entry.hits;
        case 'size':
          return b.entry.size - a.entry.size;
        default:
          return 0;
      }
    });
  }

  /**
   * Cleanup expired entries
   */
  cleanup(): number {
    let cleaned = 0;
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.persistToDisk();
    }

    return cleaned;
  }

  /**
   * Check if entry is expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Calculate approximate size of data
   */
  private calculateSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      // Fallback estimation
      const str = typeof data === 'string' ? data : JSON.stringify(data);
      return str.length * 2; // Rough estimate for UTF-16
    }
  }

  /**
   * Ensure there's enough space for new entry
   */
  private ensureSpace(requiredSize: number): void {
    const currentSize = Array.from(this.cache.values()).reduce(
      (sum, entry) => sum + entry.size,
      0
    );

    if (currentSize + requiredSize <= this.config.maxSize) {
      return;
    }

    // Remove entries using LRU strategy
    const entries = this.getEntriesSorted('timestamp');
    let freedSpace = 0;

    for (const { key, entry } of entries) {
      this.cache.delete(key);
      freedSpace += entry.size;

      if (freedSpace >= requiredSize) {
        break;
      }
    }
  }

  /**
   * Compress data (simple JSON compression)
   */
  private compress<T>(data: T): T {
    // In a real implementation, you might use a compression library
    // For now, we'll just return the data as-is
    return data;
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      const cleaned = this.cleanup();
      if (cleaned > 0) {
        console.log(`Cache cleanup: removed ${cleaned} expired entries`);
      }
    }, this.config.cleanupInterval);
  }

  /**
   * Stop cleanup timer
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Load cache from localStorage
   */
  private loadFromPersistence(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return;
    }

    try {
      const stored = localStorage.getItem('cache_data');
      if (stored) {
        const data = JSON.parse(stored);
        for (const [key, entry] of Object.entries(data)) {
          if (!this.isExpired(entry as CacheEntry)) {
            this.cache.set(key, entry as CacheEntry);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load cache from persistence:', error);
    }
  }

  /**
   * Persist cache to localStorage
   */
  private persistToDisk(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return;
    }

    try {
      const data = Object.fromEntries(this.cache.entries());
      localStorage.setItem('cache_data', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to persist cache:', error);
    }
  }

  /**
   * Destroy cache manager
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}

// Create specialized cache instances
export const priceCache = new CacheManager({
  defaultTTL: 30 * 1000, // 30 seconds for prices
  maxSize: 10 * 1024 * 1024, // 10MB
});

export const tokenCache = new CacheManager({
  defaultTTL: 5 * 60 * 1000, // 5 minutes for token data
  maxSize: 20 * 1024 * 1024, // 20MB
});

export const userCache = new CacheManager({
  defaultTTL: 15 * 60 * 1000, // 15 minutes for user data
  maxSize: 5 * 1024 * 1024, // 5MB
});

export const apiCache = new CacheManager({
  defaultTTL: 2 * 60 * 1000, // 2 minutes for API responses
  maxSize: 15 * 1024 * 1024, // 15MB
});

// Export the main cache manager class
export { CacheManager };
