/**
 * Analytics and monitoring system for Swift Sniper Fi
 */

export interface AnalyticsEvent {
  name: string;
  properties: Record<string, any>;
  timestamp: number;
  userId?: string;
  sessionId: string;
  category: 'user' | 'trading' | 'system' | 'error' | 'performance';
}

export interface UserMetrics {
  totalTrades: number;
  successfulTrades: number;
  totalVolume: number;
  totalPnL: number;
  averageHoldTime: number;
  favoriteTokens: string[];
  lastActiveAt: number;
  sessionCount: number;
}

export interface TradingMetrics {
  totalTrades: number;
  successRate: number;
  totalVolume: number;
  averageTradeSize: number;
  topPerformingTokens: Array<{ mint: string; pnl: number; trades: number }>;
  hourlyVolume: Record<string, number>;
  dailyVolume: Record<string, number>;
}

export interface SystemMetrics {
  uptime: number;
  errorRate: number;
  averageResponseTime: number;
  activeUsers: number;
  totalUsers: number;
  memoryUsage: number;
  cpuUsage: number;
}

class AnalyticsManager {
  private events: AnalyticsEvent[] = [];
  private sessionId: string;
  private userId?: string;
  private isEnabled: boolean = true;
  private batchSize: number = 50;
  private flushInterval: number = 30000; // 30 seconds
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startFlushTimer();
    this.setupPageVisibilityHandling();
  }

  /**
   * Track an analytics event
   */
  track(name: string, properties: Record<string, any> = {}, category: AnalyticsEvent['category'] = 'user'): void {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      name,
      properties: {
        ...properties,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      category,
    };

    this.events.push(event);

    // Auto-flush if batch size reached
    if (this.events.length >= this.batchSize) {
      this.flush();
    }
  }

  /**
   * Track page view
   */
  trackPageView(page: string, properties: Record<string, any> = {}): void {
    this.track('page_view', {
      page,
      referrer: document.referrer,
      ...properties,
    }, 'user');
  }

  /**
   * Track user action
   */
  trackUserAction(action: string, properties: Record<string, any> = {}): void {
    this.track('user_action', {
      action,
      ...properties,
    }, 'user');
  }

  /**
   * Track trading event
   */
  trackTrade(type: 'buy' | 'sell', properties: Record<string, any> = {}): void {
    this.track('trade', {
      type,
      ...properties,
    }, 'trading');
  }

  /**
   * Track error
   */
  trackError(error: Error, context: string, properties: Record<string, any> = {}): void {
    this.track('error', {
      message: error.message,
      stack: error.stack,
      context,
      ...properties,
    }, 'error');
  }

  /**
   * Track performance metric
   */
  trackPerformance(metric: string, value: number, properties: Record<string, any> = {}): void {
    this.track('performance', {
      metric,
      value,
      ...properties,
    }, 'performance');
  }

  /**
   * Set user ID
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Clear user ID
   */
  clearUserId(): void {
    this.userId = undefined;
  }

  /**
   * Enable/disable analytics
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (!enabled) {
      this.events = [];
    }
  }

  /**
   * Flush events to server
   */
  async flush(): Promise<void> {
    if (this.events.length === 0) return;

    const eventsToSend = [...this.events];
    this.events = [];

    try {
      // In a real implementation, send to analytics service
      await this.sendEvents(eventsToSend);
      console.log(`Sent ${eventsToSend.length} analytics events`);
    } catch (error) {
      console.error('Failed to send analytics events:', error);
      // Re-add events to queue for retry
      this.events.unshift(...eventsToSend);
    }
  }

  /**
   * Get user metrics
   */
  getUserMetrics(): UserMetrics {
    const userEvents = this.events.filter(e => e.userId === this.userId);
    const tradeEvents = userEvents.filter(e => e.name === 'trade');
    
    return {
      totalTrades: tradeEvents.length,
      successfulTrades: tradeEvents.filter(e => e.properties.success).length,
      totalVolume: tradeEvents.reduce((sum, e) => sum + (e.properties.amount || 0), 0),
      totalPnL: tradeEvents.reduce((sum, e) => sum + (e.properties.pnl || 0), 0),
      averageHoldTime: this.calculateAverageHoldTime(tradeEvents),
      favoriteTokens: this.getFavoriteTokens(tradeEvents),
      lastActiveAt: Math.max(...userEvents.map(e => e.timestamp)),
      sessionCount: new Set(userEvents.map(e => e.sessionId)).size,
    };
  }

  /**
   * Get trading metrics
   */
  getTradingMetrics(): TradingMetrics {
    const tradeEvents = this.events.filter(e => e.name === 'trade');
    
    return {
      totalTrades: tradeEvents.length,
      successRate: tradeEvents.filter(e => e.properties.success).length / tradeEvents.length || 0,
      totalVolume: tradeEvents.reduce((sum, e) => sum + (e.properties.amount || 0), 0),
      averageTradeSize: tradeEvents.reduce((sum, e) => sum + (e.properties.amount || 0), 0) / tradeEvents.length || 0,
      topPerformingTokens: this.getTopPerformingTokens(tradeEvents),
      hourlyVolume: this.getHourlyVolume(tradeEvents),
      dailyVolume: this.getDailyVolume(tradeEvents),
    };
  }

  /**
   * Get system metrics
   */
  getSystemMetrics(): SystemMetrics {
    const errorEvents = this.events.filter(e => e.category === 'error');
    const performanceEvents = this.events.filter(e => e.category === 'performance');
    
    return {
      uptime: Date.now() - (this.events[0]?.timestamp || Date.now()),
      errorRate: errorEvents.length / this.events.length || 0,
      averageResponseTime: this.getAverageResponseTime(performanceEvents),
      activeUsers: new Set(this.events.map(e => e.userId).filter(Boolean)).size,
      totalUsers: new Set(this.events.map(e => e.userId).filter(Boolean)).size,
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: 0, // Would need additional monitoring
    };
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start flush timer
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  /**
   * Stop flush timer
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * Setup page visibility handling
   */
  private setupPageVisibilityHandling(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.flush(); // Flush events when page becomes hidden
      }
    });

    window.addEventListener('beforeunload', () => {
      this.flush(); // Flush events before page unload
    });
  }

  /**
   * Send events to analytics service
   */
  private async sendEvents(events: AnalyticsEvent[]): Promise<void> {
    // In a real implementation, this would send to your analytics service
    // For now, we'll just store in localStorage for debugging
    try {
      const stored = JSON.parse(localStorage.getItem('analytics_events') || '[]');
      stored.push(...events);
      
      // Keep only last 1000 events
      if (stored.length > 1000) {
        stored.splice(0, stored.length - 1000);
      }
      
      localStorage.setItem('analytics_events', JSON.stringify(stored));
    } catch (error) {
      console.warn('Failed to store analytics events:', error);
    }
  }

  /**
   * Calculate average hold time
   */
  private calculateAverageHoldTime(tradeEvents: AnalyticsEvent[]): number {
    // This would require more sophisticated tracking of buy/sell pairs
    return 0;
  }

  /**
   * Get favorite tokens
   */
  private getFavoriteTokens(tradeEvents: AnalyticsEvent[]): string[] {
    const tokenCounts: Record<string, number> = {};
    
    for (const event of tradeEvents) {
      const token = event.properties.token;
      if (token) {
        tokenCounts[token] = (tokenCounts[token] || 0) + 1;
      }
    }
    
    return Object.entries(tokenCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([token]) => token);
  }

  /**
   * Get top performing tokens
   */
  private getTopPerformingTokens(tradeEvents: AnalyticsEvent[]): Array<{ mint: string; pnl: number; trades: number }> {
    const tokenStats: Record<string, { pnl: number; trades: number }> = {};
    
    for (const event of tradeEvents) {
      const token = event.properties.token;
      const pnl = event.properties.pnl || 0;
      
      if (token) {
        if (!tokenStats[token]) {
          tokenStats[token] = { pnl: 0, trades: 0 };
        }
        tokenStats[token].pnl += pnl;
        tokenStats[token].trades += 1;
      }
    }
    
    return Object.entries(tokenStats)
      .map(([mint, stats]) => ({ mint, ...stats }))
      .sort((a, b) => b.pnl - a.pnl)
      .slice(0, 10);
  }

  /**
   * Get hourly volume
   */
  private getHourlyVolume(tradeEvents: AnalyticsEvent[]): Record<string, number> {
    const hourlyVolume: Record<string, number> = {};
    
    for (const event of tradeEvents) {
      const hour = new Date(event.timestamp).toISOString().slice(0, 13);
      const amount = event.properties.amount || 0;
      hourlyVolume[hour] = (hourlyVolume[hour] || 0) + amount;
    }
    
    return hourlyVolume;
  }

  /**
   * Get daily volume
   */
  private getDailyVolume(tradeEvents: AnalyticsEvent[]): Record<string, number> {
    const dailyVolume: Record<string, number> = {};
    
    for (const event of tradeEvents) {
      const day = new Date(event.timestamp).toISOString().slice(0, 10);
      const amount = event.properties.amount || 0;
      dailyVolume[day] = (dailyVolume[day] || 0) + amount;
    }
    
    return dailyVolume;
  }

  /**
   * Get average response time
   */
  private getAverageResponseTime(performanceEvents: AnalyticsEvent[]): number {
    const responseTimes = performanceEvents
      .filter(e => e.properties.metric === 'response_time')
      .map(e => e.properties.value);
    
    return responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Destroy analytics manager
   */
  destroy(): void {
    this.stopFlushTimer();
    this.flush();
  }
}

// Export singleton instance
export const analytics = new AnalyticsManager();

// Convenience functions
export const trackPageView = (page: string, properties?: Record<string, any>) => 
  analytics.trackPageView(page, properties);

export const trackUserAction = (action: string, properties?: Record<string, any>) => 
  analytics.trackUserAction(action, properties);

export const trackTrade = (type: 'buy' | 'sell', properties?: Record<string, any>) => 
  analytics.trackTrade(type, properties);

export const trackError = (error: Error, context: string, properties?: Record<string, any>) => 
  analytics.trackError(error, context, properties);

export const trackPerformance = (metric: string, value: number, properties?: Record<string, any>) => 
  analytics.trackPerformance(metric, value, properties);

export { AnalyticsManager };
