import { Connection, PublicKey, AccountInfo, ParsedAccountData } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getMint, Mint } from '@solana/spl-token';
import { getConnection } from '../solana/connection';
import { priceFeedManager } from '../price/feeds';
import config from '@/config';

export interface DetectedToken {
  mint: string;
  name?: string;
  symbol?: string;
  decimals: number;
  supply: number;
  mintAuthority?: string;
  freezeAuthority?: string;
  isInitialized: boolean;
  detectedAt: number;
  liquidityPools: LiquidityPool[];
  safetyScore: number;
  riskFactors: string[];
  metadata?: TokenMetadata;
}

export interface LiquidityPool {
  address: string;
  dex: string;
  baseToken: string;
  quoteToken: string;
  liquidity: number;
  volume24h?: number;
  createdAt?: number;
}

export interface TokenMetadata {
  name?: string;
  symbol?: string;
  description?: string;
  image?: string;
  website?: string;
  twitter?: string;
  telegram?: string;
  discord?: string;
}

export interface DetectionFilters {
  minLiquidity?: number;
  maxSupply?: number;
  minHolders?: number;
  excludeKnownTokens?: boolean;
  requireMetadata?: boolean;
  maxAge?: number; // in minutes
}

export class TokenDetectionEngine {
  private connection = getConnection();
  private detectedTokens: Map<string, DetectedToken> = new Map();
  private subscriptions: Map<string, number> = new Map();
  private isMonitoring = false;
  private statusInterval: NodeJS.Timeout | null = null;
  private activityCount = 0;
  private poolsDetected = 0;

  /**
   * Start monitoring for new tokens with real blockchain event monitoring
   */
  async startMonitoring(filters: DetectionFilters = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Token monitoring is already active');
      return;
    }

    this.isMonitoring = true;
    console.log('🔍 Starting real-time token detection monitoring...');
    console.log('📊 DETECTION STATUS:');
    console.log('   📈 Total activity: 0');
    console.log('   🏊 New pools detected: 0');
    console.log('   🪙 Unique tokens found: 0');

    try {
      // Start multiple monitoring strategies
      await Promise.all([
        this.monitorNewTokenAccounts(filters),
        this.monitorProgramLogs(filters),
        this.monitorNewMarkets(filters),
        this.scanRecentBlocks(filters),
      ]);

      console.log('✅ All token monitoring systems active');

      // Start periodic status updates
      this.startStatusReporting();
    } catch (error) {
      console.error('❌ Failed to start token monitoring:', error);
      this.isMonitoring = false;
      throw error;
    }
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false;

    // Stop status reporting
    if (this.statusInterval) {
      clearInterval(this.statusInterval);
      this.statusInterval = null;
    }

    // Remove all subscriptions
    for (const [subscriptionId, id] of this.subscriptions.entries()) {
      this.connection.removeAccountChangeListener(id);
    }
    this.subscriptions.clear();

    console.log('⏹️ Token detection monitoring stopped');
  }

  /**
   * Start periodic status reporting
   */
  private startStatusReporting(): void {
    if (this.statusInterval) {
      clearInterval(this.statusInterval);
    }

    this.statusInterval = setInterval(() => {
      const totalActivity = this.activityCount;
      const poolsDetected = this.poolsDetected;
      const uniqueTokens = this.detectedTokens.size;

      console.log('📊 DETECTION STATUS:');
      console.log(`   📈 Total activity: ${totalActivity}`);
      console.log(`   🏊 New pools detected: ${poolsDetected}`);
      console.log(`   🪙 Unique tokens found: ${uniqueTokens}`);
    }, 30000); // Every 30 seconds
  }

  /**
   * Monitor new token accounts with enhanced filtering
   */
  private async monitorNewTokenAccounts(filters: DetectionFilters): Promise<void> {
    try {
      // Subscribe to mint account changes (new token creation)
      const mintSubscriptionId = this.connection.onProgramAccountChange(
        TOKEN_PROGRAM_ID,
        async (accountInfo, context) => {
          try {
            if (accountInfo && context) {
              await this.handleNewMintAccount(accountInfo, context.slot, filters);
            }
          } catch (error) {
            console.warn('Error in mint account subscription:', error);
          }
        },
        'confirmed',
        [
          {
            dataSize: 82, // Mint account size
          }
        ]
      );

      this.subscriptions.set('mint-accounts', mintSubscriptionId);

      // Subscribe to token account changes (new token accounts)
      const tokenSubscriptionId = this.connection.onProgramAccountChange(
        TOKEN_PROGRAM_ID,
        async (accountInfo, context) => {
          try {
            if (accountInfo && context) {
              await this.handleNewTokenAccount(accountInfo, context.slot, filters);
            }
          } catch (error) {
            console.warn('Error in token account subscription:', error);
          }
        },
        'confirmed',
        [
          {
            dataSize: 165, // Token account size
          }
        ]
      );

      this.subscriptions.set('token-accounts', tokenSubscriptionId);
      console.log('✅ Subscribed to token program account changes');
    } catch (error) {
      console.error('Failed to monitor new token accounts:', error);
    }
  }

  /**
   * Monitor program logs for token creation events
   */
  private async monitorProgramLogs(filters: DetectionFilters): Promise<void> {
    try {
      // Subscribe to logs for token program
      const logSubscriptionId = this.connection.onLogs(
        TOKEN_PROGRAM_ID,
        async (logs, context) => {
          await this.handleProgramLogs(logs, context.slot, filters);
        },
        'confirmed'
      );

      this.subscriptions.set('program-logs', logSubscriptionId);
      console.log('✅ Subscribed to token program logs');
    } catch (error) {
      console.error('Failed to monitor program logs:', error);
    }
  }

  /**
   * Monitor new markets and liquidity pools
   */
  private async monitorNewMarkets(filters: DetectionFilters): Promise<void> {
    try {
      // Monitor Raydium program for new pools
      const raydiumProgramId = new (await import('@solana/web3.js')).PublicKey('675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8');

      const raydiumSubscriptionId = this.connection.onProgramAccountChange(
        raydiumProgramId,
        async (accountInfo, context) => {
          await this.handleNewMarket(accountInfo, context.slot, filters, 'raydium');
        },
        'confirmed'
      );

      this.subscriptions.set('raydium-markets', raydiumSubscriptionId);

      // Monitor Orca program for new pools
      const orcaProgramId = new (await import('@solana/web3.js')).PublicKey('9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP');

      const orcaSubscriptionId = this.connection.onProgramAccountChange(
        orcaProgramId,
        async (accountInfo, context) => {
          await this.handleNewMarket(accountInfo, context.slot, filters, 'orca');
        },
        'confirmed'
      );

      this.subscriptions.set('orca-markets', orcaSubscriptionId);
      console.log('✅ Subscribed to DEX market changes');
    } catch (error) {
      console.error('Failed to monitor new markets:', error);
    }
  }

  /**
   * Scan recent blocks for new tokens
   */
  private async scanRecentBlocks(filters: DetectionFilters): Promise<void> {
    try {
      let lastProcessedSlot = await this.connection.getSlot('confirmed');

      const scanInterval = setInterval(async () => {
        if (!this.isMonitoring) {
          clearInterval(scanInterval);
          return;
        }

        try {
          const currentSlot = await this.connection.getSlot('confirmed');

          // Process blocks since last scan
          for (let slot = lastProcessedSlot + 1; slot <= currentSlot; slot++) {
            await this.processBlockForTokens(slot, filters);
          }

          lastProcessedSlot = currentSlot;
        } catch (error) {
          console.warn('Block scanning error:', error);
        }
      }, 5000); // Scan every 5 seconds

      this.subscriptions.set('block-scanner', scanInterval as any);
      console.log('✅ Started block scanning for new tokens');
    } catch (error) {
      console.error('Failed to start block scanning:', error);
    }
  }

  /**
   * Handle new mint account creation
   */
  private async handleNewMintAccount(
    accountInfo: AccountInfo<Buffer>,
    slot: number,
    filters: DetectionFilters
  ): Promise<void> {
    try {
      // Check if accountInfo and data exist and are valid
      if (!accountInfo || !accountInfo.data || !Buffer.isBuffer(accountInfo.data)) {
        // Don't log this as it happens frequently with invalid data
        return;
      }

      // Parse mint account data
      if (accountInfo.data.length === 82) {
        const mintData = this.parseMintAccountData(accountInfo.data);
        if (mintData) {
          console.log(`🆕 NEW MINT DETECTED: ${mintData.mint} (Supply: ${mintData.supply})`);
          await this.analyzeNewToken(mintData.mint, filters);
        }
      }
    } catch (error) {
      // Only log actual errors, not invalid data
      if (error.message && !error.message.includes('Invalid')) {
        console.warn('Failed to handle new mint account:', error);
      }
    }
  }

  /**
   * Handle new token account detection
   */
  private async handleNewTokenAccount(
    accountInfo: AccountInfo<Buffer>,
    slot: number,
    filters: DetectionFilters
  ): Promise<void> {
    try {
      // Check if accountInfo and data exist and are valid
      if (!accountInfo || !accountInfo.data || !Buffer.isBuffer(accountInfo.data)) {
        // Don't log this as it happens frequently with invalid data
        return;
      }

      // Parse token account data
      if (accountInfo.data.length === 165) {
        const tokenData = this.parseTokenAccountData(accountInfo.data);
        if (tokenData && tokenData.amount > 0) {
          // This is a new token account with tokens
          console.log(`💰 NEW TOKEN ACCOUNT: ${tokenData.mint} (Amount: ${tokenData.amount})`);

          // Check if we've seen this mint before
          if (!this.detectedTokens.has(tokenData.mint)) {
            await this.analyzeNewToken(tokenData.mint, filters);
          }
        }
      }
    } catch (error) {
      // Only log actual errors, not invalid data
      if (error.message && !error.message.includes('Invalid')) {
        console.warn('Failed to handle new token account:', error);
      }
    }
  }

  /**
   * Handle program logs for token events
   */
  private async handleProgramLogs(
    logs: any,
    slot: number,
    filters: DetectionFilters
  ): Promise<void> {
    try {
      // Parse logs for token creation events
      if (logs.logs) {
        for (const log of logs.logs) {
          if (log.includes('InitializeMint') || log.includes('MintTo')) {
            // Extract mint address from log if possible
            const mintMatch = log.match(/([1-9A-HJ-NP-Za-km-z]{32,44})/);
            if (mintMatch) {
              const potentialMint = mintMatch[1];
              console.log(`📋 Log event for potential mint: ${potentialMint}`);

              // Verify it's actually a mint and analyze
              try {
                await this.analyzeNewToken(potentialMint, filters);
              } catch (error) {
                // Not a valid mint, ignore
              }
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to handle program logs:', error);
    }
  }

  /**
   * Handle new market/pool creation
   */
  private async handleNewMarket(
    accountInfo: AccountInfo<Buffer>,
    slot: number,
    filters: DetectionFilters,
    dex: string
  ): Promise<void> {
    try {
      // Track activity
      this.activityCount++;

      // Only process accounts with actual data (new pool creation)
      if (!accountInfo.data || accountInfo.data.length === 0) {
        return; // Skip empty accounts (deletions/updates)
      }

      // Check if this looks like a new pool creation (specific data size)
      const isNewPool = this.isNewRaydiumPool(accountInfo.data);
      if (!isNewPool) {
        return; // Skip existing pool updates
      }

      // Track pool detection
      this.poolsDetected++;
      console.log(`🏪 NEW ${dex.toUpperCase()} POOL CREATED at slot ${slot} (${accountInfo.data.length} bytes)`);

      // Extract token mints from the NEW pool data
      const tokenMints = this.extractTokenMintsFromMarket(accountInfo.data, dex);

      if (tokenMints.length > 0) {
        console.log(`🎯 Found ${tokenMints.length} token mints in new ${dex} pool`);

        for (const mint of tokenMints) {
          if (!this.detectedTokens.has(mint)) {
            console.log(`🪙 NEW TOKEN from new ${dex} pool: ${mint}`);
            await this.analyzeNewToken(mint, filters);
          }
        }
      } else {
        console.log(`⚠️ No token mints found in ${dex} pool data`);
      }
    } catch (error) {
      console.warn(`Failed to handle new ${dex} market:`, error);
    }
  }

  /**
   * Check if Raydium account data represents a new pool creation
   */
  private isNewRaydiumPool(data: Buffer): boolean {
    try {
      // Raydium V4 AMM pools have a specific size of 752 bytes when fully initialized
      if (data.length !== 752) {
        return false; // Not a V4 AMM pool
      }

      // Check if the pool has valid data structure
      // A new pool should have non-zero data in key positions
      try {
        // Check if baseMint and quoteMint positions have valid data
        const baseMintOffset = 73;
        const quoteMintOffset = 105;

        if (data.length >= baseMintOffset + 32 && data.length >= quoteMintOffset + 32) {
          const baseMint = data.slice(baseMintOffset, baseMintOffset + 32);
          const quoteMint = data.slice(quoteMintOffset, quoteMintOffset + 32);

          // Check if both mints are not all zeros (which would indicate uninitialized)
          const hasValidBaseMint = !baseMint.every(byte => byte === 0);
          const hasValidQuoteMint = !quoteMint.every(byte => byte === 0);

          if (hasValidBaseMint && hasValidQuoteMint) {
            console.log(`🔍 Valid Raydium V4 pool detected (${data.length} bytes)`);
            return true;
          }
        }
      } catch (error) {
        // If we can't parse the structure, it's probably not a valid pool
        return false;
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Process a block for new tokens
   */
  private async processBlockForTokens(slot: number, filters: DetectionFilters): Promise<void> {
    try {
      // Use a more reliable method to get block data
      const block = await this.connection.getBlock(slot, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0,
        transactionDetails: 'full',
        rewards: false,
      });

      // Better error handling for missing block data
      if (!block) {
        // Don't log missing blocks as they're common
        return;
      }

      if (!block.transactions || !Array.isArray(block.transactions)) {
        // Don't log missing transaction data as it's common
        return;
      }

      let processedTransactions = 0;
      let raydiumTransactions = 0;

      // Process transactions in the block
      for (const transaction of block.transactions) {
        try {
          if (transaction.meta?.err) continue;

          // Check if this transaction involves Raydium
          const instructions = transaction.transaction?.message?.instructions;
          if (!instructions || !Array.isArray(instructions)) continue;

          let hasRaydiumInstruction = false;
          for (const instruction of instructions) {
            if (instruction.programId.equals(RAYDIUM_PROGRAM_ID)) {
              hasRaydiumInstruction = true;
              raydiumTransactions++;
              break;
            }
          }

          if (hasRaydiumInstruction) {
            processedTransactions++;
            await this.analyzeTransactionForTokens(transaction, filters);
          }
        } catch (error) {
          // Skip failed transaction analysis - don't log every failure
        }
      }

      if (raydiumTransactions > 0) {
        console.log(`🔍 Block ${slot}: Found ${raydiumTransactions} Raydium transactions, processed ${processedTransactions}`);
      }
    } catch (error) {
      // Only log significant errors, not missing block data
      if (error.message &&
          !error.message.includes('undefined') &&
          !error.message.includes('Expected an array') &&
          !error.message.includes('failed to get confirmed block')) {
        console.warn(`Failed to process block ${slot}:`, error.message);
      }
    }
  }

  /**
   * Analyze a newly detected token with comprehensive blockchain analysis
   */
  async analyzeNewToken(mintAddress: string, filters: DetectionFilters = {}): Promise<DetectedToken | null> {
    try {
      // Skip if already analyzed
      if (this.detectedTokens.has(mintAddress)) {
        return this.detectedTokens.get(mintAddress)!;
      }

      console.log(`🔍 Analyzing new token: ${mintAddress}`);

      // Get comprehensive blockchain data
      const blockchainData = await this.analyzeTokenWithBlockchainData(mintAddress);

      if (!blockchainData.mint) {
        console.warn(`Failed to get blockchain data for ${mintAddress}`);
        return null;
      }

      // Create detected token with blockchain data
      const detectedToken: DetectedToken = {
        mint: mintAddress,
        decimals: blockchainData.decimals || 9,
        supply: blockchainData.supply || 0,
        mintAuthority: blockchainData.mintAuthority,
        freezeAuthority: blockchainData.freezeAuthority,
        isInitialized: blockchainData.isInitialized || false,
        detectedAt: Date.now(),
        liquidityPools: blockchainData.liquidityPools || [],
        safetyScore: blockchainData.safetyScore || 0,
        riskFactors: blockchainData.riskFactors || [],
      };

      // Apply filters early to avoid unnecessary processing
      if (!this.passesFilters(detectedToken, filters)) {
        console.log(`Token ${mintAddress} filtered out`);
        return null;
      }

      // Get metadata (this might be slow, so do it after filtering)
      try {
        detectedToken.metadata = await this.getTokenMetadata(mintAddress);
        if (detectedToken.metadata) {
          detectedToken.name = detectedToken.metadata.name;
          detectedToken.symbol = detectedToken.metadata.symbol;
        }
      } catch (error) {
        console.warn(`Failed to get metadata for ${mintAddress}:`, error);
      }

      // Find liquidity pools across DEXes
      try {
        detectedToken.liquidityPools = await this.findLiquidityPools(mintAddress);
      } catch (error) {
        console.warn(`Failed to find liquidity pools for ${mintAddress}:`, error);
      }

      // Recalculate safety score with all data
      detectedToken.safetyScore = await this.calculateSafetyScore(detectedToken);

      // Final filter check with complete data
      if (!this.passesFilters(detectedToken, filters)) {
        console.log(`Token ${mintAddress} filtered out after full analysis`);
        return null;
      }

      // Store detected token
      this.detectedTokens.set(mintAddress, detectedToken);

      console.log(`✅ New token analyzed: ${detectedToken.symbol || mintAddress} (Safety: ${detectedToken.safetyScore}/100, Pools: ${detectedToken.liquidityPools?.length || 0})`);

      return detectedToken;
    } catch (error) {
      console.error(`❌ Failed to analyze token ${mintAddress}:`, error);
      return null;
    }
  }

  /**
   * Check if token passes filters
   */
  private passesFilters(token: DetectedToken, filters: DetectionFilters): boolean {
    // Max supply filter
    if (filters.maxSupply && token.supply > filters.maxSupply) {
      return false;
    }

    // Age filter
    if (filters.maxAge) {
      const ageMinutes = (Date.now() - token.detectedAt) / (1000 * 60);
      if (ageMinutes > filters.maxAge) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get token metadata
   */
  private async getTokenMetadata(mintAddress: string): Promise<TokenMetadata | undefined> {
    try {
      // This would typically use the Metaplex Token Metadata program
      // For now, return undefined as we'd need to implement the full metadata parsing
      return undefined;
    } catch (error) {
      console.warn(`Failed to get metadata for ${mintAddress}:`, error);
      return undefined;
    }
  }

  /**
   * Find liquidity pools for a token
   */
  private async findLiquidityPools(mintAddress: string): Promise<LiquidityPool[]> {
    const pools: LiquidityPool[] = [];

    try {
      // This would search for liquidity pools across different DEXes
      // For now, return empty array as this requires specific DEX program knowledge
      return pools;
    } catch (error) {
      console.warn(`Failed to find liquidity pools for ${mintAddress}:`, error);
      return pools;
    }
  }

  /**
   * Calculate safety score for a token
   */
  private async calculateSafetyScore(token: DetectedToken): Promise<number> {
    let score = 100;
    const riskFactors: string[] = [];

    // Check mint authority
    if (token.mintAuthority) {
      score -= 20;
      riskFactors.push('Mint authority present - tokens can be minted');
    }

    // Check freeze authority
    if (token.freezeAuthority) {
      score -= 15;
      riskFactors.push('Freeze authority present - accounts can be frozen');
    }

    // Check supply
    if (token.supply > config.tokenDetection.maxSupply) {
      score -= 10;
      riskFactors.push('Very high token supply');
    }

    // Check liquidity
    const totalLiquidity = token.liquidityPools.reduce((sum, pool) => sum + pool.liquidity, 0);
    if (totalLiquidity < config.tokenDetection.minLiquiditySol) {
      score -= 25;
      riskFactors.push('Low liquidity');
    }

    // Check metadata
    if (!token.metadata || !token.metadata.name || !token.metadata.symbol) {
      score -= 10;
      riskFactors.push('Missing or incomplete metadata');
    }

    // Check for suspicious patterns
    if (token.symbol && /^[A-Z]{1,3}\d+$/.test(token.symbol)) {
      score -= 15;
      riskFactors.push('Suspicious symbol pattern');
    }

    token.riskFactors = riskFactors;
    return Math.max(0, score);
  }

  /**
   * Get all detected tokens
   */
  getDetectedTokens(filters?: Partial<DetectionFilters>): DetectedToken[] {
    let tokens = Array.from(this.detectedTokens.values());

    if (filters) {
      tokens = tokens.filter(token => {
        if (filters.minLiquidity) {
          const totalLiquidity = token.liquidityPools.reduce((sum, pool) => sum + pool.liquidity, 0);
          if (totalLiquidity < filters.minLiquidity) return false;
        }

        if (filters.maxSupply && token.supply > filters.maxSupply) {
          return false;
        }

        return true;
      });
    }

    return tokens.sort((a, b) => b.detectedAt - a.detectedAt);
  }

  /**
   * Get token by mint address
   */
  getToken(mintAddress: string): DetectedToken | undefined {
    return this.detectedTokens.get(mintAddress);
  }

  /**
   * Get top tokens by safety score
   */
  getTopSafeTokens(limit: number = 10): DetectedToken[] {
    return Array.from(this.detectedTokens.values())
      .sort((a, b) => b.safetyScore - a.safetyScore)
      .slice(0, limit);
  }

  /**
   * Get recently detected tokens
   */
  getRecentTokens(minutes: number = 60): DetectedToken[] {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return Array.from(this.detectedTokens.values())
      .filter(token => token.detectedAt > cutoff)
      .sort((a, b) => b.detectedAt - a.detectedAt);
  }

  /**
   * Search tokens by symbol or name
   */
  searchTokens(query: string): DetectedToken[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.detectedTokens.values())
      .filter(token => 
        token.symbol?.toLowerCase().includes(lowerQuery) ||
        token.name?.toLowerCase().includes(lowerQuery) ||
        token.mint.includes(query)
      );
  }

  /**
   * Get detection statistics
   */
  getStats(): {
    totalDetected: number;
    safeTokens: number;
    riskyTokens: number;
    recentDetections: number;
    averageSafetyScore: number;
  } {
    const tokens = Array.from(this.detectedTokens.values());
    const recentCutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours

    const safeTokens = tokens.filter(t => t.safetyScore >= 70).length;
    const riskyTokens = tokens.filter(t => t.safetyScore < 50).length;
    const recentDetections = tokens.filter(t => t.detectedAt > recentCutoff).length;
    const averageSafetyScore = tokens.length > 0 
      ? tokens.reduce((sum, t) => sum + t.safetyScore, 0) / tokens.length 
      : 0;

    return {
      totalDetected: tokens.length,
      safeTokens,
      riskyTokens,
      recentDetections,
      averageSafetyScore: Math.round(averageSafetyScore),
    };
  }

  /**
   * Parse mint account data
   */
  private parseMintAccountData(data: Buffer): { mint: string; supply: number; decimals: number } | null {
    try {
      if (!data || data.length !== 82) return null;

      // Mint account layout:
      // 0-4: mint_authority (option)
      // 4-12: supply (u64)
      // 12: decimals (u8)
      // 13-17: is_initialized (bool) + freeze_authority (option)

      const supply = data.readBigUInt64LE(4);
      const decimals = data.readUInt8(12);

      // Generate a placeholder mint address (in real implementation, this would be the account key)
      const mint = 'detected_mint_' + Date.now();

      return {
        mint,
        supply: Number(supply),
        decimals,
      };
    } catch (error) {
      console.warn('Failed to parse mint account data:', error);
      return null;
    }
  }

  /**
   * Parse token account data
   */
  private parseTokenAccountData(data: Buffer): { mint: string; amount: number; owner: string } | null {
    try {
      if (!data || data.length !== 165) return null;

      // Token account layout:
      // 0-32: mint (pubkey)
      // 32-64: owner (pubkey)
      // 64-72: amount (u64)

      const mint = new PublicKey(data.slice(0, 32)).toBase58();
      const owner = new PublicKey(data.slice(32, 64)).toBase58();
      const amount = data.readBigUInt64LE(64);

      return {
        mint,
        amount: Number(amount),
        owner,
      };
    } catch (error) {
      console.warn('Failed to parse token account data:', error);
      return null;
    }
  }

  /**
   * Extract token mints from market data
   */
  private extractTokenMintsFromMarket(data: Buffer, dex: string): string[] {
    try {
      const mints: string[] = [];

      if (!data || data.length === 0) {
        return mints;
      }

      if (dex === 'raydium') {
        console.log(`🔍 ADVANCED Raydium pool parsing (${data.length} bytes)`);

        // Raydium AMM pool structure - use proper layout parsing
        if (data.length >= 752) {
          try {
            // Raydium V4 AMM pool layout offsets (based on LIQUIDITY_STATE_LAYOUT_V4)
            // These are the correct offsets for Raydium V4 pools
            const baseMintOffset = 73;   // Offset for baseMint in the layout
            const quoteMintOffset = 105; // Offset for quoteMint in the layout

            // Extract base token mint
            if (data.length >= baseMintOffset + 32) {
              const baseMint = data.slice(baseMintOffset, baseMintOffset + 32);
              if (!baseMint.every(byte => byte === 0)) {
                try {
                  const baseMintAddress = new PublicKey(baseMint).toBase58();
                  if (this.isValidTokenMint(baseMintAddress)) {
                    console.log(`🎯 BASE TOKEN MINT: ${baseMintAddress}`);
                    mints.push(baseMintAddress);
                  }
                } catch (error) {
                  // Invalid public key, skip
                }
              }
            }

            // Extract quote token mint
            if (data.length >= quoteMintOffset + 32) {
              const quoteMint = data.slice(quoteMintOffset, quoteMintOffset + 32);
              if (!quoteMint.every(byte => byte === 0)) {
                try {
                  const quoteMintAddress = new PublicKey(quoteMint).toBase58();
                  if (this.isValidTokenMint(quoteMintAddress)) {
                    console.log(`🎯 QUOTE TOKEN MINT: ${quoteMintAddress}`);
                    mints.push(quoteMintAddress);
                  }
                } catch (error) {
                  // Invalid public key, skip
                }
              }
            }
          } catch (error) {
            console.log(`⚠️ Error parsing known offsets, falling back to scan`);
          }
        }

        // Fallback: Scan for any valid token mints in the data
        if (mints.length === 0) {
          console.log(`🔍 Scanning entire buffer for token mints...`);

          // Look for potential token mint addresses in the data
          for (let i = 0; i <= data.length - 32; i += 4) { // Check every 4 bytes
            try {
              const potentialMint = data.slice(i, i + 32);

              // Check if this looks like a valid public key (not all zeros, not all 255s)
              if (!potentialMint.every(byte => byte === 0) &&
                  !potentialMint.every(byte => byte === 255)) {

                const pubkey = new PublicKey(potentialMint);
                const mintAddress = pubkey.toBase58();

                if (this.isValidTokenMint(mintAddress) && !mints.includes(mintAddress)) {
                  console.log(`🪙 SCANNED TOKEN MINT: ${mintAddress} (offset: ${i})`);
                  mints.push(mintAddress);

                  // Limit to prevent spam
                  if (mints.length >= 10) break;
                }
              }
            } catch (error) {
              // Skip invalid public keys
              continue;
            }
          }
        }
      } else if (dex === 'orca') {
        console.log(`🔍 Parsing Orca market data (${data.length} bytes)`);
        // Orca whirlpool structure parsing would go here
      }

      if (mints.length > 0) {
        console.log(`✅ EXTRACTED ${mints.length} TOKEN MINTS from ${dex} pool:`);
        mints.forEach((mint, index) => {
          console.log(`   ${index + 1}. ${mint}`);
        });
      } else {
        console.log(`❌ No valid token mints found in ${dex} pool data`);
      }

      return mints;
    } catch (error) {
      console.warn(`Failed to extract mints from ${dex} market:`, error);
      return [];
    }
  }

  /**
   * Validate if an address is a valid token mint
   */
  private isValidTokenMint(mintAddress: string): boolean {
    try {
      // Basic validation
      if (!mintAddress || mintAddress.length !== 44) {
        return false;
      }

      // Exclude known system accounts and common addresses
      const excludedPrefixes = [
        '********', // System Program
        'So********', // Wrapped SOL
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL', // Associated Token Program
        'SysvarRent********************************1', // Sysvar Rent
        'SysvarC1ock********************************', // Sysvar Clock
      ];

      // Exclude major tokens that are not "new"
      const excludedTokens = [
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
        '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R', // RAY
        'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So', // mSOL
        'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', // BONK
      ];

      for (const prefix of excludedPrefixes) {
        if (mintAddress.startsWith(prefix)) {
          return false;
        }
      }

      // Check exact matches for major tokens
      if (excludedTokens.includes(mintAddress)) {
        return false;
      }

      // Check if it's a valid base58 address
      try {
        const pubkey = new PublicKey(mintAddress);
        // Additional check: make sure it's not all zeros or all ones
        const bytes = pubkey.toBytes();
        if (bytes.every(b => b === 0) || bytes.every(b => b === 255)) {
          return false;
        }
        return true;
      } catch {
        return false;
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * Analyze transaction for new tokens
   */
  private async analyzeTransactionForTokens(transaction: any, filters: DetectionFilters): Promise<void> {
    try {
      if (!transaction.meta || !transaction.meta.postTokenBalances) return;

      // Check for new token accounts in post-token balances
      for (const balance of transaction.meta.postTokenBalances) {
        if (balance.uiTokenAmount && balance.uiTokenAmount.uiAmount > 0) {
          const mint = balance.mint;

          if (!this.detectedTokens.has(mint)) {
            console.log(`🔍 New token found in transaction: ${mint}`);
            await this.analyzeNewToken(mint, filters);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to analyze transaction for tokens:', error);
    }
  }

  /**
   * Enhanced token analysis with real blockchain data
   */
  private async analyzeTokenWithBlockchainData(mintAddress: string): Promise<Partial<DetectedToken>> {
    try {
      const mint = new PublicKey(mintAddress);

      // Get mint info
      const mintInfo = await getMint(this.connection, mint);

      // Get all token accounts for this mint
      const tokenAccounts = await this.connection.getProgramAccounts(TOKEN_PROGRAM_ID, {
        filters: [
          { dataSize: 165 },
          { memcmp: { offset: 0, bytes: mint.toBase58() } },
        ],
      });

      // Calculate holder count and distribution
      const holders = tokenAccounts.length;
      let totalSupply = 0;
      const holderBalances: number[] = [];

      for (const account of tokenAccounts) {
        try {
          const tokenAccount = await getAccount(this.connection, account.pubkey);
          const balance = Number(tokenAccount.amount);
          totalSupply += balance;
          holderBalances.push(balance);
        } catch (error) {
          // Skip invalid accounts
        }
      }

      // Sort balances to find top holders
      holderBalances.sort((a, b) => b - a);
      const topHolderPercentage = holderBalances.length > 0 ? (holderBalances[0] / totalSupply) * 100 : 0;
      const top10Percentage = holderBalances.slice(0, 10).reduce((sum, balance) => sum + balance, 0) / totalSupply * 100;

      return {
        mint: mintAddress,
        decimals: mintInfo.decimals,
        supply: Number(mintInfo.supply),
        mintAuthority: mintInfo.mintAuthority?.toBase58(),
        freezeAuthority: mintInfo.freezeAuthority?.toBase58(),
        isInitialized: mintInfo.isInitialized,
        liquidityPools: [], // Would be populated by DEX analysis
        safetyScore: this.calculateBasicSafetyScore(mintInfo, holders, topHolderPercentage),
        riskFactors: this.identifyRiskFactors(mintInfo, holders, topHolderPercentage),
      };
    } catch (error) {
      console.warn(`Failed to analyze token ${mintAddress} with blockchain data:`, error);
      return {};
    }
  }

  /**
   * Calculate basic safety score from blockchain data
   */
  private calculateBasicSafetyScore(mintInfo: Mint, holders: number, topHolderPercentage: number): number {
    let score = 100;

    // Penalize for authorities
    if (mintInfo.mintAuthority) score -= 20;
    if (mintInfo.freezeAuthority) score -= 15;

    // Penalize for low holder count
    if (holders < 10) score -= 30;
    else if (holders < 50) score -= 15;

    // Penalize for high concentration
    if (topHolderPercentage > 50) score -= 25;
    else if (topHolderPercentage > 20) score -= 10;

    return Math.max(0, score);
  }

  /**
   * Identify risk factors from blockchain data
   */
  private identifyRiskFactors(mintInfo: Mint, holders: number, topHolderPercentage: number): string[] {
    const risks: string[] = [];

    if (mintInfo.mintAuthority) risks.push('Mint authority present');
    if (mintInfo.freezeAuthority) risks.push('Freeze authority present');
    if (holders < 10) risks.push('Very few holders');
    if (topHolderPercentage > 50) risks.push('High token concentration');
    if (Number(mintInfo.supply) === 0) risks.push('No tokens minted yet');

    return risks;
  }

  /**
   * Clear old detections
   */
  clearOldDetections(maxAge: number = 7 * 24 * 60 * 60 * 1000): number { // 7 days default
    const cutoff = Date.now() - maxAge;
    let cleared = 0;

    for (const [mint, token] of this.detectedTokens.entries()) {
      if (token.detectedAt < cutoff) {
        this.detectedTokens.delete(mint);
        cleared++;
      }
    }

    console.log(`🧹 Cleared ${cleared} old token detections`);
    return cleared;
  }
}

// Export singleton instance
export const tokenDetectionEngine = new TokenDetectionEngine();

// Export convenience functions
export const startTokenMonitoring = (filters?: DetectionFilters) => 
  tokenDetectionEngine.startMonitoring(filters);
export const stopTokenMonitoring = () => tokenDetectionEngine.stopMonitoring();
export const getDetectedTokens = (filters?: Partial<DetectionFilters>) => 
  tokenDetectionEngine.getDetectedTokens(filters);
export const analyzeToken = (mintAddress: string, filters?: DetectionFilters) => 
  tokenDetectionEngine.analyzeNewToken(mintAddress, filters);
