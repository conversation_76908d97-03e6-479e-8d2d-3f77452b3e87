import { PublicKey, AccountInfo } from '@solana/web3.js';
import { getMint, getAccount, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { getConnection } from '../solana/connection';
import { jupiterSwapManager } from '../jupiter/swap';
import { priceFeedManager } from '../price/feeds';
import { DetectedToken } from './detection';
import { config } from '@/config';

export interface SafetyAnalysis {
  mint: string;
  overallScore: number; // 0-100
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  checks: SafetyCheck[];
  honeypotRisk: HoneypotAnalysis;
  liquidityAnalysis: LiquidityAnalysis;
  holderAnalysis: HolderAnalysis;
  authorityAnalysis: AuthorityAnalysis;
  metadataAnalysis: MetadataAnalysis;
  tradingAnalysis: TradingAnalysis;
  timestamp: number;
}

export interface SafetyCheck {
  name: string;
  passed: boolean;
  score: number; // Impact on overall score
  description: string;
  severity: 'INFO' | 'WARNING' | 'CRITICAL';
  details?: any;
}

export interface HoneypotAnalysis {
  isHoneypot: boolean;
  confidence: number; // 0-100
  indicators: string[];
  canSell: boolean;
  sellTax: number;
  buyTax: number;
  testResults?: {
    buyTest: boolean;
    sellTest: boolean;
    error?: string;
  };
}

export interface LiquidityAnalysis {
  totalLiquidity: number;
  liquidityPools: {
    dex: string;
    address: string;
    liquidity: number;
    volume24h: number;
    locked: boolean;
    lockExpiry?: number;
  }[];
  liquidityScore: number;
  isRugPullRisk: boolean;
}

export interface HolderAnalysis {
  totalHolders: number;
  topHolderPercentage: number;
  top10HolderPercentage: number;
  distributionScore: number;
  suspiciousHolders: string[];
  creatorHolding: number;
}

export interface AuthorityAnalysis {
  mintAuthority: string | null;
  freezeAuthority: string | null;
  updateAuthority: string | null;
  isRenounced: boolean;
  riskScore: number;
  warnings: string[];
}

export interface MetadataAnalysis {
  hasMetadata: boolean;
  name?: string;
  symbol?: string;
  description?: string;
  image?: string;
  website?: string;
  social: {
    twitter?: string;
    telegram?: string;
    discord?: string;
  };
  metadataScore: number;
  suspiciousPatterns: string[];
}

export interface TradingAnalysis {
  volume24h: number;
  priceChange24h: number;
  volatility: number;
  tradingScore: number;
  suspiciousActivity: string[];
  lastTrade?: number;
}

export class TokenSafetyAnalyzer {
  private connection = getConnection();
  private analysisCache: Map<string, { analysis: SafetyAnalysis; expiry: number }> = new Map();
  private readonly CACHE_DURATION = 300000; // 5 minutes

  /**
   * Perform comprehensive safety analysis on a token
   */
  async analyzeToken(mintAddress: string, forceRefresh: boolean = false): Promise<SafetyAnalysis> {
    // Check cache first
    if (!forceRefresh) {
      const cached = this.analysisCache.get(mintAddress);
      if (cached && cached.expiry > Date.now()) {
        return cached.analysis;
      }
    }

    console.log(`🔍 Analyzing token safety: ${mintAddress}`);

    const analysis: SafetyAnalysis = {
      mint: mintAddress,
      overallScore: 0,
      riskLevel: 'HIGH',
      checks: [],
      honeypotRisk: await this.analyzeHoneypotRisk(mintAddress),
      liquidityAnalysis: await this.analyzeLiquidity(mintAddress),
      holderAnalysis: await this.analyzeHolders(mintAddress),
      authorityAnalysis: await this.analyzeAuthorities(mintAddress),
      metadataAnalysis: await this.analyzeMetadata(mintAddress),
      tradingAnalysis: await this.analyzeTradingActivity(mintAddress),
      timestamp: Date.now(),
    };

    // Run individual safety checks
    analysis.checks = await this.runSafetyChecks(mintAddress, analysis);

    // Calculate overall score
    analysis.overallScore = this.calculateOverallScore(analysis);
    analysis.riskLevel = this.determineRiskLevel(analysis.overallScore);

    // Cache the analysis
    this.analysisCache.set(mintAddress, {
      analysis,
      expiry: Date.now() + this.CACHE_DURATION,
    });

    console.log(`✅ Safety analysis complete: ${analysis.overallScore}/100 (${analysis.riskLevel})`);
    return analysis;
  }

  /**
   * Analyze honeypot risk
   */
  private async analyzeHoneypotRisk(mintAddress: string): Promise<HoneypotAnalysis> {
    const analysis: HoneypotAnalysis = {
      isHoneypot: false,
      confidence: 0,
      indicators: [],
      canSell: true,
      sellTax: 0,
      buyTax: 0,
    };

    try {
      // Simulate a small buy and sell to test for honeypot
      const testResults = await this.simulateTrade(mintAddress);
      analysis.testResults = testResults;

      if (!testResults.sellTest) {
        analysis.isHoneypot = true;
        analysis.confidence = 90;
        analysis.canSell = false;
        analysis.indicators.push('Cannot sell tokens');
      }

      // Check for high taxes
      if (analysis.sellTax > 10) {
        analysis.indicators.push(`High sell tax: ${analysis.sellTax}%`);
        analysis.confidence += 20;
      }

      if (analysis.buyTax > 10) {
        analysis.indicators.push(`High buy tax: ${analysis.buyTax}%`);
        analysis.confidence += 10;
      }

      analysis.confidence = Math.min(100, analysis.confidence);
    } catch (error) {
      console.warn('Honeypot analysis failed:', error);
      analysis.indicators.push('Analysis failed - proceed with caution');
    }

    return analysis;
  }

  /**
   * Simulate a trade to test for honeypot with real transaction simulation
   */
  private async simulateTrade(mintAddress: string): Promise<{
    buyTest: boolean;
    sellTest: boolean;
    error?: string;
  }> {
    try {
      console.log(`🧪 Testing honeypot for ${mintAddress}`);

      // Create a temporary keypair for testing
      const testKeypair = new (await import('@solana/web3.js')).Keypair();

      // Test 1: Check if we can get a buy quote
      const buyQuote = await jupiterSwapManager.getSwapQuote({
        inputMint: 'So11111111111111111111111111111111111111112', // SOL
        outputMint: mintAddress,
        amount: 0.001, // 0.001 SOL
        slippageBps: 1000, // 10% slippage tolerance for testing
      });

      if (!buyQuote.success) {
        return {
          buyTest: false,
          sellTest: false,
          error: `Buy quote failed: ${buyQuote.error}`
        };
      }

      console.log(`✅ Buy quote successful: ${buyQuote.inputAmount} SOL → ${buyQuote.outputAmount} tokens`);

      // Test 2: Check if we can get a sell quote
      const sellQuote = await jupiterSwapManager.getSwapQuote({
        inputMint: mintAddress,
        outputMint: 'So11111111111111111111111111111111111111112', // SOL
        amount: buyQuote.outputAmount * 0.9, // Sell 90% to account for slippage
        slippageBps: 1000, // 10% slippage tolerance
      });

      if (!sellQuote.success) {
        console.warn(`❌ Sell quote failed: ${sellQuote.error}`);
        return {
          buyTest: true,
          sellTest: false,
          error: `Sell quote failed: ${sellQuote.error}`,
        };
      }

      console.log(`✅ Sell quote successful: ${sellQuote.inputAmount} tokens → ${sellQuote.outputAmount} SOL`);

      // Test 3: Advanced honeypot detection - check for suspicious patterns
      const suspiciousPatterns = await this.detectSuspiciousPatterns(mintAddress, buyQuote, sellQuote);

      return {
        buyTest: true,
        sellTest: true,
        error: suspiciousPatterns.length > 0 ? `Suspicious patterns: ${suspiciousPatterns.join(', ')}` : undefined,
      };
    } catch (error) {
      console.error(`Honeypot test failed for ${mintAddress}:`, error);
      return {
        buyTest: false,
        sellTest: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Detect suspicious patterns that might indicate a honeypot
   */
  private async detectSuspiciousPatterns(
    mintAddress: string,
    buyQuote: any,
    sellQuote: any
  ): Promise<string[]> {
    const patterns: string[] = [];

    try {
      // Pattern 1: Extreme price impact difference
      const buyImpact = buyQuote.priceImpact || 0;
      const sellImpact = sellQuote.priceImpact || 0;

      if (Math.abs(buyImpact - sellImpact) > 50) {
        patterns.push('Extreme price impact difference');
      }

      // Pattern 2: Very high slippage on sell
      if (sellImpact > 90) {
        patterns.push('Extremely high sell slippage');
      }

      // Pattern 3: Suspicious route complexity
      if (sellQuote.route && sellQuote.route.length > buyQuote.route?.length + 2) {
        patterns.push('Suspicious routing complexity');
      }

      // Pattern 4: Check for blacklisted DEXes or suspicious pools
      const suspiciousDexes = ['unknown', 'suspicious'];
      if (buyQuote.route?.some((hop: any) => suspiciousDexes.includes(hop.swapInfo?.label?.toLowerCase()))) {
        patterns.push('Suspicious DEX in route');
      }

      // Pattern 5: Analyze token account structure
      const accountAnalysis = await this.analyzeTokenAccountStructure(mintAddress);
      if (accountAnalysis.suspiciousPatterns.length > 0) {
        patterns.push(...accountAnalysis.suspiciousPatterns);
      }

    } catch (error) {
      console.warn('Failed to detect suspicious patterns:', error);
      patterns.push('Pattern analysis failed');
    }

    return patterns;
  }

  /**
   * Analyze token account structure for suspicious patterns
   */
  private async analyzeTokenAccountStructure(mintAddress: string): Promise<{
    suspiciousPatterns: string[];
    accountCount: number;
    largestHolderPercentage: number;
  }> {
    try {
      const mint = new (await import('@solana/web3.js')).PublicKey(mintAddress);

      // Get all token accounts for this mint
      const tokenAccounts = await this.connection.getProgramAccounts(
        new (await import('@solana/web3.js')).PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'),
        {
          filters: [
            { dataSize: 165 }, // Token account size
            { memcmp: { offset: 0, bytes: mint.toBase58() } }, // Filter by mint
          ],
        }
      );

      const patterns: string[] = [];
      const accountCount = tokenAccounts.length;

      if (accountCount < 5) {
        patterns.push('Very few token holders');
      }

      // Analyze holder distribution
      const balances: number[] = [];
      let totalSupply = 0;

      for (const account of tokenAccounts.slice(0, 100)) { // Limit to first 100 for performance
        try {
          const accountData = account.account.data;
          if (accountData.length >= 72) {
            const balance = accountData.readBigUInt64LE(64);
            const balanceNum = Number(balance);
            balances.push(balanceNum);
            totalSupply += balanceNum;
          }
        } catch (error) {
          // Skip invalid accounts
        }
      }

      balances.sort((a, b) => b - a);
      const largestHolderPercentage = totalSupply > 0 ? (balances[0] / totalSupply) * 100 : 0;

      if (largestHolderPercentage > 80) {
        patterns.push('Single holder owns >80% of supply');
      } else if (largestHolderPercentage > 50) {
        patterns.push('Single holder owns >50% of supply');
      }

      // Check for suspicious holder patterns
      const top10Supply = balances.slice(0, 10).reduce((sum, balance) => sum + balance, 0);
      const top10Percentage = totalSupply > 0 ? (top10Supply / totalSupply) * 100 : 0;

      if (top10Percentage > 95) {
        patterns.push('Top 10 holders own >95% of supply');
      }

      return {
        suspiciousPatterns: patterns,
        accountCount,
        largestHolderPercentage,
      };
    } catch (error) {
      console.warn('Failed to analyze token account structure:', error);
      return {
        suspiciousPatterns: ['Account structure analysis failed'],
        accountCount: 0,
        largestHolderPercentage: 0,
      };
    }
  }

  /**
   * Analyze liquidity across multiple DEXes
   */
  private async analyzeLiquidity(mintAddress: string): Promise<LiquidityAnalysis> {
    try {
      console.log(`💧 Analyzing liquidity for ${mintAddress}`);

      const liquidityPools: LiquidityAnalysis['liquidityPools'] = [];
      let totalLiquidity = 0;

      // Check Raydium pools
      const raydiumPools = await this.getRaydiumPools(mintAddress);
      liquidityPools.push(...raydiumPools);

      // Check Orca pools
      const orcaPools = await this.getOrcaPools(mintAddress);
      liquidityPools.push(...orcaPools);

      // Check Jupiter aggregated liquidity
      const jupiterLiquidity = await this.getJupiterLiquidity(mintAddress);
      if (jupiterLiquidity) {
        liquidityPools.push(jupiterLiquidity);
      }

      // Calculate total liquidity
      totalLiquidity = liquidityPools.reduce((sum, pool) => sum + pool.liquidity, 0);

      // Calculate liquidity score
      const liquidityScore = this.calculateLiquidityScore(totalLiquidity, liquidityPools);

      // Assess rug pull risk
      const isRugPullRisk = this.assessRugPullRisk(liquidityPools, totalLiquidity);

      console.log(`💧 Liquidity analysis complete: ${totalLiquidity.toFixed(2)} SOL across ${liquidityPools.length} pools`);

      return {
        totalLiquidity,
        liquidityPools,
        liquidityScore,
        isRugPullRisk,
      };
    } catch (error) {
      console.warn('Liquidity analysis failed:', error);
      return {
        totalLiquidity: 0,
        liquidityPools: [],
        liquidityScore: 0,
        isRugPullRisk: true,
      };
    }
  }

  /**
   * Get Raydium pools for a token
   */
  private async getRaydiumPools(mintAddress: string): Promise<LiquidityAnalysis['liquidityPools']> {
    try {
      // This would integrate with Raydium API
      // For now, return empty array as we'd need specific Raydium SDK integration
      return [];
    } catch (error) {
      console.warn('Failed to get Raydium pools:', error);
      return [];
    }
  }

  /**
   * Get Orca pools for a token
   */
  private async getOrcaPools(mintAddress: string): Promise<LiquidityAnalysis['liquidityPools']> {
    try {
      // This would integrate with Orca API
      // For now, return empty array as we'd need specific Orca SDK integration
      return [];
    } catch (error) {
      console.warn('Failed to get Orca pools:', error);
      return [];
    }
  }

  /**
   * Get Jupiter aggregated liquidity data
   */
  private async getJupiterLiquidity(mintAddress: string): Promise<LiquidityAnalysis['liquidityPools'][0] | null> {
    try {
      // Use Jupiter to get a quote and estimate liquidity
      const quote = await jupiterSwapManager.getSwapQuote({
        inputMint: 'So11111111111111111111111111111111111111112', // SOL
        outputMint: mintAddress,
        amount: 1, // 1 SOL
        slippageBps: 100, // 1% slippage
      });

      if (quote.success && quote.priceImpact !== undefined) {
        // Estimate liquidity based on price impact
        // Lower price impact = higher liquidity
        const estimatedLiquidity = this.estimateLiquidityFromPriceImpact(1, quote.priceImpact);

        return {
          dex: 'Jupiter (Aggregated)',
          address: 'aggregated',
          liquidity: estimatedLiquidity,
          volume24h: 0, // Would need additional API call
          locked: false, // Unknown for aggregated
        };
      }

      return null;
    } catch (error) {
      console.warn('Failed to get Jupiter liquidity:', error);
      return null;
    }
  }

  /**
   * Estimate liquidity from price impact
   */
  private estimateLiquidityFromPriceImpact(tradeSize: number, priceImpact: number): number {
    if (priceImpact <= 0) return 1000; // Very high liquidity

    // Rough estimation: liquidity = trade_size / (price_impact / 100) * scaling_factor
    const scalingFactor = 50; // Empirical scaling factor
    return Math.min(1000, (tradeSize / (priceImpact / 100)) * scalingFactor);
  }

  /**
   * Calculate liquidity score (0-100)
   */
  private calculateLiquidityScore(totalLiquidity: number, pools: LiquidityAnalysis['liquidityPools']): number {
    let score = 0;

    // Base score from total liquidity
    if (totalLiquidity >= 100) score += 40;
    else if (totalLiquidity >= 50) score += 30;
    else if (totalLiquidity >= 10) score += 20;
    else if (totalLiquidity >= 1) score += 10;

    // Bonus for multiple pools (diversification)
    if (pools.length >= 3) score += 20;
    else if (pools.length >= 2) score += 10;

    // Bonus for locked liquidity
    const lockedPools = pools.filter(pool => pool.locked);
    if (lockedPools.length > 0) {
      score += Math.min(20, lockedPools.length * 10);
    }

    // Bonus for high volume
    const totalVolume = pools.reduce((sum, pool) => sum + pool.volume24h, 0);
    if (totalVolume >= 1000) score += 20;
    else if (totalVolume >= 100) score += 10;

    return Math.min(100, score);
  }

  /**
   * Assess rug pull risk based on liquidity
   */
  private assessRugPullRisk(pools: LiquidityAnalysis['liquidityPools'], totalLiquidity: number): boolean {
    // High risk if very low liquidity
    if (totalLiquidity < 1) return true;

    // High risk if no locked liquidity
    const hasLockedLiquidity = pools.some(pool => pool.locked);
    if (!hasLockedLiquidity && totalLiquidity < 10) return true;

    // High risk if only one small pool
    if (pools.length === 1 && totalLiquidity < 5) return true;

    return false;
  }

  /**
   * Analyze token holders
   */
  private async analyzeHolders(mintAddress: string): Promise<HolderAnalysis> {
    try {
      const mint = new PublicKey(mintAddress);
      
      // Get all token accounts for this mint
      const tokenAccounts = await this.connection.getProgramAccounts(TOKEN_PROGRAM_ID, {
        filters: [
          { dataSize: 165 }, // Token account size
          { memcmp: { offset: 0, bytes: mint.toBase58() } }, // Filter by mint
        ],
      });

      const holders = tokenAccounts.length;
      
      // This is a simplified analysis - would need more sophisticated holder analysis
      return {
        totalHolders: holders,
        topHolderPercentage: 0,
        top10HolderPercentage: 0,
        distributionScore: holders > 100 ? 80 : holders > 50 ? 60 : 30,
        suspiciousHolders: [],
        creatorHolding: 0,
      };
    } catch (error) {
      console.warn('Holder analysis failed:', error);
      return {
        totalHolders: 0,
        topHolderPercentage: 100,
        top10HolderPercentage: 100,
        distributionScore: 0,
        suspiciousHolders: [],
        creatorHolding: 100,
      };
    }
  }

  /**
   * Analyze token authorities
   */
  private async analyzeAuthorities(mintAddress: string): Promise<AuthorityAnalysis> {
    try {
      const mint = new PublicKey(mintAddress);
      const mintInfo = await getMint(this.connection, mint);

      const warnings: string[] = [];
      let riskScore = 0;

      const mintAuthority = mintInfo.mintAuthority?.toBase58() || null;
      const freezeAuthority = mintInfo.freezeAuthority?.toBase58() || null;

      if (mintAuthority) {
        warnings.push('Mint authority present - new tokens can be minted');
        riskScore += 30;
      }

      if (freezeAuthority) {
        warnings.push('Freeze authority present - accounts can be frozen');
        riskScore += 20;
      }

      const isRenounced = !mintAuthority && !freezeAuthority;

      return {
        mintAuthority,
        freezeAuthority,
        updateAuthority: null, // Would need to check metadata program
        isRenounced,
        riskScore,
        warnings,
      };
    } catch (error) {
      console.warn('Authority analysis failed:', error);
      return {
        mintAuthority: 'unknown',
        freezeAuthority: 'unknown',
        updateAuthority: 'unknown',
        isRenounced: false,
        riskScore: 100,
        warnings: ['Failed to analyze authorities'],
      };
    }
  }

  /**
   * Analyze token metadata
   */
  private async analyzeMetadata(mintAddress: string): Promise<MetadataAnalysis> {
    // This would require integration with Metaplex metadata program
    return {
      hasMetadata: false,
      metadataScore: 0,
      suspiciousPatterns: ['No metadata found'],
      social: {},
    };
  }

  /**
   * Analyze trading activity
   */
  private async analyzeTradingActivity(mintAddress: string): Promise<TradingAnalysis> {
    try {
      const price = await priceFeedManager.getTokenPrice(mintAddress);
      
      return {
        volume24h: price?.volume24h || 0,
        priceChange24h: price?.priceChangePercent24h || 0,
        volatility: 0,
        tradingScore: price ? 50 : 0,
        suspiciousActivity: [],
        lastTrade: price?.lastUpdated,
      };
    } catch (error) {
      return {
        volume24h: 0,
        priceChange24h: 0,
        volatility: 0,
        tradingScore: 0,
        suspiciousActivity: ['No trading data available'],
      };
    }
  }

  /**
   * Run individual safety checks
   */
  private async runSafetyChecks(mintAddress: string, analysis: SafetyAnalysis): Promise<SafetyCheck[]> {
    const checks: SafetyCheck[] = [];

    // Honeypot check
    checks.push({
      name: 'Honeypot Detection',
      passed: !analysis.honeypotRisk.isHoneypot,
      score: analysis.honeypotRisk.isHoneypot ? -50 : 20,
      description: analysis.honeypotRisk.isHoneypot ? 'Token appears to be a honeypot' : 'No honeypot indicators detected',
      severity: analysis.honeypotRisk.isHoneypot ? 'CRITICAL' : 'INFO',
      details: analysis.honeypotRisk,
    });

    // Authority check
    checks.push({
      name: 'Authority Analysis',
      passed: analysis.authorityAnalysis.isRenounced,
      score: analysis.authorityAnalysis.isRenounced ? 15 : -analysis.authorityAnalysis.riskScore,
      description: analysis.authorityAnalysis.isRenounced ? 'Authorities renounced' : 'Authorities present',
      severity: analysis.authorityAnalysis.riskScore > 30 ? 'WARNING' : 'INFO',
      details: analysis.authorityAnalysis,
    });

    // Liquidity check
    checks.push({
      name: 'Liquidity Analysis',
      passed: analysis.liquidityAnalysis.totalLiquidity >= config.tokenDetection.minLiquiditySol,
      score: analysis.liquidityAnalysis.liquidityScore,
      description: `Total liquidity: ${analysis.liquidityAnalysis.totalLiquidity} SOL`,
      severity: analysis.liquidityAnalysis.isRugPullRisk ? 'WARNING' : 'INFO',
      details: analysis.liquidityAnalysis,
    });

    // Holder distribution check
    checks.push({
      name: 'Holder Distribution',
      passed: analysis.holderAnalysis.distributionScore > 50,
      score: analysis.holderAnalysis.distributionScore - 50,
      description: `${analysis.holderAnalysis.totalHolders} holders`,
      severity: analysis.holderAnalysis.distributionScore < 30 ? 'WARNING' : 'INFO',
      details: analysis.holderAnalysis,
    });

    return checks;
  }

  /**
   * Calculate overall safety score
   */
  private calculateOverallScore(analysis: SafetyAnalysis): number {
    let score = 50; // Base score

    // Add/subtract scores from individual checks
    for (const check of analysis.checks) {
      score += check.score;
    }

    // Additional factors
    score += analysis.liquidityAnalysis.liquidityScore * 0.3;
    score += analysis.holderAnalysis.distributionScore * 0.2;
    score += analysis.metadataAnalysis.metadataScore * 0.1;
    score += analysis.tradingAnalysis.tradingScore * 0.2;

    // Subtract authority risk
    score -= analysis.authorityAnalysis.riskScore * 0.5;

    // Heavy penalty for honeypots
    if (analysis.honeypotRisk.isHoneypot) {
      score = Math.min(score, 20);
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Determine risk level based on score
   */
  private determineRiskLevel(score: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (score >= 80) return 'LOW';
    if (score >= 60) return 'MEDIUM';
    if (score >= 30) return 'HIGH';
    return 'CRITICAL';
  }

  /**
   * Get cached analysis
   */
  getCachedAnalysis(mintAddress: string): SafetyAnalysis | null {
    const cached = this.analysisCache.get(mintAddress);
    if (cached && cached.expiry > Date.now()) {
      return cached.analysis;
    }
    return null;
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
  }

  /**
   * Get analysis statistics
   */
  getAnalysisStats(): {
    totalAnalyzed: number;
    safeTokens: number;
    riskyTokens: number;
    honeypots: number;
    cacheSize: number;
  } {
    const analyses = Array.from(this.analysisCache.values()).map(c => c.analysis);
    
    return {
      totalAnalyzed: analyses.length,
      safeTokens: analyses.filter(a => a.riskLevel === 'LOW').length,
      riskyTokens: analyses.filter(a => a.riskLevel === 'HIGH' || a.riskLevel === 'CRITICAL').length,
      honeypots: analyses.filter(a => a.honeypotRisk.isHoneypot).length,
      cacheSize: this.analysisCache.size,
    };
  }
}

// Export singleton instance
export const tokenSafetyAnalyzer = new TokenSafetyAnalyzer();

// Export convenience functions
export const analyzeTokenSafety = (mintAddress: string, forceRefresh?: boolean) => 
  tokenSafetyAnalyzer.analyzeToken(mintAddress, forceRefresh);
export const getCachedSafetyAnalysis = (mintAddress: string) => 
  tokenSafetyAnalyzer.getCachedAnalysis(mintAddress);
