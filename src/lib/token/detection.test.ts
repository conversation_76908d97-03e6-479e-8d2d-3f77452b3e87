import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { tokenDetectionEngine } from './detection';
import { createMockConnection } from '@/test/setup';

// Mock Solana connection
vi.mock('@/lib/solana/connection', () => ({
  getConnection: vi.fn(() => createMockConnection()),
}));

// Mock SPL Token
vi.mock('@solana/spl-token', () => ({
  getMint: vi.fn(),
  getAccount: vi.fn(),
  TOKEN_PROGRAM_ID: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
}));

// Mock Metaplex
vi.mock('@metaplex-foundation/mpl-token-metadata', () => ({
  Metadata: {
    fromAccountAddress: vi.fn(),
  },
  PROGRAM_ID: 'metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s',
}));

describe('TokenDetectionEngine', () => {
  const mockToken = {
    mint: 'TestMint123456789',
    symbol: 'TEST',
    name: 'Test Token',
    decimals: 9,
    supply: **********,
    mintAuthority: null,
    freezeAuthority: null,
    isInitialized: true,
    detectedAt: Date.now(),
    liquidityPools: [],
    safetyScore: 75,
    riskFactors: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    tokenDetectionEngine.clearDetectedTokens();
  });

  afterEach(() => {
    tokenDetectionEngine.stopMonitoring();
    vi.clearAllMocks();
  });

  describe('startMonitoring', () => {
    it('should start monitoring successfully', async () => {
      const mockConnection = createMockConnection();
      mockConnection.onProgramAccountChange.mockReturnValue(1);
      mockConnection.onLogs.mockReturnValue(2);

      await tokenDetectionEngine.startMonitoring();

      expect(tokenDetectionEngine.isMonitoring()).toBe(true);
      expect(mockConnection.onProgramAccountChange).toHaveBeenCalled();
      expect(mockConnection.onLogs).toHaveBeenCalled();
    });

    it('should not start monitoring if already active', async () => {
      await tokenDetectionEngine.startMonitoring();
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      await tokenDetectionEngine.startMonitoring();

      expect(consoleSpy).toHaveBeenCalledWith('Token monitoring is already active');
      consoleSpy.mockRestore();
    });

    it('should handle monitoring startup errors', async () => {
      const mockConnection = createMockConnection();
      mockConnection.onProgramAccountChange.mockImplementation(() => {
        throw new Error('Subscription failed');
      });

      await expect(tokenDetectionEngine.startMonitoring()).rejects.toThrow('Subscription failed');
      expect(tokenDetectionEngine.isMonitoring()).toBe(false);
    });
  });

  describe('stopMonitoring', () => {
    it('should stop monitoring successfully', async () => {
      const mockConnection = createMockConnection();
      mockConnection.onProgramAccountChange.mockReturnValue(1);
      mockConnection.removeAccountChangeListener.mockImplementation(() => {});

      await tokenDetectionEngine.startMonitoring();
      tokenDetectionEngine.stopMonitoring();

      expect(tokenDetectionEngine.isMonitoring()).toBe(false);
      expect(mockConnection.removeAccountChangeListener).toHaveBeenCalled();
    });
  });

  describe('analyzeNewToken', () => {
    it('should analyze new token successfully', async () => {
      const { getMint } = await import('@solana/spl-token');
      (getMint as any).mockResolvedValue({
        decimals: 9,
        supply: BigInt(**********),
        mintAuthority: null,
        freezeAuthority: null,
        isInitialized: true,
      });

      const mockConnection = createMockConnection();
      mockConnection.getProgramAccounts.mockResolvedValue([
        {
          pubkey: 'account1',
          account: {
            data: Buffer.alloc(165),
          },
        },
      ]);

      const result = await tokenDetectionEngine.analyzeNewToken('TestMint123456789');

      expect(result).toBeDefined();
      expect(result?.mint).toBe('TestMint123456789');
      expect(result?.decimals).toBe(9);
      expect(result?.supply).toBe(**********);
    });

    it('should return null for invalid token', async () => {
      const { getMint } = await import('@solana/spl-token');
      (getMint as any).mockRejectedValue(new Error('Invalid mint'));

      const result = await tokenDetectionEngine.analyzeNewToken('InvalidMint');

      expect(result).toBeNull();
    });

    it('should return cached token if already analyzed', async () => {
      // Add token to cache
      tokenDetectionEngine['detectedTokens'].set('TestMint123456789', mockToken);

      const result = await tokenDetectionEngine.analyzeNewToken('TestMint123456789');

      expect(result).toEqual(mockToken);
    });

    it('should filter out tokens that do not pass filters', async () => {
      const { getMint } = await import('@solana/spl-token');
      (getMint as any).mockResolvedValue({
        decimals: 9,
        supply: BigInt(**********0000), // Very high supply
        mintAuthority: 'SomeAuthority',
        freezeAuthority: 'SomeAuthority',
        isInitialized: true,
      });

      const filters = {
        maxSupply: **********, // Lower than token supply
        minSafetyScore: 80, // Higher than calculated score
      };

      const result = await tokenDetectionEngine.analyzeNewToken('TestMint123456789', filters);

      expect(result).toBeNull();
    });
  });

  describe('getRecentTokens', () => {
    it('should return recent tokens within time window', () => {
      const now = Date.now();
      const recentToken = { ...mockToken, detectedAt: now - 30000 }; // 30 seconds ago
      const oldToken = { ...mockToken, mint: 'OldMint', detectedAt: now - 120000 }; // 2 minutes ago

      tokenDetectionEngine['detectedTokens'].set('TestMint123456789', recentToken);
      tokenDetectionEngine['detectedTokens'].set('OldMint', oldToken);

      const result = tokenDetectionEngine.getRecentTokens(1); // Last 1 minute

      expect(result).toHaveLength(1);
      expect(result[0].mint).toBe('TestMint123456789');
    });

    it('should return empty array when no recent tokens', () => {
      const result = tokenDetectionEngine.getRecentTokens(1);

      expect(result).toHaveLength(0);
    });
  });

  describe('getTopSafeTokens', () => {
    it('should return tokens sorted by safety score', () => {
      const token1 = { ...mockToken, mint: 'Token1', safetyScore: 90 };
      const token2 = { ...mockToken, mint: 'Token2', safetyScore: 80 };
      const token3 = { ...mockToken, mint: 'Token3', safetyScore: 70 };

      tokenDetectionEngine['detectedTokens'].set('Token1', token1);
      tokenDetectionEngine['detectedTokens'].set('Token2', token2);
      tokenDetectionEngine['detectedTokens'].set('Token3', token3);

      const result = tokenDetectionEngine.getTopSafeTokens(2);

      expect(result).toHaveLength(2);
      expect(result[0].safetyScore).toBe(90);
      expect(result[1].safetyScore).toBe(80);
    });
  });

  describe('searchTokens', () => {
    it('should search tokens by symbol', () => {
      const token1 = { ...mockToken, symbol: 'USDC', name: 'USD Coin' };
      const token2 = { ...mockToken, mint: 'Token2', symbol: 'USDT', name: 'Tether USD' };

      tokenDetectionEngine['detectedTokens'].set('Token1', token1);
      tokenDetectionEngine['detectedTokens'].set('Token2', token2);

      const result = tokenDetectionEngine.searchTokens('USD');

      expect(result).toHaveLength(2);
    });

    it('should search tokens by name', () => {
      const token1 = { ...mockToken, symbol: 'BTC', name: 'Bitcoin' };
      const token2 = { ...mockToken, mint: 'Token2', symbol: 'ETH', name: 'Ethereum' };

      tokenDetectionEngine['detectedTokens'].set('Token1', token1);
      tokenDetectionEngine['detectedTokens'].set('Token2', token2);

      const result = tokenDetectionEngine.searchTokens('bit');

      expect(result).toHaveLength(1);
      expect(result[0].symbol).toBe('BTC');
    });

    it('should return empty array for no matches', () => {
      tokenDetectionEngine['detectedTokens'].set('Token1', mockToken);

      const result = tokenDetectionEngine.searchTokens('NOMATCH');

      expect(result).toHaveLength(0);
    });
  });

  describe('getStats', () => {
    it('should return correct statistics', () => {
      const safeToken = { ...mockToken, safetyScore: 80 };
      const riskyToken = { ...mockToken, mint: 'RiskyToken', safetyScore: 40 };

      tokenDetectionEngine['detectedTokens'].set('SafeToken', safeToken);
      tokenDetectionEngine['detectedTokens'].set('RiskyToken', riskyToken);

      const stats = tokenDetectionEngine.getStats();

      expect(stats.totalDetected).toBe(2);
      expect(stats.safeTokens).toBe(1);
      expect(stats.riskyTokens).toBe(1);
      expect(stats.averageSafetyScore).toBe(60);
    });

    it('should handle empty token list', () => {
      const stats = tokenDetectionEngine.getStats();

      expect(stats.totalDetected).toBe(0);
      expect(stats.safeTokens).toBe(0);
      expect(stats.riskyTokens).toBe(0);
      expect(stats.averageSafetyScore).toBe(0);
    });
  });

  describe('clearOldDetections', () => {
    it('should clear old detections', () => {
      const now = Date.now();
      const recentToken = { ...mockToken, detectedAt: now - 1000 }; // 1 second ago
      const oldToken = { ...mockToken, mint: 'OldToken', detectedAt: now - 8 * 24 * 60 * 60 * 1000 }; // 8 days ago

      tokenDetectionEngine['detectedTokens'].set('RecentToken', recentToken);
      tokenDetectionEngine['detectedTokens'].set('OldToken', oldToken);

      const cleared = tokenDetectionEngine.clearOldDetections();

      expect(cleared).toBe(1);
      expect(tokenDetectionEngine['detectedTokens'].has('RecentToken')).toBe(true);
      expect(tokenDetectionEngine['detectedTokens'].has('OldToken')).toBe(false);
    });
  });

  describe('passesFilters', () => {
    it('should pass all filters', () => {
      const filters = {
        minLiquidity: 0.5,
        maxSupply: 2000000000,
        minSafetyScore: 70,
        excludeKnownTokens: false,
      };

      const result = tokenDetectionEngine['passesFilters'](mockToken, filters);

      expect(result).toBe(true);
    });

    it('should fail liquidity filter', () => {
      const tokenWithLiquidity = {
        ...mockToken,
        liquidityPools: [{ dex: 'test', address: 'test', liquidity: 0.3, volume24h: 0, locked: false }],
      };

      const filters = { minLiquidity: 0.5 };

      const result = tokenDetectionEngine['passesFilters'](tokenWithLiquidity, filters);

      expect(result).toBe(false);
    });

    it('should fail supply filter', () => {
      const filters = { maxSupply: 500000000 };

      const result = tokenDetectionEngine['passesFilters'](mockToken, filters);

      expect(result).toBe(false);
    });

    it('should fail safety score filter', () => {
      const filters = { minSafetyScore: 80 };

      const result = tokenDetectionEngine['passesFilters'](mockToken, filters);

      expect(result).toBe(false);
    });
  });

  describe('calculateSafetyScore', () => {
    it('should calculate high safety score for good token', async () => {
      const goodToken = {
        ...mockToken,
        mintAuthority: null,
        freezeAuthority: null,
        liquidityPools: [
          { dex: 'raydium', address: 'pool1', liquidity: 10, volume24h: 100, locked: true },
        ],
      };

      const score = await tokenDetectionEngine['calculateSafetyScore'](goodToken);

      expect(score).toBeGreaterThan(70);
    });

    it('should calculate low safety score for risky token', async () => {
      const riskyToken = {
        ...mockToken,
        mintAuthority: 'SomeAuthority',
        freezeAuthority: 'SomeAuthority',
        liquidityPools: [],
        riskFactors: ['Mint authority present', 'No liquidity'],
      };

      const score = await tokenDetectionEngine['calculateSafetyScore'](riskyToken);

      expect(score).toBeLessThan(50);
    });
  });
});
