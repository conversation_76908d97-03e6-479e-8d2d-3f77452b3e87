import { createClient } from '@supabase/supabase-js';
import config from '@/config';

// Create Supabase client
export const supabase = createClient(
  config.supabase.url,
  config.supabase.anon<PERSON>ey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  }
);

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          created_at: string;
          updated_at: string;
          last_login_at?: string;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          email: string;
          created_at?: string;
          updated_at?: string;
          last_login_at?: string;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          email?: string;
          created_at?: string;
          updated_at?: string;
          last_login_at?: string;
          is_active?: boolean;
        };
      };
      wallets: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          address: string;
          private_key_encrypted: string;
          is_primary: boolean;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          address: string;
          private_key_encrypted: string;
          is_primary?: boolean;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          address?: string;
          private_key_encrypted?: string;
          is_primary?: boolean;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      trading_settings: {
        Row: {
          id: string;
          user_id: string;
          auto_trading_enabled: boolean;
          max_position_size_sol: number;
          min_safety_score: number;
          max_slippage_percent: number;
          stop_loss_percent: number;
          take_profit_percent: number;
          priority_fee_lamports: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          auto_trading_enabled?: boolean;
          max_position_size_sol?: number;
          min_safety_score?: number;
          max_slippage_percent?: number;
          stop_loss_percent?: number;
          take_profit_percent?: number;
          priority_fee_lamports?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          auto_trading_enabled?: boolean;
          max_position_size_sol?: number;
          min_safety_score?: number;
          max_slippage_percent?: number;
          stop_loss_percent?: number;
          take_profit_percent?: number;
          priority_fee_lamports?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      trades: {
        Row: {
          id: string;
          user_id: string;
          wallet_id: string;
          type: 'buy' | 'sell';
          input_mint: string;
          output_mint: string;
          input_amount: number;
          output_amount: number;
          price_impact: number;
          slippage: number;
          signature: string;
          status: 'pending' | 'confirmed' | 'failed';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          wallet_id: string;
          type: 'buy' | 'sell';
          input_mint: string;
          output_mint: string;
          input_amount: number;
          output_amount: number;
          price_impact: number;
          slippage: number;
          signature: string;
          status?: 'pending' | 'confirmed' | 'failed';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          wallet_id?: string;
          type?: 'buy' | 'sell';
          input_mint?: string;
          output_mint?: string;
          input_amount?: number;
          output_amount?: number;
          price_impact?: number;
          slippage?: number;
          signature?: string;
          status?: 'pending' | 'confirmed' | 'failed';
          created_at?: string;
          updated_at?: string;
        };
      };
      detected_tokens: {
        Row: {
          id: string;
          token_address: string;
          pair_address: string;
          token_name?: string;
          token_symbol?: string;
          initial_liquidity_sol?: number;
          current_price?: number;
          holders_count?: number;
          creation_timestamp?: string;
          mint_authority?: string;
          freeze_authority?: string;
          is_honeypot?: boolean;
          can_sell?: boolean;
          liquidity_locked?: boolean;
          detected_at: string;
          safety_score?: number;
          risk_factors?: any;
          liquidity_pools?: any;
          metadata?: any;
          decimals?: number;
          supply?: number;
        };
        Insert: {
          id?: string;
          token_address: string;
          pair_address: string;
          token_name?: string;
          token_symbol?: string;
          initial_liquidity_sol?: number;
          current_price?: number;
          holders_count?: number;
          creation_timestamp?: string;
          mint_authority?: string;
          freeze_authority?: string;
          is_honeypot?: boolean;
          can_sell?: boolean;
          liquidity_locked?: boolean;
          detected_at?: string;
          safety_score?: number;
          risk_factors?: any;
          liquidity_pools?: any;
          metadata?: any;
          decimals?: number;
          supply?: number;
        };
        Update: {
          id?: string;
          token_address?: string;
          pair_address?: string;
          token_name?: string;
          token_symbol?: string;
          initial_liquidity_sol?: number;
          current_price?: number;
          holders_count?: number;
          creation_timestamp?: string;
          mint_authority?: string;
          freeze_authority?: string;
          is_honeypot?: boolean;
          can_sell?: boolean;
          liquidity_locked?: boolean;
          detected_at?: string;
          safety_score?: number;
          risk_factors?: any;
          liquidity_pools?: any;
          metadata?: any;
          decimals?: number;
          supply?: number;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Helper functions for common operations
export const supabaseHelpers = {
  /**
   * Get current user
   */
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  /**
   * Sign in with email and password
   */
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) throw error;
    return data;
  },

  /**
   * Sign up with email and password
   */
  async signUp(email: string, password: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });
    if (error) throw error;
    return data;
  },

  /**
   * Sign out
   */
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  /**
   * Reset password
   */
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) throw error;
  },

  /**
   * Update password
   */
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({ password });
    if (error) throw error;
  },

  /**
   * Subscribe to auth changes
   */
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  },
};

export type { Database };
export default supabase;
