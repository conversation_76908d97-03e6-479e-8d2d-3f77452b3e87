/**
 * Input validation and sanitization utilities
 */

export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'solana-address' | 'private-key';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  sanitize?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: any;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

/**
 * Validate a single value against a rule
 */
export function validateValue(value: any, rule: ValidationRule, fieldName: string = 'field'): ValidationResult {
  const errors: string[] = [];
  let sanitizedValue = value;

  // Check required
  if (rule.required && (value === undefined || value === null || value === '')) {
    errors.push(`${fieldName} is required`);
    return { isValid: false, errors };
  }

  // Skip further validation if value is empty and not required
  if (!rule.required && (value === undefined || value === null || value === '')) {
    return { isValid: true, errors: [], sanitizedValue: value };
  }

  // Sanitize if requested
  if (rule.sanitize) {
    sanitizedValue = sanitizeInput(value);
  }

  // Type validation
  if (rule.type) {
    const typeResult = validateType(sanitizedValue, rule.type, fieldName);
    if (!typeResult.isValid) {
      errors.push(...typeResult.errors);
    }
  }

  // String validations
  if (typeof sanitizedValue === 'string') {
    if (rule.minLength !== undefined && sanitizedValue.length < rule.minLength) {
      errors.push(`${fieldName} must be at least ${rule.minLength} characters long`);
    }
    if (rule.maxLength !== undefined && sanitizedValue.length > rule.maxLength) {
      errors.push(`${fieldName} must be no more than ${rule.maxLength} characters long`);
    }
    if (rule.pattern && !rule.pattern.test(sanitizedValue)) {
      errors.push(`${fieldName} format is invalid`);
    }
  }

  // Number validations
  if (typeof sanitizedValue === 'number') {
    if (rule.min !== undefined && sanitizedValue < rule.min) {
      errors.push(`${fieldName} must be at least ${rule.min}`);
    }
    if (rule.max !== undefined && sanitizedValue > rule.max) {
      errors.push(`${fieldName} must be no more than ${rule.max}`);
    }
  }

  // Custom validation
  if (rule.custom) {
    const customResult = rule.custom(sanitizedValue);
    if (customResult !== true) {
      errors.push(typeof customResult === 'string' ? customResult : `${fieldName} is invalid`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue,
  };
}

/**
 * Validate an object against a schema
 */
export function validateObject(obj: any, schema: ValidationSchema): ValidationResult & { sanitizedObject?: any } {
  const errors: string[] = [];
  const sanitizedObject: any = {};

  for (const [fieldName, rule] of Object.entries(schema)) {
    const value = obj[fieldName];
    const result = validateValue(value, rule, fieldName);
    
    if (!result.isValid) {
      errors.push(...result.errors);
    }
    
    if (result.sanitizedValue !== undefined) {
      sanitizedObject[fieldName] = result.sanitizedValue;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedObject: errors.length === 0 ? sanitizedObject : undefined,
  };
}

/**
 * Validate type
 */
function validateType(value: any, type: ValidationRule['type'], fieldName: string): ValidationResult {
  const errors: string[] = [];

  switch (type) {
    case 'string':
      if (typeof value !== 'string') {
        errors.push(`${fieldName} must be a string`);
      }
      break;

    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        errors.push(`${fieldName} must be a valid number`);
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') {
        errors.push(`${fieldName} must be a boolean`);
      }
      break;

    case 'email':
      if (typeof value !== 'string' || !isValidEmail(value)) {
        errors.push(`${fieldName} must be a valid email address`);
      }
      break;

    case 'url':
      if (typeof value !== 'string' || !isValidUrl(value)) {
        errors.push(`${fieldName} must be a valid URL`);
      }
      break;

    case 'solana-address':
      if (typeof value !== 'string' || !isValidSolanaAddress(value)) {
        errors.push(`${fieldName} must be a valid Solana address`);
      }
      break;

    case 'private-key':
      if (typeof value !== 'string' || !isValidPrivateKey(value)) {
        errors.push(`${fieldName} must be a valid private key`);
      }
      break;
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Sanitize input to prevent XSS and other attacks
 */
export function sanitizeInput(input: any): any {
  if (typeof input !== 'string') {
    return input;
  }

  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate Solana address format
 */
export function isValidSolanaAddress(address: string): boolean {
  // Solana addresses are base58 encoded and 32-44 characters long
  const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
  return base58Regex.test(address);
}

/**
 * Validate private key format
 */
export function isValidPrivateKey(key: string): boolean {
  // Check for base58 format (Solana standard)
  if (/^[1-9A-HJ-NP-Za-km-z]{87,88}$/.test(key)) {
    return true;
  }
  
  // Check for array format [1,2,3,...]
  if (key.startsWith('[') && key.endsWith(']')) {
    try {
      const array = JSON.parse(key);
      return Array.isArray(array) && array.length === 64 && array.every(n => typeof n === 'number' && n >= 0 && n <= 255);
    } catch {
      return false;
    }
  }
  
  return false;
}

/**
 * Validate trading parameters
 */
export const tradingValidationSchema: ValidationSchema = {
  amount: {
    required: true,
    type: 'number',
    min: 0.001,
    max: 1000,
  },
  slippage: {
    required: true,
    type: 'number',
    min: 0.1,
    max: 50,
  },
  inputMint: {
    required: true,
    type: 'solana-address',
  },
  outputMint: {
    required: true,
    type: 'solana-address',
  },
  priorityFee: {
    type: 'number',
    min: 0,
    max: 100000,
  },
};

/**
 * Validate wallet creation parameters
 */
export const walletValidationSchema: ValidationSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 50,
    sanitize: true,
  },
  password: {
    required: true,
    type: 'string',
    minLength: 8,
    maxLength: 128,
    custom: (value: string) => {
      // Password strength validation
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumbers = /\d/.test(value);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
      
      if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
        return 'Password must contain uppercase, lowercase, numbers, and special characters';
      }
      return true;
    },
  },
  privateKey: {
    type: 'private-key',
  },
};

/**
 * Validate user registration parameters
 */
export const userRegistrationSchema: ValidationSchema = {
  email: {
    required: true,
    type: 'email',
    sanitize: true,
  },
  password: {
    required: true,
    type: 'string',
    minLength: 8,
    maxLength: 128,
    custom: (value: string) => {
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumbers = /\d/.test(value);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
      
      if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
        return 'Password must contain uppercase, lowercase, numbers, and special characters';
      }
      return true;
    },
  },
};

/**
 * Validate bot settings
 */
export const botSettingsSchema: ValidationSchema = {
  maxPositionSizeSol: {
    required: true,
    type: 'number',
    min: 0.01,
    max: 100,
  },
  minSafetyScore: {
    required: true,
    type: 'number',
    min: 0,
    max: 100,
  },
  maxSlippagePercent: {
    required: true,
    type: 'number',
    min: 0.1,
    max: 50,
  },
  stopLossPercent: {
    required: true,
    type: 'number',
    min: 1,
    max: 90,
  },
  takeProfitPercent: {
    required: true,
    type: 'number',
    min: 1,
    max: 1000,
  },
  priorityFeeLamports: {
    required: true,
    type: 'number',
    min: 1000,
    max: 100000,
  },
};
