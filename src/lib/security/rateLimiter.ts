/**
 * Rate limiting system for API calls and user actions
 */

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (identifier: string) => string;
  onLimitReached?: (identifier: string) => void;
}

export interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalRequests: number;
}

class RateLimiter {
  private store = new Map<string, RateLimitEntry>();
  private config: Required<RateLimitConfig>;
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(config: RateLimitConfig) {
    this.config = {
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      keyGenerator: (id: string) => id,
      onLimitReached: () => {},
      ...config,
    };

    // Start cleanup timer
    this.startCleanupTimer();
  }

  /**
   * Check if request is allowed
   */
  checkLimit(identifier: string): RateLimitResult {
    const key = this.config.keyGenerator(identifier);
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    let entry = this.store.get(key);

    // Create new entry if doesn't exist or window has passed
    if (!entry || entry.resetTime <= now) {
      entry = {
        count: 0,
        resetTime: now + this.config.windowMs,
        firstRequest: now,
      };
      this.store.set(key, entry);
    }

    // Check if limit exceeded
    if (entry.count >= this.config.maxRequests) {
      this.config.onLimitReached(identifier);
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        totalRequests: entry.count,
      };
    }

    // Increment counter
    entry.count++;

    return {
      allowed: true,
      remaining: this.config.maxRequests - entry.count,
      resetTime: entry.resetTime,
      totalRequests: entry.count,
    };
  }

  /**
   * Record a successful request
   */
  recordSuccess(identifier: string): void {
    if (this.config.skipSuccessfulRequests) {
      return;
    }
    // Success is already recorded in checkLimit
  }

  /**
   * Record a failed request
   */
  recordFailure(identifier: string): void {
    if (this.config.skipFailedRequests) {
      const key = this.config.keyGenerator(identifier);
      const entry = this.store.get(key);
      if (entry && entry.count > 0) {
        entry.count--;
      }
    }
  }

  /**
   * Reset limit for identifier
   */
  reset(identifier: string): void {
    const key = this.config.keyGenerator(identifier);
    this.store.delete(key);
  }

  /**
   * Get current status for identifier
   */
  getStatus(identifier: string): RateLimitResult | null {
    const key = this.config.keyGenerator(identifier);
    const entry = this.store.get(key);

    if (!entry) {
      return null;
    }

    const now = Date.now();
    if (entry.resetTime <= now) {
      this.store.delete(key);
      return null;
    }

    return {
      allowed: entry.count < this.config.maxRequests,
      remaining: Math.max(0, this.config.maxRequests - entry.count),
      resetTime: entry.resetTime,
      totalRequests: entry.count,
    };
  }

  /**
   * Get all active limits
   */
  getAllLimits(): Record<string, RateLimitEntry> {
    const now = Date.now();
    const active: Record<string, RateLimitEntry> = {};

    for (const [key, entry] of this.store.entries()) {
      if (entry.resetTime > now) {
        active[key] = { ...entry };
      }
    }

    return active;
  }

  /**
   * Clear expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let cleared = 0;

    for (const [key, entry] of this.store.entries()) {
      if (entry.resetTime <= now) {
        this.store.delete(key);
        cleared++;
      }
    }

    return cleared;
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.windowMs);
  }

  /**
   * Stop cleanup timer
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Destroy rate limiter
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.store.clear();
  }
}

// Pre-configured rate limiters for different use cases
export const apiRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // 100 requests per minute
  onLimitReached: (id) => {
    console.warn(`API rate limit exceeded for: ${id}`);
  },
});

export const tradingRateLimiter = new RateLimiter({
  windowMs: 10 * 1000, // 10 seconds
  maxRequests: 5, // 5 trades per 10 seconds
  onLimitReached: (id) => {
    console.warn(`Trading rate limit exceeded for: ${id}`);
  },
});

export const authRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 auth attempts per 15 minutes
  skipSuccessfulRequests: true,
  onLimitReached: (id) => {
    console.warn(`Auth rate limit exceeded for: ${id}`);
  },
});

export const walletRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10, // 10 wallet operations per minute
  onLimitReached: (id) => {
    console.warn(`Wallet rate limit exceeded for: ${id}`);
  },
});

/**
 * Rate limiting middleware for async functions
 */
export function withRateLimit<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  rateLimiter: RateLimiter,
  getIdentifier: (...args: Parameters<T>) => string
): T {
  return (async (...args: Parameters<T>) => {
    const identifier = getIdentifier(...args);
    const result = rateLimiter.checkLimit(identifier);

    if (!result.allowed) {
      const error = new Error(`Rate limit exceeded. Try again in ${Math.ceil((result.resetTime - Date.now()) / 1000)} seconds.`);
      (error as any).rateLimitInfo = result;
      throw error;
    }

    try {
      const response = await fn(...args);
      rateLimiter.recordSuccess(identifier);
      return response;
    } catch (error) {
      rateLimiter.recordFailure(identifier);
      throw error;
    }
  }) as T;
}

/**
 * Rate limiting decorator for classes
 */
export function RateLimit(
  rateLimiter: RateLimiter,
  getIdentifier?: (...args: any[]) => string
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const identifier = getIdentifier ? getIdentifier(...args) : 'default';
      const result = rateLimiter.checkLimit(identifier);

      if (!result.allowed) {
        const error = new Error(`Rate limit exceeded for ${propertyKey}. Try again in ${Math.ceil((result.resetTime - Date.now()) / 1000)} seconds.`);
        (error as any).rateLimitInfo = result;
        throw error;
      }

      try {
        const response = await originalMethod.apply(this, args);
        rateLimiter.recordSuccess(identifier);
        return response;
      } catch (error) {
        rateLimiter.recordFailure(identifier);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * React hook for rate limiting
 */
export function useRateLimit(rateLimiter: RateLimiter, identifier: string) {
  const checkLimit = () => rateLimiter.checkLimit(identifier);
  const getStatus = () => rateLimiter.getStatus(identifier);
  const reset = () => rateLimiter.reset(identifier);

  return {
    checkLimit,
    getStatus,
    reset,
  };
}

export { RateLimiter };
