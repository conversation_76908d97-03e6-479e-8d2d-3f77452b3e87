import axios, { AxiosInstance } from 'axios';
import { jupiterAPI, PriceInfo } from '../jupiter/api';
import config from '@/config';

export interface TokenPrice {
  mint: string;
  symbol: string;
  price: number;
  priceChange24h?: number;
  priceChangePercent24h?: number;
  volume24h?: number;
  marketCap?: number;
  lastUpdated: number;
  source: 'jupiter' | 'dexscreener' | 'coingecko' | 'birdeye';
}

export interface PriceHistory {
  timestamp: number;
  price: number;
  volume?: number;
}

export interface DexScreenerToken {
  chainId: string;
  dexId: string;
  url: string;
  pairAddress: string;
  baseToken: {
    address: string;
    name: string;
    symbol: string;
  };
  quoteToken: {
    address: string;
    name: string;
    symbol: string;
  };
  priceNative: string;
  priceUsd?: string;
  txns: {
    m5: { buys: number; sells: number };
    h1: { buys: number; sells: number };
    h6: { buys: number; sells: number };
    h24: { buys: number; sells: number };
  };
  volume: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  priceChange: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  liquidity?: {
    usd?: number;
    base: number;
    quote: number;
  };
  fdv?: number;
  marketCap?: number;
  pairCreatedAt?: number;
}

export interface CoinGeckoPrice {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export class PriceFeedManager {
  private dexScreenerAPI: AxiosInstance;
  private coinGeckoAPI: AxiosInstance;
  private priceCache: Map<string, { price: TokenPrice; expiry: number }> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 seconds

  constructor() {
    this.dexScreenerAPI = axios.create({
      baseURL: config.apis.dexscreener,
      timeout: 10000,
    });

    this.coinGeckoAPI = axios.create({
      baseURL: config.apis.coingecko,
      timeout: 10000,
    });
  }

  /**
   * Get token price from multiple sources with fallback
   */
  async getTokenPrice(mint: string, forceRefresh: boolean = false): Promise<TokenPrice | null> {
    // Check cache first
    if (!forceRefresh) {
      const cached = this.priceCache.get(mint);
      if (cached && cached.expiry > Date.now()) {
        return cached.price;
      }
    }

    // Try Jupiter first (fastest)
    try {
      const jupiterPrice = await this.getJupiterPrice(mint);
      if (jupiterPrice) {
        this.cachePrice(mint, jupiterPrice);
        return jupiterPrice;
      }
    } catch (error) {
      console.warn('Jupiter price fetch failed:', error);
    }

    // Try DexScreener
    try {
      const dexScreenerPrice = await this.getDexScreenerPrice(mint);
      if (dexScreenerPrice) {
        this.cachePrice(mint, dexScreenerPrice);
        return dexScreenerPrice;
      }
    } catch (error) {
      console.warn('DexScreener price fetch failed:', error);
    }

    // Try CoinGecko (if we have the mapping)
    try {
      const coinGeckoPrice = await this.getCoinGeckoPrice(mint);
      if (coinGeckoPrice) {
        this.cachePrice(mint, coinGeckoPrice);
        return coinGeckoPrice;
      }
    } catch (error) {
      console.warn('CoinGecko price fetch failed:', error);
    }

    return null;
  }

  /**
   * Get multiple token prices
   */
  async getTokenPrices(mints: string[]): Promise<Record<string, TokenPrice>> {
    const prices: Record<string, TokenPrice> = {};
    
    // Try to get all prices from Jupiter first
    try {
      const jupiterPrices = await jupiterAPI.getPrices(mints);
      for (const [mint, priceInfo] of Object.entries(jupiterPrices)) {
        const tokenPrice = this.convertJupiterPrice(mint, priceInfo);
        if (tokenPrice) {
          prices[mint] = tokenPrice;
          this.cachePrice(mint, tokenPrice);
        }
      }
    } catch (error) {
      console.warn('Bulk Jupiter price fetch failed:', error);
    }

    // Fill in missing prices individually
    const missingMints = mints.filter(mint => !prices[mint]);
    for (const mint of missingMints) {
      try {
        const price = await this.getTokenPrice(mint);
        if (price) {
          prices[mint] = price;
        }
      } catch (error) {
        console.warn(`Failed to get price for ${mint}:`, error);
      }
    }

    return prices;
  }

  /**
   * Get Jupiter price
   */
  private async getJupiterPrice(mint: string): Promise<TokenPrice | null> {
    try {
      const priceInfo = await jupiterAPI.getPrice(mint);
      return this.convertJupiterPrice(mint, priceInfo);
    } catch (error) {
      return null;
    }
  }

  /**
   * Get DexScreener price
   */
  private async getDexScreenerPrice(mint: string): Promise<TokenPrice | null> {
    try {
      const response = await this.dexScreenerAPI.get(`/dex/tokens/${mint}`);
      const data = response.data;
      
      if (!data.pairs || data.pairs.length === 0) {
        return null;
      }

      // Get the pair with highest liquidity
      const bestPair = data.pairs.reduce((best: DexScreenerToken, current: DexScreenerToken) => {
        const bestLiquidity = best.liquidity?.usd || 0;
        const currentLiquidity = current.liquidity?.usd || 0;
        return currentLiquidity > bestLiquidity ? current : best;
      });

      return {
        mint,
        symbol: bestPair.baseToken.symbol,
        price: parseFloat(bestPair.priceUsd || '0'),
        priceChange24h: bestPair.priceChange.h24,
        priceChangePercent24h: bestPair.priceChange.h24,
        volume24h: bestPair.volume.h24,
        marketCap: bestPair.marketCap,
        lastUpdated: Date.now(),
        source: 'dexscreener',
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Get CoinGecko price (requires token ID mapping)
   */
  private async getCoinGeckoPrice(mint: string): Promise<TokenPrice | null> {
    try {
      // This would require a mapping from Solana mint to CoinGecko ID
      // For now, return null as we don't have this mapping
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Convert Jupiter price info to TokenPrice
   */
  private convertJupiterPrice(mint: string, priceInfo: PriceInfo): TokenPrice | null {
    if (!priceInfo || priceInfo.price === 0) {
      return null;
    }

    return {
      mint,
      symbol: priceInfo.mintSymbol,
      price: priceInfo.price,
      lastUpdated: Date.now(),
      source: 'jupiter',
    };
  }

  /**
   * Cache price data
   */
  private cachePrice(mint: string, price: TokenPrice): void {
    this.priceCache.set(mint, {
      price,
      expiry: Date.now() + this.CACHE_DURATION,
    });
  }

  /**
   * Get price history for a token
   */
  async getPriceHistory(
    mint: string,
    timeframe: '1h' | '24h' | '7d' | '30d' = '24h'
  ): Promise<PriceHistory[]> {
    try {
      // This would typically require a more sophisticated price history API
      // For now, return empty array
      return [];
    } catch (error) {
      console.error('Failed to get price history:', error);
      return [];
    }
  }

  /**
   * Subscribe to real-time price updates
   */
  subscribeToPriceUpdates(
    mints: string[],
    callback: (mint: string, price: TokenPrice) => void,
    interval: number = 30000
  ): () => void {
    const intervalId = setInterval(async () => {
      for (const mint of mints) {
        try {
          const price = await this.getTokenPrice(mint, true);
          if (price) {
            callback(mint, price);
          }
        } catch (error) {
          console.warn(`Failed to update price for ${mint}:`, error);
        }
      }
    }, interval);

    return () => clearInterval(intervalId);
  }

  /**
   * Calculate price change percentage
   */
  calculatePriceChange(currentPrice: number, previousPrice: number): number {
    if (previousPrice === 0) return 0;
    return ((currentPrice - previousPrice) / previousPrice) * 100;
  }

  /**
   * Format price for display
   */
  formatPrice(price: number, decimals: number = 6): string {
    if (price === 0) return '0';
    
    if (price < 0.000001) {
      return price.toExponential(2);
    } else if (price < 0.01) {
      return price.toFixed(6);
    } else if (price < 1) {
      return price.toFixed(4);
    } else {
      return price.toFixed(2);
    }
  }

  /**
   * Get market data summary
   */
  async getMarketSummary(mints: string[]): Promise<{
    totalValue: number;
    topGainers: TokenPrice[];
    topLosers: TokenPrice[];
    highestVolume: TokenPrice[];
  }> {
    const prices = await this.getTokenPrices(mints);
    const priceArray = Object.values(prices);

    const topGainers = priceArray
      .filter(p => p.priceChangePercent24h !== undefined)
      .sort((a, b) => (b.priceChangePercent24h || 0) - (a.priceChangePercent24h || 0))
      .slice(0, 5);

    const topLosers = priceArray
      .filter(p => p.priceChangePercent24h !== undefined)
      .sort((a, b) => (a.priceChangePercent24h || 0) - (b.priceChangePercent24h || 0))
      .slice(0, 5);

    const highestVolume = priceArray
      .filter(p => p.volume24h !== undefined)
      .sort((a, b) => (b.volume24h || 0) - (a.volume24h || 0))
      .slice(0, 5);

    const totalValue = priceArray.reduce((sum, price) => sum + price.price, 0);

    return {
      totalValue,
      topGainers,
      topLosers,
      highestVolume,
    };
  }

  /**
   * Clear price cache
   */
  clearCache(): void {
    this.priceCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.priceCache.size,
      hitRate: 0, // Would need to track hits/misses
    };
  }
}

// Export singleton instance
export const priceFeedManager = new PriceFeedManager();

// Export convenience functions
export const getTokenPrice = (mint: string, forceRefresh?: boolean) => 
  priceFeedManager.getTokenPrice(mint, forceRefresh);
export const getTokenPrices = (mints: string[]) => priceFeedManager.getTokenPrices(mints);
export const subscribeToPriceUpdates = (
  mints: string[], 
  callback: (mint: string, price: TokenPrice) => void,
  interval?: number
) => priceFeedManager.subscribeToPriceUpdates(mints, callback, interval);
export const formatPrice = (price: number, decimals?: number) => 
  priceFeedManager.formatPrice(price, decimals);
