import { PublicKey } from '@solana/web3.js';

// Native SOL token mint (wrapped SOL)
export const NATIVE_SOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');

// Common token mints on Solana
export const TOKEN_MINTS = {
  SOL: NATIVE_SOL_MINT,
  USDC: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'),
  USDT: new PublicKey('Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'),
  RAY: new PublicKey('4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R'),
  SRM: new PublicKey('SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt'),
  ORCA: new PublicKey('orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE'),
  MNGO: new PublicKey('MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac'),
} as const;

// DEX Program IDs
export const DEX_PROGRAM_IDS = {
  RAYDIUM_V4: new PublicKey('675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8'),
  RAYDIUM_V5: new PublicKey('5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h'),
  ORCA: new PublicKey('9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP'),
  SERUM_V3: new PublicKey('9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin'),
  JUPITER_V6: new PublicKey('JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4'),
} as const;

// Token Program IDs
export const TOKEN_PROGRAM_IDS = {
  TOKEN_PROGRAM: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'),
  TOKEN_2022_PROGRAM: new PublicKey('TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb'),
  ASSOCIATED_TOKEN_PROGRAM: new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'),
} as const;

// System Program IDs
export const SYSTEM_PROGRAM_IDS = {
  SYSTEM_PROGRAM: new PublicKey('11111111111111111111111111111111'),
  RENT_PROGRAM: new PublicKey('SysvarRent111111111111111111111111111111111'),
  CLOCK_PROGRAM: new PublicKey('SysvarC1ock11111111111111111111111111111111'),
} as const;

// Common transaction fees (in lamports)
export const TRANSACTION_FEES = {
  BASE_FEE: 5000, // 0.000005 SOL
  PRIORITY_FEE_LOW: 1000, // 0.000001 SOL
  PRIORITY_FEE_MEDIUM: 5000, // 0.000005 SOL
  PRIORITY_FEE_HIGH: 10000, // 0.00001 SOL
  PRIORITY_FEE_ULTRA: 50000, // 0.00005 SOL
} as const;

// Solana constants
export const SOLANA_CONSTANTS = {
  LAMPORTS_PER_SOL: 1_000_000_000,
  MAX_TRANSACTION_SIZE: 1232,
  MAX_ACCOUNTS_PER_TRANSACTION: 64,
  BLOCKHASH_CACHE_TIMEOUT: 150000, // 2.5 minutes
  CONFIRMATION_TIMEOUT: 60000, // 1 minute
} as const;

// Token decimals for common tokens
export const TOKEN_DECIMALS = {
  SOL: 9,
  USDC: 6,
  USDT: 6,
  RAY: 6,
  SRM: 6,
  ORCA: 6,
  MNGO: 6,
} as const;

// Slippage constants
export const SLIPPAGE = {
  MIN: 0.1, // 0.1%
  LOW: 0.5, // 0.5%
  MEDIUM: 1.0, // 1%
  HIGH: 3.0, // 3%
  MAX: 10.0, // 10%
} as const;

// Trading limits
export const TRADING_LIMITS = {
  MIN_SOL_AMOUNT: 0.001, // Minimum 0.001 SOL
  MAX_SOL_AMOUNT: 1000, // Maximum 1000 SOL per trade
  MIN_TOKEN_AMOUNT: 1, // Minimum 1 token unit
  MAX_SLIPPAGE_PERCENT: 50, // Maximum 50% slippage
} as const;

// Network-specific constants
export const NETWORK_CONSTANTS = {
  MAINNET: {
    CLUSTER: 'mainnet-beta',
    EXPLORER_URL: 'https://explorer.solana.com',
    SOLSCAN_URL: 'https://solscan.io',
  },
  DEVNET: {
    CLUSTER: 'devnet',
    EXPLORER_URL: 'https://explorer.solana.com',
    SOLSCAN_URL: 'https://solscan.io',
  },
  TESTNET: {
    CLUSTER: 'testnet',
    EXPLORER_URL: 'https://explorer.solana.com',
    SOLSCAN_URL: 'https://solscan.io',
  },
} as const;

// Jupiter-specific constants
export const JUPITER_CONSTANTS = {
  API_V6_URL: 'https://quote-api.jup.ag/v6',
  PRICE_API_URL: 'https://price.jup.ag/v4',
  PROGRAM_ID: new PublicKey('JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4'),
  MAX_ACCOUNTS: 28, // Jupiter v6 limit
  DEFAULT_SLIPPAGE_BPS: 50, // 0.5%
} as const;

// Token safety constants
export const TOKEN_SAFETY = {
  MIN_LIQUIDITY_USD: 1000,
  MIN_HOLDERS: 10,
  MAX_SUPPLY: 1_000_000_000_000,
  HONEYPOT_INDICATORS: [
    'cannot_sell',
    'high_tax',
    'mint_authority',
    'freeze_authority',
    'low_liquidity',
  ],
} as const;

// Rate limiting constants
export const RATE_LIMITS = {
  RPC_REQUESTS_PER_SECOND: 10,
  API_REQUESTS_PER_MINUTE: 60,
  WEBSOCKET_RECONNECT_DELAY: 5000,
  MAX_RETRY_ATTEMPTS: 3,
} as const;

// Export utility functions
export const lamportsToSol = (lamports: number): number => {
  return lamports / SOLANA_CONSTANTS.LAMPORTS_PER_SOL;
};

export const solToLamports = (sol: number): number => {
  return Math.floor(sol * SOLANA_CONSTANTS.LAMPORTS_PER_SOL);
};

export const formatTokenAmount = (amount: number, decimals: number): number => {
  return amount / Math.pow(10, decimals);
};

export const parseTokenAmount = (amount: number, decimals: number): number => {
  return Math.floor(amount * Math.pow(10, decimals));
};
