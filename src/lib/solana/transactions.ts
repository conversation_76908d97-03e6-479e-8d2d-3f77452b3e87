import {
  Transaction,
  SystemProgram,
  <PERSON>Key,
  TransactionInstruction,
  sendAndConfirmTransaction,
  Keypair,
  ComputeBudgetProgram,
  TransactionMessage,
  VersionedTransaction,
  AddressLookupTableAccount,
} from '@solana/web3.js';
import {
  createTransferInstruction,
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  TOKEN_PROGRAM_ID,
  ASSOCIATED_TOKEN_PROGRAM_ID,
} from '@solana/spl-token';
import { getConnection } from './connection';
import { SOLANA_CONSTANTS, TRANSACTION_FEES } from './constants';
import { config } from '@/config';

export interface TransactionOptions {
  priorityFee?: number;
  computeUnits?: number;
  skipPreflight?: boolean;
  maxRetries?: number;
  commitment?: 'processed' | 'confirmed' | 'finalized';
}

export interface TransferOptions extends TransactionOptions {
  amount: number;
  fromKeypair: Keypair;
  toAddress: string;
  tokenMint?: string; // If undefined, transfer SOL
}

export interface SwapTransactionParams {
  inputMint: string;
  outputMint: string;
  amount: number;
  slippageBps: number;
  userPublicKey: string;
  wrapUnwrapSOL?: boolean;
}

export interface TransactionResult {
  signature: string;
  success: boolean;
  error?: string;
  slot?: number;
  confirmationStatus?: string;
}

export class TransactionManager {
  private connection = getConnection();

  /**
   * Create a priority fee instruction
   */
  private createPriorityFeeInstruction(microLamports: number): TransactionInstruction {
    return ComputeBudgetProgram.setComputeUnitPrice({
      microLamports,
    });
  }

  /**
   * Create a compute budget instruction
   */
  private createComputeBudgetInstruction(units: number): TransactionInstruction {
    return ComputeBudgetProgram.setComputeUnitLimit({
      units,
    });
  }

  /**
   * Get recent blockhash with retry logic
   */
  private async getRecentBlockhash(maxRetries: number = 3) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        const { blockhash, lastValidBlockHeight } = await this.connection.getLatestBlockhash();
        return { blockhash, lastValidBlockHeight };
      } catch (error) {
        if (i === maxRetries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
    throw new Error('Failed to get recent blockhash');
  }

  /**
   * Send and confirm transaction with retry logic
   */
  async sendAndConfirmTransactionWithRetry(
    transaction: Transaction,
    signers: Keypair[],
    options: TransactionOptions = {}
  ): Promise<TransactionResult> {
    const {
      maxRetries = 3,
      commitment = 'confirmed',
      skipPreflight = false,
    } = options;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        // Get fresh blockhash for each attempt
        const { blockhash, lastValidBlockHeight } = await this.getRecentBlockhash();
        transaction.recentBlockhash = blockhash;
        transaction.lastValidBlockHeight = lastValidBlockHeight;

        const signature = await sendAndConfirmTransaction(
          this.connection,
          transaction,
          signers,
          {
            commitment,
            skipPreflight,
            maxRetries: 1, // We handle retries ourselves
          }
        );

        // Get confirmation status
        const status = await this.connection.getSignatureStatus(signature);
        
        return {
          signature,
          success: true,
          slot: status.value?.slot,
          confirmationStatus: status.value?.confirmationStatus || 'confirmed',
        };
      } catch (error) {
        console.warn(`Transaction attempt ${attempt + 1} failed:`, error);
        
        if (attempt === maxRetries - 1) {
          return {
            signature: '',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
      }
    }

    return {
      signature: '',
      success: false,
      error: 'Max retries exceeded',
    };
  }

  /**
   * Transfer SOL between accounts
   */
  async transferSOL(options: TransferOptions): Promise<TransactionResult> {
    try {
      const { amount, fromKeypair, toAddress, priorityFee, computeUnits } = options;
      const toPublicKey = new PublicKey(toAddress);
      const lamports = Math.floor(amount * SOLANA_CONSTANTS.LAMPORTS_PER_SOL);

      const transaction = new Transaction();

      // Add priority fee if specified
      if (priorityFee) {
        const priorityFeeIx = this.createPriorityFeeInstruction(priorityFee);
        transaction.add(priorityFeeIx);
      }

      // Add compute budget if specified
      if (computeUnits) {
        const computeBudgetIx = this.createComputeBudgetInstruction(computeUnits);
        transaction.add(computeBudgetIx);
      }

      // Add transfer instruction
      const transferIx = SystemProgram.transfer({
        fromPubkey: fromKeypair.publicKey,
        toPubkey: toPublicKey,
        lamports,
      });
      transaction.add(transferIx);

      return await this.sendAndConfirmTransactionWithRetry(transaction, [fromKeypair], options);
    } catch (error) {
      return {
        signature: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Transfer SPL tokens between accounts
   */
  async transferToken(options: TransferOptions & { tokenMint: string }): Promise<TransactionResult> {
    try {
      const { amount, fromKeypair, toAddress, tokenMint, priorityFee, computeUnits } = options;
      const toPublicKey = new PublicKey(toAddress);
      const mintPublicKey = new PublicKey(tokenMint);

      const transaction = new Transaction();

      // Add priority fee if specified
      if (priorityFee) {
        const priorityFeeIx = this.createPriorityFeeInstruction(priorityFee);
        transaction.add(priorityFeeIx);
      }

      // Add compute budget if specified
      if (computeUnits) {
        const computeBudgetIx = this.createComputeBudgetInstruction(computeUnits);
        transaction.add(computeBudgetIx);
      }

      // Get source token account
      const sourceTokenAccount = await getAssociatedTokenAddress(
        mintPublicKey,
        fromKeypair.publicKey
      );

      // Get or create destination token account
      const destinationTokenAccount = await getAssociatedTokenAddress(
        mintPublicKey,
        toPublicKey
      );

      // Check if destination token account exists
      const destinationAccountInfo = await this.connection.getAccountInfo(destinationTokenAccount);
      if (!destinationAccountInfo) {
        // Create destination token account
        const createAccountIx = createAssociatedTokenAccountInstruction(
          fromKeypair.publicKey, // payer
          destinationTokenAccount,
          toPublicKey, // owner
          mintPublicKey
        );
        transaction.add(createAccountIx);
      }

      // Add transfer instruction
      const transferIx = createTransferInstruction(
        sourceTokenAccount,
        destinationTokenAccount,
        fromKeypair.publicKey,
        amount
      );
      transaction.add(transferIx);

      return await this.sendAndConfirmTransactionWithRetry(transaction, [fromKeypair], options);
    } catch (error) {
      return {
        signature: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create a versioned transaction (for Jupiter swaps)
   */
  async createVersionedTransaction(
    instructions: TransactionInstruction[],
    payer: PublicKey,
    lookupTables: AddressLookupTableAccount[] = []
  ): Promise<VersionedTransaction> {
    const { blockhash } = await this.getRecentBlockhash();

    const messageV0 = new TransactionMessage({
      payerKey: payer,
      recentBlockhash: blockhash,
      instructions,
    }).compileToV0Message(lookupTables);

    return new VersionedTransaction(messageV0);
  }

  /**
   * Simulate transaction before sending
   */
  async simulateTransaction(transaction: Transaction): Promise<{
    success: boolean;
    logs?: string[];
    error?: string;
    computeUnitsConsumed?: number;
  }> {
    try {
      const simulation = await this.connection.simulateTransaction(transaction);
      
      return {
        success: !simulation.value.err,
        logs: simulation.value.logs || [],
        error: simulation.value.err ? JSON.stringify(simulation.value.err) : undefined,
        computeUnitsConsumed: simulation.value.unitsConsumed,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Simulation failed',
      };
    }
  }

  /**
   * Get transaction status
   */
  async getTransactionStatus(signature: string): Promise<{
    confirmed: boolean;
    finalized: boolean;
    slot?: number;
    error?: string;
  }> {
    try {
      const status = await this.connection.getSignatureStatus(signature);
      
      return {
        confirmed: status.value?.confirmationStatus === 'confirmed' || 
                  status.value?.confirmationStatus === 'finalized',
        finalized: status.value?.confirmationStatus === 'finalized',
        slot: status.value?.slot,
        error: status.value?.err ? JSON.stringify(status.value.err) : undefined,
      };
    } catch (error) {
      return {
        confirmed: false,
        finalized: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Calculate transaction fee
   */
  async calculateTransactionFee(transaction: Transaction): Promise<number> {
    try {
      const feeCalculator = await this.connection.getFeeForMessage(
        transaction.compileMessage()
      );
      return feeCalculator.value || TRANSACTION_FEES.BASE_FEE;
    } catch (error) {
      console.warn('Failed to calculate transaction fee:', error);
      return TRANSACTION_FEES.BASE_FEE;
    }
  }

  /**
   * Get optimal priority fee based on network conditions
   */
  async getOptimalPriorityFee(): Promise<number> {
    try {
      // This is a simplified implementation
      // In practice, you'd query recent transactions or use a fee estimation service
      const recentPerformanceSamples = await this.connection.getRecentPerformanceSamples(1);
      
      if (recentPerformanceSamples.length > 0) {
        const sample = recentPerformanceSamples[0];
        const avgSlot = sample.samplePeriodSecs;
        
        // Simple heuristic: higher fee for congested network
        if (avgSlot > 1) {
          return TRANSACTION_FEES.PRIORITY_FEE_HIGH;
        } else {
          return TRANSACTION_FEES.PRIORITY_FEE_MEDIUM;
        }
      }
      
      return TRANSACTION_FEES.PRIORITY_FEE_MEDIUM;
    } catch (error) {
      console.warn('Failed to get optimal priority fee:', error);
      return TRANSACTION_FEES.PRIORITY_FEE_MEDIUM;
    }
  }
}

// Export singleton instance
export const transactionManager = new TransactionManager();

// Export convenience functions
export const transferSOL = (options: TransferOptions) => transactionManager.transferSOL(options);
export const transferToken = (options: TransferOptions & { tokenMint: string }) => 
  transactionManager.transferToken(options);
export const simulateTransaction = (transaction: Transaction) => 
  transactionManager.simulateTransaction(transaction);
export const getTransactionStatus = (signature: string) => 
  transactionManager.getTransactionStatus(signature);
