import { Connection, ConnectionConfig, Commitment } from '@solana/web3.js';
import config from '@/config';

// Connection configuration
const CONNECTION_CONFIG: ConnectionConfig = {
  commitment: 'confirmed' as Commitment,
  confirmTransactionInitialTimeout: 60000,
  wsEndpoint: config.solana.wsUrl,
  httpHeaders: {
    'Content-Type': 'application/json',
  },
};

// RPC endpoint management with fallback
class SolanaConnectionManager {
  private connections: Map<string, Connection> = new Map();
  private currentEndpoint: string;
  private fallbackEndpoints: string[] = [];
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.currentEndpoint = config.solana.rpcUrl;
    this.setupFallbackEndpoints();
    this.initializeConnection();
    this.startHealthCheck();
  }

  private setupFallbackEndpoints(): void {
    // Add fallback endpoints based on network
    const network = config.solana.network;
    
    if (network === 'mainnet-beta' || network === 'mainnet') {
      this.fallbackEndpoints = [
        config.solana.rpcUrl,
        'https://api.mainnet-beta.solana.com',
        'https://solana-api.projectserum.com',
        'https://rpc.ankr.com/solana',
      ];
    } else if (network === 'devnet') {
      this.fallbackEndpoints = [
        config.solana.rpcUrl,
        'https://api.devnet.solana.com',
        'https://devnet.genesysgo.net',
      ];
    } else {
      this.fallbackEndpoints = [
        config.solana.rpcUrl,
        'https://api.testnet.solana.com',
      ];
    }

    // Remove duplicates and current endpoint
    this.fallbackEndpoints = [...new Set(this.fallbackEndpoints)]
      .filter(endpoint => endpoint !== this.currentEndpoint);
  }

  private initializeConnection(): void {
    try {
      const connection = new Connection(this.currentEndpoint, CONNECTION_CONFIG);
      this.connections.set(this.currentEndpoint, connection);
      console.log(`✅ Connected to Solana ${config.solana.network} at ${this.currentEndpoint}`);
    } catch (error) {
      console.error('❌ Failed to initialize Solana connection:', error);
      this.switchToFallback();
    }
  }

  private async switchToFallback(): Promise<void> {
    console.log('🔄 Switching to fallback RPC endpoint...');
    
    for (const endpoint of this.fallbackEndpoints) {
      try {
        const connection = new Connection(endpoint, CONNECTION_CONFIG);
        
        // Test the connection
        await connection.getLatestBlockhash();
        
        this.currentEndpoint = endpoint;
        this.connections.set(endpoint, connection);
        console.log(`✅ Successfully switched to fallback: ${endpoint}`);
        return;
      } catch (error) {
        console.warn(`⚠️ Fallback endpoint failed: ${endpoint}`, error);
        continue;
      }
    }
    
    throw new Error('All RPC endpoints failed');
  }

  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const connection = this.getConnection();
        await connection.getLatestBlockhash();
      } catch (error) {
        console.warn('⚠️ Health check failed, switching to fallback:', error);
        await this.switchToFallback();
      }
    }, 30000); // Check every 30 seconds
  }

  public getConnection(): Connection {
    const connection = this.connections.get(this.currentEndpoint);
    if (!connection) {
      throw new Error('No active Solana connection');
    }
    return connection;
  }

  public getCurrentEndpoint(): string {
    return this.currentEndpoint;
  }

  public async getConnectionInfo() {
    const connection = this.getConnection();
    try {
      const [version, blockHeight, latestBlockhash] = await Promise.all([
        connection.getVersion(),
        connection.getBlockHeight(),
        connection.getLatestBlockhash(),
      ]);

      return {
        endpoint: this.currentEndpoint,
        network: config.solana.network,
        version,
        blockHeight,
        latestBlockhash: latestBlockhash.blockhash,
        lastValidBlockHeight: latestBlockhash.lastValidBlockHeight,
      };
    } catch (error) {
      console.error('Failed to get connection info:', error);
      throw error;
    }
  }

  public destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    this.connections.clear();
  }
}

// Create singleton instance
export const connectionManager = new SolanaConnectionManager();

// Export convenience functions
export const getConnection = () => connectionManager.getConnection();
export const getCurrentEndpoint = () => connectionManager.getCurrentEndpoint();
export const getConnectionInfo = () => connectionManager.getConnectionInfo();

// Export the connection directly for backward compatibility
export const connection = connectionManager.getConnection();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    connectionManager.destroy();
  });
}
