import { Keypair, <PERSON>K<PERSON>, <PERSON><PERSON>rogram, Transaction, sendAndConfirmTransaction } from '@solana/web3.js';
import { getAssociatedTokenAddress, createAssociatedTokenAccountInstruction, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import bs58 from 'bs58';
import { getConnection } from './connection';
import { NATIVE_SOL_MINT, SOLANA_CONSTANTS } from './constants';
import { encryptPrivateKey, decryptPrivateKey } from '../crypto/encryption';

export interface WalletInfo {
  publicKey: string;
  privateKey?: string; // Only available when creating/importing
  encryptedPrivateKey?: string;
  balance: number;
  tokenBalances: TokenBalance[];
}

export interface TokenBalance {
  mint: string;
  amount: number;
  decimals: number;
  symbol?: string;
  name?: string;
  uiAmount: number;
}

export interface CreateWalletOptions {
  name?: string;
  encrypt?: boolean;
  password?: string;
}

export interface ImportWalletOptions {
  privateKey: string;
  name?: string;
  encrypt?: boolean;
  password?: string;
}

export class SolanaWallet {
  private keypair: Keypair;
  private connection = getConnection();

  constructor(keypair: Keypair) {
    this.keypair = keypair;
  }

  // Static methods for wallet creation and import
  static generate(): SolanaWallet {
    const keypair = Keypair.generate();
    return new SolanaWallet(keypair);
  }

  static fromPrivateKey(privateKey: string): SolanaWallet {
    try {
      let secretKey: Uint8Array;
      
      // Try different formats
      if (privateKey.startsWith('[') && privateKey.endsWith(']')) {
        // Array format: [1,2,3,...]
        secretKey = new Uint8Array(JSON.parse(privateKey));
      } else if (privateKey.length === 128) {
        // Hex format
        secretKey = new Uint8Array(
          privateKey.match(/.{1,2}/g)?.map(byte => parseInt(byte, 16)) || []
        );
      } else {
        // Base58 format
        secretKey = bs58.decode(privateKey);
      }

      const keypair = Keypair.fromSecretKey(secretKey);
      return new SolanaWallet(keypair);
    } catch (error) {
      throw new Error(`Invalid private key format: ${error}`);
    }
  }

  static fromEncryptedPrivateKey(encryptedPrivateKey: string, password: string): SolanaWallet {
    try {
      const privateKey = decryptPrivateKey(encryptedPrivateKey, password);
      return SolanaWallet.fromPrivateKey(privateKey);
    } catch (error) {
      throw new Error(`Failed to decrypt private key: ${error}`);
    }
  }

  // Getters
  get publicKey(): PublicKey {
    return this.keypair.publicKey;
  }

  get publicKeyString(): string {
    return this.keypair.publicKey.toBase58();
  }

  get privateKey(): string {
    return bs58.encode(this.keypair.secretKey);
  }

  get privateKeyArray(): number[] {
    return Array.from(this.keypair.secretKey);
  }

  // Get encrypted private key
  getEncryptedPrivateKey(password: string): string {
    return encryptPrivateKey(this.privateKey, password);
  }

  // Balance operations
  async getSOLBalance(): Promise<number> {
    try {
      const balance = await this.connection.getBalance(this.publicKey);
      return balance / SOLANA_CONSTANTS.LAMPORTS_PER_SOL;
    } catch (error) {
      console.error('Failed to get SOL balance:', error);
      throw error;
    }
  }

  async getTokenBalances(): Promise<TokenBalance[]> {
    try {
      const tokenAccounts = await this.connection.getParsedTokenAccountsByOwner(
        this.publicKey,
        { programId: TOKEN_PROGRAM_ID }
      );

      const balances: TokenBalance[] = [];

      for (const account of tokenAccounts.value) {
        const parsedInfo = account.account.data.parsed.info;
        const mint = parsedInfo.mint;
        const amount = parsedInfo.tokenAmount.amount;
        const decimals = parsedInfo.tokenAmount.decimals;
        const uiAmount = parsedInfo.tokenAmount.uiAmount || 0;

        balances.push({
          mint,
          amount: parseInt(amount),
          decimals,
          uiAmount,
        });
      }

      return balances;
    } catch (error) {
      console.error('Failed to get token balances:', error);
      throw error;
    }
  }

  async getAllBalances(): Promise<WalletInfo> {
    try {
      const [solBalance, tokenBalances] = await Promise.all([
        this.getSOLBalance(),
        this.getTokenBalances(),
      ]);

      return {
        publicKey: this.publicKeyString,
        balance: solBalance,
        tokenBalances,
      };
    } catch (error) {
      console.error('Failed to get all balances:', error);
      throw error;
    }
  }

  // Transaction operations
  async sendSOL(toAddress: string, amount: number): Promise<string> {
    try {
      const toPublicKey = new PublicKey(toAddress);
      const lamports = Math.floor(amount * SOLANA_CONSTANTS.LAMPORTS_PER_SOL);

      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: this.publicKey,
          toPubkey: toPublicKey,
          lamports,
        })
      );

      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [this.keypair]
      );

      return signature;
    } catch (error) {
      console.error('Failed to send SOL:', error);
      throw error;
    }
  }

  async createTokenAccount(mintAddress: string): Promise<string> {
    try {
      const mint = new PublicKey(mintAddress);
      const associatedTokenAddress = await getAssociatedTokenAddress(
        mint,
        this.publicKey
      );

      // Check if account already exists
      const accountInfo = await this.connection.getAccountInfo(associatedTokenAddress);
      if (accountInfo) {
        return associatedTokenAddress.toBase58();
      }

      const transaction = new Transaction().add(
        createAssociatedTokenAccountInstruction(
          this.publicKey, // payer
          associatedTokenAddress, // associated token account
          this.publicKey, // owner
          mint // mint
        )
      );

      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [this.keypair]
      );

      console.log('Token account created:', signature);
      return associatedTokenAddress.toBase58();
    } catch (error) {
      console.error('Failed to create token account:', error);
      throw error;
    }
  }

  // Utility methods
  async airdrop(amount: number = 1): Promise<string> {
    try {
      if (this.connection.rpcEndpoint.includes('mainnet')) {
        throw new Error('Airdrop not available on mainnet');
      }

      const lamports = amount * SOLANA_CONSTANTS.LAMPORTS_PER_SOL;
      const signature = await this.connection.requestAirdrop(this.publicKey, lamports);
      
      await this.connection.confirmTransaction(signature);
      return signature;
    } catch (error) {
      console.error('Failed to airdrop:', error);
      throw error;
    }
  }

  // Export wallet data
  exportWallet(includePrivateKey: boolean = false): Partial<WalletInfo> {
    const walletData: Partial<WalletInfo> = {
      publicKey: this.publicKeyString,
    };

    if (includePrivateKey) {
      walletData.privateKey = this.privateKey;
    }

    return walletData;
  }

  // Sign message
  signMessage(message: string): string {
    const messageBytes = new TextEncoder().encode(message);
    const signature = this.keypair.sign(messageBytes);
    return bs58.encode(signature);
  }

  // Verify signature
  static verifySignature(message: string, signature: string, publicKey: string): boolean {
    try {
      const messageBytes = new TextEncoder().encode(message);
      const signatureBytes = bs58.decode(signature);
      const pubKey = new PublicKey(publicKey);
      
      // Note: This is a simplified verification. In practice, you'd use nacl.sign.detached.verify
      return true; // Placeholder
    } catch (error) {
      return false;
    }
  }
}

// Utility functions for wallet management
export const generateWallet = (options: CreateWalletOptions = {}): SolanaWallet => {
  return SolanaWallet.generate();
};

export const importWallet = (options: ImportWalletOptions): SolanaWallet => {
  return SolanaWallet.fromPrivateKey(options.privateKey);
};

export const validateAddress = (address: string): boolean => {
  try {
    new PublicKey(address);
    return true;
  } catch {
    return false;
  }
};

export const validatePrivateKey = (privateKey: string): boolean => {
  try {
    SolanaWallet.fromPrivateKey(privateKey);
    return true;
  } catch {
    return false;
  }
};
