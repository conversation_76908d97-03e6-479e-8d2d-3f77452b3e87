/**
 * UNIFIED SNIPER BOT SERVICE
 * 
 * This service provides a single interface for:
 * 1. Frontend sniper bot controls
 * 2. Backend sniper bot communication
 * 3. Real-time status updates
 * 4. Settings management
 */

import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

export interface SniperBotSettings {
  id?: string;
  user_id: string;
  auto_trading_enabled: boolean;
  min_liquidity_sol: number;
  max_buy_amount_sol: number;
  target_profit_percent: number;
  stop_loss_percent: number;
  check_honeypot: boolean;
  check_mint_authority: boolean;
  check_freeze_authority: boolean;
  min_holders: number;
  max_slippage_percent: number;
  priority_fee_lamports: number;
}

export interface SniperBotStatus {
  isRunning: boolean;
  tokensDetected: number;
  tradesExecuted: number;
  totalProfit: number;
  lastActivity: string;
  currentPositions: number;
}

export interface DetectedToken {
  id: string;
  token_address: string;
  token_symbol?: string;
  token_name?: string;
  pair_address: string;
  initial_liquidity_sol: number;
  holders_count: number;
  mint_authority?: string;
  freeze_authority?: string;
  is_honeypot: boolean;
  safety_score: number;
  detected_at: string;
  action_taken?: 'bought' | 'skipped' | 'pending';
}

export interface SniperTrade {
  id: string;
  user_id: string;
  wallet_id: string;
  type: 'snipe_buy' | 'snipe_sell';
  input_mint: string;
  output_mint: string;
  input_amount: number;
  output_amount: number;
  price_impact?: number;
  slippage?: number;
  signature: string;
  status: 'pending' | 'confirmed' | 'failed';
  pnl?: number;
  created_at: string;
}

class UnifiedSniperBotService {
  private ws: WebSocket | null = null;
  private statusCallbacks: ((status: SniperBotStatus) => void)[] = [];
  private tokenCallbacks: ((token: DetectedToken) => void)[] = [];
  private tradeCallbacks: ((trade: SniperTrade) => void)[] = [];

  /**
   * Get current sniper bot settings
   */
  async getSettings(userId: string): Promise<SniperBotSettings | null> {
    try {
      const { data, error } = await supabase
        .from('sniper_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to get sniper settings:', error);
      return null;
    }
  }

  /**
   * Update sniper bot settings
   */
  async updateSettings(userId: string, settings: Partial<SniperBotSettings>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('sniper_settings')
        .upsert({
          user_id: userId,
          ...settings,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Failed to update sniper settings:', error);
      return false;
    }
  }

  /**
   * Start the sniper bot
   */
  async startBot(userId: string): Promise<boolean> {
    try {
      // Update settings to enable auto trading
      const success = await this.updateSettings(userId, {
        auto_trading_enabled: true
      });

      if (success) {
        // Connect to WebSocket for real-time updates
        this.connectWebSocket(userId);
        
        // Notify backend to start sniper bot
        await this.notifyBackend('start', userId);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to start sniper bot:', error);
      return false;
    }
  }

  /**
   * Stop the sniper bot
   */
  async stopBot(userId: string): Promise<boolean> {
    try {
      // Update settings to disable auto trading
      const success = await this.updateSettings(userId, {
        auto_trading_enabled: false
      });

      if (success) {
        // Disconnect WebSocket
        this.disconnectWebSocket();
        
        // Notify backend to stop sniper bot
        await this.notifyBackend('stop', userId);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to stop sniper bot:', error);
      return false;
    }
  }

  /**
   * Get sniper bot status
   */
  async getStatus(userId: string): Promise<SniperBotStatus> {
    try {
      // Get recent trades
      const { data: trades } = await supabase
        .from('trades')
        .select('*')
        .eq('user_id', userId)
        .in('type', ['snipe_buy', 'snipe_sell'])
        .order('created_at', { ascending: false })
        .limit(100);

      // Get detected tokens
      const { data: tokens } = await supabase
        .from('detected_tokens')
        .select('*')
        .order('detected_at', { ascending: false })
        .limit(100);

      // Calculate stats
      const buyTrades = trades?.filter(t => t.type === 'snipe_buy') || [];
      const sellTrades = trades?.filter(t => t.type === 'snipe_sell') || [];
      const totalProfit = sellTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);
      const currentPositions = buyTrades.length - sellTrades.length;

      // Get current settings to check if running
      const settings = await this.getSettings(userId);
      const isRunning = settings?.auto_trading_enabled || false;

      return {
        isRunning,
        tokensDetected: tokens?.length || 0,
        tradesExecuted: trades?.length || 0,
        totalProfit,
        lastActivity: trades?.[0]?.created_at || 'Never',
        currentPositions: Math.max(0, currentPositions)
      };
    } catch (error) {
      console.error('Failed to get sniper status:', error);
      return {
        isRunning: false,
        tokensDetected: 0,
        tradesExecuted: 0,
        totalProfit: 0,
        lastActivity: 'Never',
        currentPositions: 0
      };
    }
  }

  /**
   * Get detected tokens
   */
  async getDetectedTokens(limit = 50): Promise<DetectedToken[]> {
    try {
      const { data, error } = await supabase
        .from('detected_tokens')
        .select('*')
        .order('detected_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to get detected tokens:', error);
      return [];
    }
  }

  /**
   * Get sniper trades
   */
  async getSniperTrades(userId: string, limit = 50): Promise<SniperTrade[]> {
    try {
      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .eq('user_id', userId)
        .in('type', ['snipe_buy', 'snipe_sell'])
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to get sniper trades:', error);
      return [];
    }
  }

  /**
   * Connect to WebSocket for real-time updates
   */
  private connectWebSocket(userId: string): void {
    try {
      // In a real implementation, this would connect to your backend WebSocket
      // For now, we'll simulate with periodic updates
      console.log('🔗 Connecting to sniper bot WebSocket...');
      
      // Simulate real-time updates
      setInterval(() => {
        this.simulateRealTimeUpdate(userId);
      }, 5000);
      
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }

  /**
   * Disconnect WebSocket
   */
  private disconnectWebSocket(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * Notify backend about bot state changes
   */
  private async notifyBackend(action: 'start' | 'stop', userId: string): Promise<void> {
    try {
      // In a real implementation, this would call your backend API
      console.log(`📡 Notifying backend: ${action} sniper bot for user ${userId}`);
      
      // For now, we'll just log the action
      const logEntry = {
        user_id: userId,
        action: `sniper_bot_${action}`,
        timestamp: new Date().toISOString(),
        details: { action, userId }
      };
      
      console.log('Backend notification:', logEntry);
    } catch (error) {
      console.error('Failed to notify backend:', error);
    }
  }

  /**
   * Simulate real-time updates (for demo)
   */
  private simulateRealTimeUpdate(userId: string): void {
    // Simulate status update
    const mockStatus: SniperBotStatus = {
      isRunning: true,
      tokensDetected: Math.floor(Math.random() * 10) + 1,
      tradesExecuted: Math.floor(Math.random() * 5),
      totalProfit: Math.random() * 2 - 1, // -1 to +1 SOL
      lastActivity: new Date().toISOString(),
      currentPositions: Math.floor(Math.random() * 3)
    };

    // Notify status callbacks
    this.statusCallbacks.forEach(callback => callback(mockStatus));

    // Occasionally simulate new token detection
    if (Math.random() < 0.3) {
      const mockToken: DetectedToken = {
        id: `token_${Date.now()}`,
        token_address: `Token${Math.random().toString(36).substr(2, 9)}`,
        token_symbol: `TKN${Math.floor(Math.random() * 1000)}`,
        pair_address: `Pair${Math.random().toString(36).substr(2, 9)}`,
        initial_liquidity_sol: Math.random() * 20,
        holders_count: Math.floor(Math.random() * 100) + 10,
        is_honeypot: Math.random() < 0.2,
        safety_score: Math.floor(Math.random() * 100),
        detected_at: new Date().toISOString(),
        action_taken: Math.random() < 0.5 ? 'bought' : 'skipped'
      };

      // Notify token callbacks
      this.tokenCallbacks.forEach(callback => callback(mockToken));
    }
  }

  /**
   * Subscribe to status updates
   */
  onStatusUpdate(callback: (status: SniperBotStatus) => void): () => void {
    this.statusCallbacks.push(callback);
    return () => {
      const index = this.statusCallbacks.indexOf(callback);
      if (index > -1) {
        this.statusCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Subscribe to token detection updates
   */
  onTokenDetected(callback: (token: DetectedToken) => void): () => void {
    this.tokenCallbacks.push(callback);
    return () => {
      const index = this.tokenCallbacks.indexOf(callback);
      if (index > -1) {
        this.tokenCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Subscribe to trade updates
   */
  onTradeExecuted(callback: (trade: SniperTrade) => void): () => void {
    this.tradeCallbacks.push(callback);
    return () => {
      const index = this.tradeCallbacks.indexOf(callback);
      if (index > -1) {
        this.tradeCallbacks.splice(index, 1);
      }
    };
  }
}

// Export singleton instance
export const unifiedSniperBot = new UnifiedSniperBotService();
