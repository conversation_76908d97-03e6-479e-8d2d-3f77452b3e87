/**
 * Detection Service
 * 
 * Manages the token detection engine lifecycle and provides
 * a centralized interface for starting/stopping detection
 */

import { tokenDetectionEngine, DetectionFilters } from '../token/detection';
import { toast } from 'sonner';

export class DetectionService {
  private isInitialized = false;
  private isRunning = false;
  private statusUpdateInterval: NodeJS.Timeout | null = null;
  private statusCallbacks: ((status: DetectionStatus) => void)[] = [];

  /**
   * Initialize the detection service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🔍 Detection service already initialized');
      return;
    }

    try {
      console.log('🚀 Initializing token detection service...');
      
      // Start the detection engine with default filters
      await this.startDetection({
        minLiquidity: 1, // 1 SOL minimum
        maxSupply: 1000000000, // 1B max supply
        requireMetadata: false,
        excludeKnownTokens: true,
      });

      this.isInitialized = true;
      this.startStatusUpdates();
      
      console.log('✅ Token detection service initialized successfully');
      toast.success('🔍 Token detection started');
    } catch (error) {
      console.error('❌ Failed to initialize detection service:', error);
      toast.error('Failed to start token detection');
      throw error;
    }
  }

  /**
   * Start token detection with filters
   */
  async startDetection(filters: DetectionFilters = {}): Promise<void> {
    try {
      if (this.isRunning) {
        console.log('🔍 Token detection already running');
        return;
      }

      console.log('🔍 Starting token detection...');
      await tokenDetectionEngine.startMonitoring(filters);
      this.isRunning = true;
      
      this.notifyStatusUpdate();
    } catch (error) {
      console.error('❌ Failed to start token detection:', error);
      throw error;
    }
  }

  /**
   * Stop token detection
   */
  async stopDetection(): Promise<void> {
    try {
      console.log('🛑 Stopping token detection...');
      await tokenDetectionEngine.stopMonitoring();
      this.isRunning = false;
      
      this.notifyStatusUpdate();
    } catch (error) {
      console.error('❌ Failed to stop token detection:', error);
      throw error;
    }
  }

  /**
   * Get current detection status
   */
  getStatus(): DetectionStatus {
    const detectedTokens = tokenDetectionEngine.getDetectedTokens();
    const recentTokens = tokenDetectionEngine.getRecentTokens(5); // Last 5 minutes
    
    return {
      isRunning: this.isRunning,
      isInitialized: this.isInitialized,
      totalTokensDetected: detectedTokens.length,
      recentTokensCount: recentTokens.length,
      lastActivity: recentTokens.length > 0 ? new Date().toISOString() : 'Never',
      detectedTokens: detectedTokens.slice(0, 10), // Latest 10 tokens
    };
  }

  /**
   * Subscribe to status updates
   */
  onStatusUpdate(callback: (status: DetectionStatus) => void): () => void {
    this.statusCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.statusCallbacks.indexOf(callback);
      if (index > -1) {
        this.statusCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Start periodic status updates
   */
  private startStatusUpdates(): void {
    if (this.statusUpdateInterval) {
      clearInterval(this.statusUpdateInterval);
    }

    this.statusUpdateInterval = setInterval(() => {
      this.notifyStatusUpdate();
    }, 10000); // Update every 10 seconds
  }

  /**
   * Notify all subscribers of status update
   */
  private notifyStatusUpdate(): void {
    const status = this.getStatus();
    this.statusCallbacks.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('Error in status callback:', error);
      }
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.statusUpdateInterval) {
      clearInterval(this.statusUpdateInterval);
      this.statusUpdateInterval = null;
    }
    
    this.statusCallbacks = [];
    
    if (this.isRunning) {
      this.stopDetection().catch(console.error);
    }
  }
}

export interface DetectionStatus {
  isRunning: boolean;
  isInitialized: boolean;
  totalTokensDetected: number;
  recentTokensCount: number;
  lastActivity: string;
  detectedTokens: any[];
}

// Export singleton instance
export const detectionService = new DetectionService();

// Auto-initialize when imported (for dashboard)
if (typeof window !== 'undefined') {
  // Only auto-initialize in browser environment
  setTimeout(() => {
    detectionService.initialize().catch(error => {
      console.error('Failed to auto-initialize detection service:', error);
    });
  }, 2000); // Wait 2 seconds for other services to initialize
}

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    detectionService.destroy();
  });
}
