/**
 * Comprehensive database service for Swift Sniper Fi
 */

import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];
type Wallet = Tables['wallets']['Row'];
type Trade = Tables['trades']['Row'];
type DetectedToken = Tables['detected_tokens']['Row'];
type TradingSettings = Tables['trading_settings']['Row'];

export class DatabaseService {
  /**
   * Wallet operations
   */
  async createWallet(data: {
    name: string;
    address: string;
    private_key_encrypted: string;
    is_primary?: boolean;
  }): Promise<Wallet> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data: wallet, error } = await supabase
      .from('wallets')
      .insert({
        user_id: user.user.id,
        name: data.name,
        address: data.address,
        private_key_encrypted: data.private_key_encrypted,
        is_primary: data.is_primary || false,
        is_active: true,
      })
      .select()
      .single();

    if (error) throw error;
    return wallet;
  }

  async getWallets(): Promise<Wallet[]> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('wallets')
      .select('*')
      .eq('user_id', user.user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async getPrimaryWallet(): Promise<Wallet | null> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('wallets')
      .select('*')
      .eq('user_id', user.user.id)
      .eq('is_primary', true)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data || null;
  }

  async setPrimaryWallet(walletId: string): Promise<void> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // First, unset all primary wallets
    await supabase
      .from('wallets')
      .update({ is_primary: false })
      .eq('user_id', user.user.id);

    // Then set the new primary wallet
    const { error } = await supabase
      .from('wallets')
      .update({ is_primary: true })
      .eq('id', walletId)
      .eq('user_id', user.user.id);

    if (error) throw error;
  }

  async deleteWallet(walletId: string): Promise<void> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('wallets')
      .update({ is_active: false })
      .eq('id', walletId)
      .eq('user_id', user.user.id);

    if (error) throw error;
  }

  /**
   * Trading operations
   */
  async createTrade(data: {
    wallet_id: string;
    type: 'buy' | 'sell' | 'snipe_buy' | 'snipe_sell';
    input_mint: string;
    output_mint: string;
    input_amount: number;
    output_amount: number;
    price_impact?: number;
    slippage?: number;
    signature?: string;
    safety_score?: number;
    response_time_ms?: number;
  }): Promise<Trade> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data: trade, error } = await supabase
      .from('trades')
      .insert({
        user_id: user.user.id,
        ...data,
      })
      .select()
      .single();

    if (error) throw error;
    return trade;
  }

  async updateTradeStatus(tradeId: string, status: 'confirmed' | 'failed' | 'cancelled', signature?: string): Promise<void> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const updateData: any = { status, updated_at: new Date().toISOString() };
    if (signature) updateData.signature = signature;

    const { error } = await supabase
      .from('trades')
      .update(updateData)
      .eq('id', tradeId)
      .eq('user_id', user.user.id);

    if (error) throw error;
  }

  async getTrades(limit: number = 50): Promise<Trade[]> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', user.user.id)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  /**
   * Trading settings operations
   */
  async getTradingSettings(): Promise<TradingSettings | null> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('trading_settings')
      .select('*')
      .eq('user_id', user.user.id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data || null;
  }

  async updateTradingSettings(settings: Partial<TradingSettings>): Promise<TradingSettings> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('trading_settings')
      .upsert({
        user_id: user.user.id,
        ...settings,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Token detection operations
   */
  async saveDetectedToken(token: {
    token_address: string;
    pair_address: string;
    token_name?: string;
    token_symbol?: string;
    initial_liquidity_sol?: number;
    current_price?: number;
    holders_count?: number;
    mint_authority?: string;
    freeze_authority?: string;
    is_honeypot?: boolean;
    can_sell?: boolean;
    liquidity_locked?: boolean;
    safety_score?: number;
    risk_factors?: any;
    liquidity_pools?: any;
    metadata?: any;
  }): Promise<DetectedToken> {
    const { data, error } = await supabase
      .from('detected_tokens')
      .upsert({
        ...token,
        detected_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getDetectedTokens(limit: number = 100): Promise<DetectedToken[]> {
    const { data, error } = await supabase
      .from('detected_tokens')
      .select('*')
      .order('detected_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getTokenByAddress(address: string): Promise<DetectedToken | null> {
    const { data, error } = await supabase
      .from('detected_tokens')
      .select('*')
      .eq('token_address', address)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data || null;
  }

  /**
   * Analytics and statistics
   */
  async getUserStats(): Promise<{
    totalTrades: number;
    successfulTrades: number;
    totalPnL: number;
    totalWallets: number;
  }> {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const [tradesResult, walletsResult] = await Promise.all([
      supabase
        .from('trades')
        .select('status, pnl')
        .eq('user_id', user.user.id),
      supabase
        .from('wallets')
        .select('id')
        .eq('user_id', user.user.id)
        .eq('is_active', true),
    ]);

    const trades = tradesResult.data || [];
    const wallets = walletsResult.data || [];

    const totalTrades = trades.length;
    const successfulTrades = trades.filter(t => t.status === 'confirmed').length;
    const totalPnL = trades.reduce((sum, t) => sum + (t.pnl || 0), 0);
    const totalWallets = wallets.length;

    return {
      totalTrades,
      successfulTrades,
      totalPnL,
      totalWallets,
    };
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('wallets')
        .select('id')
        .limit(1);

      return !error;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
