import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { walletService } from './walletService';
import { supabase } from '@/lib/supabase';
import { createMockKeypair, mockLocalStorage } from '@/test/setup';

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          order: vi.fn(() => ({
            limit: vi.fn(),
          })),
        })),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

// Mock crypto-js
vi.mock('crypto-js', () => ({
  AES: {
    encrypt: vi.fn(() => ({ toString: () => 'encrypted-data' })),
    decrypt: vi.fn(() => ({ toString: () => 'decrypted-data' })),
  },
  enc: {
    Utf8: {},
  },
}));

// Mock Solana Web3
vi.mock('@solana/web3.js', () => ({
  Keypair: {
    generate: vi.fn(() => createMockKeypair()),
    fromSecretKey: vi.fn(() => createMockKeypair()),
  },
  PublicKey: vi.fn().mockImplementation((key) => ({
    toBase58: () => key,
    toString: () => key,
  })),
  Connection: vi.fn(() => ({
    getBalance: vi.fn().mockResolvedValue(**********),
    getTokenAccountsByOwner: vi.fn().mockResolvedValue({ value: [] }),
  })),
}));

describe('WalletService', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
  };

  const mockWallet = {
    id: 'test-wallet-id',
    user_id: 'test-user-id',
    name: 'Test Wallet',
    public_key: 'MockPublicKey123456789',
    encrypted_private_key: 'encrypted-private-key',
    is_primary: true,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.clear();
    
    // Mock authenticated user
    (supabase.auth.getUser as any).mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('createWallet', () => {
    it('should create a new wallet successfully', async () => {
      // Mock successful database insert
      const mockInsert = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockWallet,
            error: null,
          }),
        }),
      });
      
      (supabase.from as any).mockReturnValue({
        insert: mockInsert,
      });

      const result = await walletService.createWallet('Test Wallet', 'password123');

      expect(result).toEqual(mockWallet);
      expect(mockInsert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: mockUser.id,
          name: 'Test Wallet',
          public_key: expect.any(String),
          encrypted_private_key: expect.any(String),
        })
      );
    });

    it('should throw error when user is not authenticated', async () => {
      (supabase.auth.getUser as any).mockResolvedValue({
        data: { user: null },
        error: null,
      });

      await expect(
        walletService.createWallet('Test Wallet', 'password123')
      ).rejects.toThrow('User not authenticated');
    });

    it('should handle database errors', async () => {
      const mockInsert = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: null,
            error: { message: 'Database error' },
          }),
        }),
      });
      
      (supabase.from as any).mockReturnValue({
        insert: mockInsert,
      });

      await expect(
        walletService.createWallet('Test Wallet', 'password123')
      ).rejects.toThrow('Database error');
    });
  });

  describe('getWallets', () => {
    it('should return user wallets', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({
              data: [mockWallet],
              error: null,
            }),
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        select: mockSelect,
      });

      const result = await walletService.getWallets();

      expect(result).toEqual([mockWallet]);
      expect(mockSelect).toHaveBeenCalledWith('*');
    });

    it('should return empty array when no wallets found', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        select: mockSelect,
      });

      const result = await walletService.getWallets();

      expect(result).toEqual([]);
    });
  });

  describe('getPrimaryWallet', () => {
    it('should return primary wallet', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockWallet,
              error: null,
            }),
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        select: mockSelect,
      });

      const result = await walletService.getPrimaryWallet();

      expect(result).toEqual(mockWallet);
    });

    it('should return null when no primary wallet found', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { code: 'PGRST116' }, // No rows found
            }),
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        select: mockSelect,
      });

      const result = await walletService.getPrimaryWallet();

      expect(result).toBeNull();
    });
  });

  describe('deleteWallet', () => {
    it('should delete wallet successfully', async () => {
      const mockDelete = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: null,
        }),
      });

      (supabase.from as any).mockReturnValue({
        delete: mockDelete,
      });

      await walletService.deleteWallet('test-wallet-id');

      expect(mockDelete).toHaveBeenCalled();
    });

    it('should handle delete errors', async () => {
      const mockDelete = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: { message: 'Delete failed' },
        }),
      });

      (supabase.from as any).mockReturnValue({
        delete: mockDelete,
      });

      await expect(
        walletService.deleteWallet('test-wallet-id')
      ).rejects.toThrow('Delete failed');
    });
  });

  describe('setPrimaryWallet', () => {
    it('should set wallet as primary', async () => {
      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { ...mockWallet, is_primary: true },
              error: null,
            }),
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        update: mockUpdate,
      });

      const result = await walletService.setPrimaryWallet('test-wallet-id');

      expect(result.is_primary).toBe(true);
      expect(mockUpdate).toHaveBeenCalledWith({ is_primary: true });
    });
  });

  describe('unlockWallet', () => {
    it('should unlock wallet with correct password', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockWallet,
            error: null,
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        select: mockSelect,
      });

      const result = await walletService.unlockWallet('test-wallet-id', 'password123');

      expect(result).toBeDefined();
      expect(result.publicKey).toBeDefined();
    });

    it('should throw error for invalid wallet ID', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: null,
            error: { code: 'PGRST116' },
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        select: mockSelect,
      });

      await expect(
        walletService.unlockWallet('invalid-id', 'password123')
      ).rejects.toThrow('Wallet not found');
    });
  });

  describe('getWalletStats', () => {
    it('should return wallet statistics', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({
              data: [mockWallet],
              error: null,
            }),
          }),
        }),
      });

      (supabase.from as any).mockReturnValue({
        select: mockSelect,
      });

      const result = await walletService.getWalletStats();

      expect(result).toEqual({
        totalWallets: 1,
        totalValue: 0,
        totalSolBalance: 0,
        totalTokens: 0,
      });
    });
  });
});
