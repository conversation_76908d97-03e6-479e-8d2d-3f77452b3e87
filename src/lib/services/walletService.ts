import { supabase } from '@/lib/supabase';
import { SolanaWallet, WalletInfo, generateWallet, importWallet } from '../solana/wallet';
import { encryptPrivateKey, decryptPrivate<PERSON>ey } from '../crypto/encryption';
import { databaseService } from './databaseService';
import { getTokenPrices } from '../price/feeds';
import { NATIVE_SOL_MINT } from '../solana/constants';
import { getConnection } from '../solana/connection';
import { PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';

export interface StoredWallet {
  id: string;
  user_id: string;
  name: string;
  address: string;
  private_key_encrypted: string;
  is_primary: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WalletBalance {
  walletId: string;
  address: string;
  name: string;
  solBalance: number;
  totalValue: number;
  lastUpdated: number;
  tokens: any[]; // Simplified - no complex token structure for now
}

export interface CreateWalletRequest {
  name: string;
  password: string;
  isPrimary?: boolean;
}

export interface ImportWalletRequest {
  name: string;
  privateKey: string;
  password: string;
  isPrimary?: boolean;
}

export class WalletService {
  /**
   * Create a new wallet
   */
  async createWallet(request: CreateWalletRequest): Promise<{ wallet: StoredWallet; solanaWallet: SolanaWallet }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Generate new Solana wallet
      const solanaWallet = generateWallet();
      
      // Encrypt private key (using simple base64 for now)
      const privateKeyArray = Array.from(solanaWallet.keypair.secretKey);
      const encryptedPrivateKey = btoa(JSON.stringify(privateKeyArray));

      // If this is the first wallet or explicitly set as primary, make it primary
      if (request.isPrimary) {
        await this.clearPrimaryWallets(user.id);
      }

      // Store in database
      const { data, error } = await supabase
        .from('wallets')
        .insert({
          user_id: user.id,
          name: request.name,
          address: solanaWallet.publicKeyString,
          private_key_encrypted: encryptedPrivateKey,
          is_primary: request.isPrimary || false,
          is_active: true,
        })
        .select()
        .single();

      if (error) throw error;

      return { wallet: data, solanaWallet };
    } catch (error) {
      console.error('Failed to create wallet:', error);
      throw error;
    }
  }

  /**
   * Import an existing wallet
   */
  async importWallet(request: ImportWalletRequest): Promise<{ wallet: StoredWallet; solanaWallet: SolanaWallet }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Import Solana wallet
      const solanaWallet = importWallet({ privateKey: request.privateKey });
      
      // Check if wallet already exists
      const { data: existingWallet } = await supabase
        .from('wallets')
        .select('id')
        .eq('user_id', user.id)
        .eq('address', solanaWallet.publicKeyString)
        .single();

      if (existingWallet) {
        throw new Error('Wallet already exists');
      }

      // Encrypt private key (using simple base64 for now)
      const privateKeyArray = Array.from(solanaWallet.keypair.secretKey);
      const encryptedPrivateKey = btoa(JSON.stringify(privateKeyArray));

      // If this is set as primary, clear other primary wallets
      if (request.isPrimary) {
        await this.clearPrimaryWallets(user.id);
      }

      // Store in database
      const { data, error } = await supabase
        .from('wallets')
        .insert({
          user_id: user.id,
          name: request.name,
          address: solanaWallet.publicKeyString,
          private_key_encrypted: encryptedPrivateKey,
          is_primary: request.isPrimary || false,
        })
        .select()
        .single();

      if (error) throw error;

      return { wallet: data, solanaWallet };
    } catch (error) {
      console.error('Failed to import wallet:', error);
      throw error;
    }
  }

  /**
   * Get all wallets for the current user
   */
  async getWallets(): Promise<StoredWallet[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('wallets')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to get wallets:', error);
      throw error;
    }
  }

  /**
   * Get primary wallet
   */
  async getPrimaryWallet(): Promise<StoredWallet | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('wallets')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_primary', true)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      return data;
    } catch (error) {
      console.error('Failed to get primary wallet:', error);
      return null;
    }
  }

  /**
   * Set wallet as primary
   */
  async setPrimaryWallet(walletId: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Clear existing primary wallets
      await this.clearPrimaryWallets(user.id);

      // Set new primary wallet
      const { error } = await supabase
        .from('wallets')
        .update({ is_primary: true })
        .eq('id', walletId)
        .eq('user_id', user.id);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to set primary wallet:', error);
      throw error;
    }
  }

  /**
   * Delete a wallet
   */
  async deleteWallet(walletId: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('wallets')
        .delete()
        .eq('id', walletId)
        .eq('user_id', user.id);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to delete wallet:', error);
      throw error;
    }
  }

  /**
   * Get wallet balances
   */
  async getWalletBalances(walletIds?: string[]): Promise<WalletBalance[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get wallets
      let query = supabase
        .from('wallets')
        .select('*')
        .eq('user_id', user.id);

      if (walletIds && walletIds.length > 0) {
        query = query.in('id', walletIds);
      }

      const { data: wallets, error } = await query;
      if (error) throw error;

      const balances: WalletBalance[] = [];

      for (const wallet of wallets || []) {
        try {
          // Simplified balance checking - just SOL for now
          const connection = getConnection();
          const publicKey = new PublicKey(wallet.address);
          const solBalance = await connection.getBalance(publicKey);
          const solBalanceFormatted = solBalance / LAMPORTS_PER_SOL;

          balances.push({
            walletId: wallet.id,
            address: wallet.address,
            name: wallet.name,
            solBalance: solBalanceFormatted,
            totalValue: solBalanceFormatted * 200, // Rough SOL price
            lastUpdated: Date.now(),
            tokens: [], // Simplified - no tokens for now
          });
        } catch (error) {
          console.warn(`Failed to get balance for wallet ${wallet.id}:`, error);
          // Add empty balance entry
          balances.push({
            walletId: wallet.id,
            address: wallet.address,
            name: wallet.name,
            solBalance: 0,
            totalValue: 0,
            lastUpdated: Date.now(),
            tokens: [],
          });
        }
      }

      return balances;
    } catch (error) {
      console.error('Failed to get wallet balances:', error);
      throw error;
    }
  }

  /**
   * Get Solana wallet instance with private key
   */
  async getSolanaWallet(walletId: string, password: string): Promise<SolanaWallet> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: wallet, error } = await supabase
        .from('wallets')
        .select('*')
        .eq('id', walletId)
        .eq('user_id', user.id)
        .single();

      if (error) throw error;
      if (!wallet) throw new Error('Wallet not found');

      // Decrypt private key (using simple base64 for now)
      const privateKeyArray = JSON.parse(atob(wallet.private_key_encrypted));
      const privateKeyString = JSON.stringify(privateKeyArray);

      // Create Solana wallet
      return importWallet(privateKeyString);
    } catch (error) {
      console.error('Failed to get Solana wallet:', error);
      throw error;
    }
  }

  /**
   * Update wallet name
   */
  async updateWalletName(walletId: string, name: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('wallets')
        .update({ name })
        .eq('id', walletId)
        .eq('user_id', user.id);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to update wallet name:', error);
      throw error;
    }
  }

  /**
   * Clear all primary wallets for a user
   */
  private async clearPrimaryWallets(userId: string): Promise<void> {
    const { error } = await supabase
      .from('wallets')
      .update({ is_primary: false })
      .eq('user_id', userId)
      .eq('is_primary', true);

    if (error) throw error;
  }

  /**
   * Export wallet private key (requires password)
   */
  async exportWalletPrivateKey(walletId: string, password: string): Promise<string> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: wallet, error } = await supabase
        .from('wallets')
        .select('private_key_encrypted')
        .eq('id', walletId)
        .eq('user_id', user.id)
        .single();

      if (error) throw error;
      if (!wallet) throw new Error('Wallet not found');

      // Decrypt and return private key (using simple base64 for now)
      const privateKeyArray = JSON.parse(atob(wallet.private_key_encrypted));
      return new Uint8Array(privateKeyArray);
    } catch (error) {
      console.error('Failed to export wallet private key:', error);
      throw error;
    }
  }

  /**
   * Get wallet statistics
   */
  async getWalletStats(): Promise<{
    totalWallets: number;
    totalValue: number;
    primaryWallet?: string;
    lastActivity?: number;
  }> {
    try {
      const wallets = await this.getWallets();
      const balances = await this.getWalletBalances();
      
      const totalValue = balances.reduce((sum, balance) => sum + balance.totalValue, 0);
      const primaryWallet = wallets.find(w => w.is_primary)?.address;
      const lastActivity = Math.max(...balances.map(b => b.lastUpdated));

      return {
        totalWallets: wallets.length,
        totalValue,
        primaryWallet,
        lastActivity: lastActivity > 0 ? lastActivity : undefined,
      };
    } catch (error) {
      console.error('Failed to get wallet stats:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const walletService = new WalletService();
