import { supabase } from '@/lib/supabase';
import { walletService } from './walletService';
import { jupiterSwapManager, SwapPara<PERSON>, SwapR<PERSON>ult } from '../jupiter/swap';
import { tokenDetectionEngine, DetectedToken } from '../token/detection';
import { tokenSafetyAnalyzer, SafetyAnalysis } from '../token/safety';
import { priceFeedManager, TokenPrice } from '../price/feeds';
import { NATIVE_SOL_MINT } from '../solana/constants';

export interface TradingSettings {
  id: string;
  user_id: string;
  auto_trading_enabled: boolean;
  max_position_size_sol: number;
  min_safety_score: number;
  max_slippage_percent: number;
  stop_loss_percent: number;
  take_profit_percent: number;
  priority_fee_lamports: number;
  created_at: string;
  updated_at: string;
}

export interface TradeLog {
  id: string;
  user_id: string;
  wallet_id: string;
  input_mint: string;
  output_mint: string;
  input_amount: number;
  output_amount: number;
  slippage_percent: number;
  price_impact_percent: number;
  transaction_signature: string;
  status: 'pending' | 'success' | 'failed';
  error_message?: string;
  created_at: string;
}

export interface SniperTarget {
  mint: string;
  symbol?: string;
  name?: string;
  targetPrice?: number;
  maxBuyAmount: number;
  safetyScore: number;
  isActive: boolean;
  detectedAt: number;
}

export interface DashboardData {
  portfolio: {
    totalValue: number;
    solBalance: number;
    tokenCount: number;
    dayChange: number;
    dayChangePercent: number;
  };
  recentTrades: TradeLog[];
  detectedTokens: DetectedToken[];
  sniperTargets: SniperTarget[];
  tradingSettings: TradingSettings | null;
  isAutoTradingActive: boolean;
}

export class TradingService {
  private autoTradingInterval: NodeJS.Timeout | null = null;
  private priceSubscriptions: (() => void)[] = [];

  /**
   * Get trading settings for the current user
   */
  async getTradingSettings(): Promise<TradingSettings | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('sniper_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Failed to get trading settings:', error);
      return null;
    }
  }

  /**
   * Update trading settings
   */
  async updateTradingSettings(settings: Partial<TradingSettings>): Promise<TradingSettings> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('sniper_settings')
        .upsert({
          user_id: user.id,
          ...settings,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to update trading settings:', error);
      throw error;
    }
  }

  /**
   * Get recent trade logs
   */
  async getRecentTrades(limit: number = 20): Promise<TradeLog[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('bot_logs')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to get recent trades:', error);
      return [];
    }
  }

  /**
   * Log a trade
   */
  async logTrade(trade: Omit<TradeLog, 'id' | 'user_id' | 'created_at'>): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('bot_logs')
        .insert({
          user_id: user.id,
          ...trade,
        });

      if (error) throw error;
    } catch (error) {
      console.error('Failed to log trade:', error);
    }
  }

  /**
   * Execute a manual trade
   */
  async executeTrade(params: SwapParams): Promise<SwapResult> {
    try {
      // Execute the swap
      const result = await jupiterSwapManager.executeSwap(params);

      // Log the trade
      await this.logTrade({
        wallet_id: '', // Would need to get wallet ID from keypair
        input_mint: params.inputMint,
        output_mint: params.outputMint,
        input_amount: params.amount,
        output_amount: result.outputAmount || 0,
        slippage_percent: (params.slippageBps || 0) / 100,
        price_impact_percent: result.priceImpact || 0,
        transaction_signature: result.signature || '',
        status: result.success ? 'success' : 'failed',
        error_message: result.error,
      });

      return result;
    } catch (error) {
      console.error('Failed to execute trade:', error);
      throw error;
    }
  }

  /**
   * Start auto trading
   */
  async startAutoTrading(): Promise<void> {
    if (this.autoTradingInterval) {
      console.warn('Auto trading is already running');
      return;
    }

    console.log('🤖 Starting auto trading...');

    // Start token detection
    await tokenDetectionEngine.startMonitoring({
      minLiquidity: 1, // 1 SOL minimum liquidity
      maxSupply: 1000000000, // 1B max supply
      requireMetadata: false,
    });

    // Start auto trading loop
    this.autoTradingInterval = setInterval(async () => {
      await this.autoTradingLoop();
    }, 5000); // Check every 5 seconds

    // Update settings
    await this.updateTradingSettings({ auto_trading_enabled: true });
  }

  /**
   * Stop auto trading
   */
  async stopAutoTrading(): Promise<void> {
    console.log('⏹️ Stopping auto trading...');

    if (this.autoTradingInterval) {
      clearInterval(this.autoTradingInterval);
      this.autoTradingInterval = null;
    }

    // Stop token detection
    tokenDetectionEngine.stopMonitoring();

    // Clear price subscriptions
    this.priceSubscriptions.forEach(unsubscribe => unsubscribe());
    this.priceSubscriptions = [];

    // Update settings
    await this.updateTradingSettings({ auto_trading_enabled: false });
  }

  /**
   * Auto trading loop
   */
  private async autoTradingLoop(): Promise<void> {
    try {
      const settings = await this.getTradingSettings();
      if (!settings || !settings.auto_trading_enabled) {
        return;
      }

      // Get recently detected tokens
      const recentTokens = tokenDetectionEngine.getRecentTokens(5); // Last 5 minutes

      for (const token of recentTokens) {
        await this.evaluateTokenForTrading(token, settings);
      }
    } catch (error) {
      console.error('Auto trading loop error:', error);
    }
  }

  /**
   * Evaluate a token for potential trading
   */
  private async evaluateTokenForTrading(token: DetectedToken, settings: TradingSettings): Promise<void> {
    try {
      // Skip if safety score is too low
      if (token.safetyScore < settings.min_safety_score) {
        return;
      }

      // Perform detailed safety analysis
      const safetyAnalysis = await tokenSafetyAnalyzer.analyzeToken(token.mint);
      
      if (safetyAnalysis.riskLevel === 'CRITICAL' || safetyAnalysis.riskLevel === 'HIGH') {
        return;
      }

      // Check if we should buy this token
      const shouldBuy = this.shouldBuyToken(token, safetyAnalysis, settings);
      
      if (shouldBuy) {
        await this.executeSniperBuy(token, settings);
      }
    } catch (error) {
      console.error(`Failed to evaluate token ${token.mint}:`, error);
    }
  }

  /**
   * Determine if we should buy a token
   */
  private shouldBuyToken(
    token: DetectedToken, 
    safetyAnalysis: SafetyAnalysis, 
    settings: TradingSettings
  ): boolean {
    // Check safety score
    if (safetyAnalysis.overallScore < settings.min_safety_score) {
      return false;
    }

    // Check for honeypot
    if (safetyAnalysis.honeypotRisk.isHoneypot) {
      return false;
    }

    // Check liquidity
    const totalLiquidity = token.liquidityPools.reduce((sum, pool) => sum + pool.liquidity, 0);
    if (totalLiquidity < 1) { // Minimum 1 SOL liquidity
      return false;
    }

    // Additional criteria can be added here
    return true;
  }

  /**
   * Execute a sniper buy
   */
  private async executeSniperBuy(token: DetectedToken, settings: TradingSettings): Promise<void> {
    try {
      console.log(`🎯 Executing sniper buy for ${token.symbol || token.mint}`);

      // Get primary wallet
      const primaryWallet = await walletService.getPrimaryWallet();
      if (!primaryWallet) {
        console.warn('No primary wallet found for auto trading');
        return;
      }

      // This would require the wallet to be unlocked
      // For now, we'll just log the potential trade
      console.log(`Would buy ${settings.max_position_size_sol} SOL worth of ${token.symbol || token.mint}`);

      // Log the potential trade
      await this.logTrade({
        wallet_id: primaryWallet.id,
        input_mint: NATIVE_SOL_MINT.toBase58(),
        output_mint: token.mint,
        input_amount: settings.max_position_size_sol,
        output_amount: 0,
        slippage_percent: settings.max_slippage_percent,
        price_impact_percent: 0,
        transaction_signature: 'simulated',
        status: 'pending',
      });
    } catch (error) {
      console.error('Failed to execute sniper buy:', error);
    }
  }

  /**
   * Get dashboard data
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      const [
        walletStats,
        recentTrades,
        tradingSettings,
      ] = await Promise.all([
        walletService.getWalletStats(),
        this.getRecentTrades(10),
        this.getTradingSettings(),
      ]);

      // Get detected tokens
      const detectedTokens = tokenDetectionEngine.getRecentTokens(60); // Last hour

      // Create sniper targets from detected tokens
      const sniperTargets: SniperTarget[] = detectedTokens
        .filter(token => token.safetyScore >= 50)
        .map(token => ({
          mint: token.mint,
          symbol: token.symbol,
          name: token.name,
          maxBuyAmount: tradingSettings?.max_position_size_sol || 0.1,
          safetyScore: token.safetyScore,
          isActive: true,
          detectedAt: token.detectedAt,
        }));

      return {
        portfolio: {
          totalValue: walletStats.totalValue,
          solBalance: 0, // Would need to calculate from balances
          tokenCount: 0, // Would need to calculate from balances
          dayChange: 0, // Would need historical data
          dayChangePercent: 0, // Would need historical data
        },
        recentTrades,
        detectedTokens,
        sniperTargets,
        tradingSettings,
        isAutoTradingActive: !!this.autoTradingInterval,
      };
    } catch (error) {
      console.error('Failed to get dashboard data:', error);
      throw error;
    }
  }

  /**
   * Check if auto trading is active
   */
  isAutoTradingActive(): boolean {
    return !!this.autoTradingInterval;
  }
}

// Export singleton instance
export const tradingService = new TradingService();
