import { Keypair } from '@solana/web3.js';
import { tokenDetectionEngine, DetectedToken } from '../token/detection';
import { tokenSafetyAnalyzer, SafetyAnalysis } from '../token/safety';
import { jupiterSwapManager, SwapParams } from '../jupiter/swap';
import { priceFeedManager, TokenPrice } from '../price/feeds';
import { walletService } from '../services/walletService';
import { tradingService, TradingSettings } from '../services/tradingService';
import { NATIVE_SOL_MINT, SOLANA_CONSTANTS } from '../solana/constants';
import config from '@/config';

export interface BotPosition {
  mint: string;
  symbol?: string;
  entryPrice: number;
  amount: number;
  solInvested: number;
  entryTime: number;
  stopLoss?: number;
  takeProfit?: number;
  currentPrice?: number;
  unrealizedPnL?: number;
  unrealizedPnLPercent?: number;
}

export interface BotStats {
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  totalVolume: number;
  totalPnL: number;
  winRate: number;
  averageHoldTime: number;
  largestWin: number;
  largestLoss: number;
  activePositions: number;
  detectedTokens: number;
}

export interface SniperConfig {
  enabled: boolean;
  maxPositionSize: number;
  minSafetyScore: number;
  maxSlippage: number;
  stopLossPercent: number;
  takeProfitPercent: number;
  priorityFee: number;
  maxPositions: number;
  minLiquidity: number;
  maxSupply: number;
  blacklistedTokens: string[];
  whitelistedTokens: string[];
}

export class SniperBot {
  private isRunning = false;
  private positions: Map<string, BotPosition> = new Map();
  private config: SniperConfig;
  private wallet: Keypair | null = null;
  private stats: BotStats = {
    totalTrades: 0,
    successfulTrades: 0,
    failedTrades: 0,
    totalVolume: 0,
    totalPnL: 0,
    winRate: 0,
    averageHoldTime: 0,
    largestWin: 0,
    largestLoss: 0,
    activePositions: 0,
    detectedTokens: 0,
  };

  // Monitoring intervals
  private detectionInterval: NodeJS.Timeout | null = null;
  private positionMonitorInterval: NodeJS.Timeout | null = null;
  private priceUpdateInterval: NodeJS.Timeout | null = null;

  constructor(config: SniperConfig) {
    this.config = config;
  }

  /**
   * Start the sniper bot
   */
  async start(walletId: string, password: string): Promise<void> {
    if (this.isRunning) {
      throw new Error('Bot is already running');
    }

    console.log('🤖 Starting Sniper Bot...');

    try {
      // Get wallet
      const solanaWallet = await walletService.getSolanaWallet(walletId, password);
      this.wallet = (solanaWallet as any).keypair; // Access the internal keypair

      // Start token detection
      await tokenDetectionEngine.startMonitoring({
        minLiquidity: this.config.minLiquidity,
        maxSupply: this.config.maxSupply,
        excludeKnownTokens: true,
      });

      // Start monitoring loops
      this.startDetectionLoop();
      this.startPositionMonitoring();
      this.startPriceUpdates();

      this.isRunning = true;
      console.log('✅ Sniper Bot started successfully');
    } catch (error) {
      console.error('❌ Failed to start Sniper Bot:', error);
      throw error;
    }
  }

  /**
   * Stop the sniper bot
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    console.log('⏹️ Stopping Sniper Bot...');

    // Stop monitoring
    tokenDetectionEngine.stopMonitoring();

    // Clear intervals
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
      this.detectionInterval = null;
    }
    if (this.positionMonitorInterval) {
      clearInterval(this.positionMonitorInterval);
      this.positionMonitorInterval = null;
    }
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
      this.priceUpdateInterval = null;
    }

    this.isRunning = false;
    this.wallet = null;
    console.log('✅ Sniper Bot stopped');
  }

  /**
   * Start token detection loop
   */
  private startDetectionLoop(): void {
    this.detectionInterval = setInterval(async () => {
      try {
        const recentTokens = tokenDetectionEngine.getRecentTokens(1); // Last minute
        
        for (const token of recentTokens) {
          await this.evaluateToken(token);
        }
        
        this.stats.detectedTokens = tokenDetectionEngine.getDetectedTokens().length;
      } catch (error) {
        console.error('Detection loop error:', error);
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Start position monitoring loop
   */
  private startPositionMonitoring(): void {
    this.positionMonitorInterval = setInterval(async () => {
      try {
        await this.monitorPositions();
      } catch (error) {
        console.error('Position monitoring error:', error);
      }
    }, 10000); // Check every 10 seconds
  }

  /**
   * Start price update loop
   */
  private startPriceUpdates(): void {
    this.priceUpdateInterval = setInterval(async () => {
      try {
        await this.updatePositionPrices();
      } catch (error) {
        console.error('Price update error:', error);
      }
    }, 15000); // Update every 15 seconds
  }

  /**
   * Evaluate a detected token for trading
   */
  private async evaluateToken(token: DetectedToken): Promise<void> {
    try {
      // Skip if blacklisted
      if (this.config.blacklistedTokens.includes(token.mint)) {
        return;
      }

      // Skip if we already have a position
      if (this.positions.has(token.mint)) {
        return;
      }

      // Skip if we have too many positions
      if (this.positions.size >= this.config.maxPositions) {
        return;
      }

      // Basic safety check
      if (token.safetyScore < this.config.minSafetyScore) {
        return;
      }

      // Detailed safety analysis
      const safetyAnalysis = await tokenSafetyAnalyzer.analyzeToken(token.mint);
      
      if (!this.shouldBuyToken(token, safetyAnalysis)) {
        return;
      }

      // Execute buy
      await this.executeBuy(token, safetyAnalysis);
    } catch (error) {
      console.error(`Failed to evaluate token ${token.mint}:`, error);
    }
  }

  /**
   * Determine if we should buy a token
   */
  private shouldBuyToken(token: DetectedToken, analysis: SafetyAnalysis): boolean {
    // Check overall safety score
    if (analysis.overallScore < this.config.minSafetyScore) {
      return false;
    }

    // Check for honeypot
    if (analysis.honeypotRisk.isHoneypot) {
      return false;
    }

    // Check risk level
    if (analysis.riskLevel === 'CRITICAL') {
      return false;
    }

    // Check liquidity
    if (analysis.liquidityAnalysis.totalLiquidity < this.config.minLiquidity) {
      return false;
    }

    // Check if it's a rug pull risk
    if (analysis.liquidityAnalysis.isRugPullRisk) {
      return false;
    }

    // Check authorities (prefer renounced)
    if (analysis.authorityAnalysis.riskScore > 50) {
      return false;
    }

    // Additional custom criteria can be added here
    return true;
  }

  /**
   * Execute a buy order
   */
  private async executeBuy(token: DetectedToken, analysis: SafetyAnalysis): Promise<void> {
    if (!this.wallet) {
      throw new Error('Wallet not available');
    }

    try {
      console.log(`🎯 Executing buy for ${token.symbol || token.mint}`);

      const swapParams: SwapParams = {
        inputMint: NATIVE_SOL_MINT.toBase58(),
        outputMint: token.mint,
        amount: this.config.maxPositionSize,
        slippageBps: this.config.maxSlippage * 100,
        userKeypair: this.wallet,
        priorityFee: this.config.priorityFee,
      };

      const result = await jupiterSwapManager.executeSwap(swapParams);

      if (result.success && result.outputAmount) {
        // Create position
        const position: BotPosition = {
          mint: token.mint,
          symbol: token.symbol,
          entryPrice: result.inputAmount! / result.outputAmount,
          amount: result.outputAmount,
          solInvested: result.inputAmount!,
          entryTime: Date.now(),
          stopLoss: result.inputAmount! * (1 - this.config.stopLossPercent / 100),
          takeProfit: result.inputAmount! * (1 + this.config.takeProfitPercent / 100),
        };

        this.positions.set(token.mint, position);
        this.stats.totalTrades++;
        this.stats.successfulTrades++;
        this.stats.totalVolume += result.inputAmount!;
        this.stats.activePositions = this.positions.size;

        console.log(`✅ Buy executed: ${result.inputAmount} SOL → ${result.outputAmount} ${token.symbol}`);

        // Log trade
        await tradingService.logTrade({
          wallet_id: '', // Would need wallet ID
          input_mint: swapParams.inputMint,
          output_mint: swapParams.outputMint,
          input_amount: result.inputAmount!,
          output_amount: result.outputAmount,
          slippage_percent: (swapParams.slippageBps || 0) / 100,
          price_impact_percent: result.priceImpact || 0,
          transaction_signature: result.signature || '',
          status: 'success',
        });
      } else {
        this.stats.totalTrades++;
        this.stats.failedTrades++;
        console.log(`❌ Buy failed: ${result.error}`);
      }
    } catch (error) {
      this.stats.totalTrades++;
      this.stats.failedTrades++;
      console.error('Buy execution failed:', error);
    }
  }

  /**
   * Monitor existing positions
   */
  private async monitorPositions(): Promise<void> {
    for (const [mint, position] of this.positions.entries()) {
      try {
        await this.checkPositionForExit(mint, position);
      } catch (error) {
        console.error(`Failed to monitor position ${mint}:`, error);
      }
    }
  }

  /**
   * Check if a position should be closed
   */
  private async checkPositionForExit(mint: string, position: BotPosition): Promise<void> {
    if (!position.currentPrice) {
      return;
    }

    const currentValue = position.amount * position.currentPrice;
    const pnl = currentValue - position.solInvested;
    const pnlPercent = (pnl / position.solInvested) * 100;

    // Check stop loss
    if (position.stopLoss && currentValue <= position.stopLoss) {
      console.log(`🛑 Stop loss triggered for ${position.symbol}: ${pnlPercent.toFixed(2)}%`);
      await this.executeSell(mint, position, 'stop_loss');
      return;
    }

    // Check take profit
    if (position.takeProfit && currentValue >= position.takeProfit) {
      console.log(`🎯 Take profit triggered for ${position.symbol}: ${pnlPercent.toFixed(2)}%`);
      await this.executeSell(mint, position, 'take_profit');
      return;
    }

    // Check for rug pull or other exit conditions
    const analysis = await tokenSafetyAnalyzer.getCachedAnalysis(mint);
    if (analysis && analysis.riskLevel === 'CRITICAL') {
      console.log(`⚠️ Risk level changed to CRITICAL for ${position.symbol}, selling immediately`);
      await this.executeSell(mint, position, 'risk_management');
    }
  }

  /**
   * Execute a sell order
   */
  private async executeSell(mint: string, position: BotPosition, reason: string): Promise<void> {
    if (!this.wallet) {
      return;
    }

    try {
      const swapParams: SwapParams = {
        inputMint: mint,
        outputMint: NATIVE_SOL_MINT.toBase58(),
        amount: position.amount,
        slippageBps: this.config.maxSlippage * 100,
        userKeypair: this.wallet,
        priorityFee: this.config.priorityFee,
      };

      const result = await jupiterSwapManager.executeSwap(swapParams);

      if (result.success && result.outputAmount) {
        const pnl = result.outputAmount - position.solInvested;
        const pnlPercent = (pnl / position.solInvested) * 100;
        const holdTime = Date.now() - position.entryTime;

        // Update stats
        this.stats.totalPnL += pnl;
        this.stats.totalVolume += result.outputAmount;
        
        if (pnl > 0) {
          this.stats.largestWin = Math.max(this.stats.largestWin, pnl);
        } else {
          this.stats.largestLoss = Math.min(this.stats.largestLoss, pnl);
        }

        // Remove position
        this.positions.delete(mint);
        this.stats.activePositions = this.positions.size;

        console.log(`💰 Sell executed (${reason}): ${position.amount} ${position.symbol} → ${result.outputAmount} SOL (${pnlPercent.toFixed(2)}%)`);

        // Log trade
        await tradingService.logTrade({
          wallet_id: '', // Would need wallet ID
          input_mint: swapParams.inputMint,
          output_mint: swapParams.outputMint,
          input_amount: position.amount,
          output_amount: result.outputAmount,
          slippage_percent: (swapParams.slippageBps || 0) / 100,
          price_impact_percent: result.priceImpact || 0,
          transaction_signature: result.signature || '',
          status: 'success',
        });
      }
    } catch (error) {
      console.error(`Failed to sell position ${mint}:`, error);
    }
  }

  /**
   * Update current prices for all positions
   */
  private async updatePositionPrices(): Promise<void> {
    if (this.positions.size === 0) {
      return;
    }

    try {
      const mints = Array.from(this.positions.keys());
      const prices = await priceFeedManager.getTokenPrices(mints);

      for (const [mint, position] of this.positions.entries()) {
        const priceInfo = prices[mint];
        if (priceInfo) {
          position.currentPrice = priceInfo.price;
          
          if (position.currentPrice > 0) {
            const currentValue = position.amount * position.currentPrice;
            position.unrealizedPnL = currentValue - position.solInvested;
            position.unrealizedPnLPercent = (position.unrealizedPnL / position.solInvested) * 100;
          }
        }
      }
    } catch (error) {
      console.error('Failed to update position prices:', error);
    }
  }

  /**
   * Get current bot status
   */
  getStatus(): {
    isRunning: boolean;
    positions: BotPosition[];
    stats: BotStats;
    config: SniperConfig;
  } {
    // Update win rate
    if (this.stats.totalTrades > 0) {
      this.stats.winRate = (this.stats.successfulTrades / this.stats.totalTrades) * 100;
    }

    return {
      isRunning: this.isRunning,
      positions: Array.from(this.positions.values()),
      stats: { ...this.stats },
      config: { ...this.config },
    };
  }

  /**
   * Update bot configuration
   */
  updateConfig(newConfig: Partial<SniperConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 Bot configuration updated');
  }

  /**
   * Force close all positions
   */
  async closeAllPositions(): Promise<void> {
    console.log('🚨 Closing all positions...');
    
    for (const [mint, position] of this.positions.entries()) {
      try {
        await this.executeSell(mint, position, 'manual_close');
      } catch (error) {
        console.error(`Failed to close position ${mint}:`, error);
      }
    }
  }
}

// Export singleton instance
export const sniperBot = new SniperBot({
  enabled: false,
  maxPositionSize: 0.1,
  minSafetyScore: 70,
  maxSlippage: 3,
  stopLossPercent: 20,
  takeProfitPercent: 100,
  priorityFee: 5000,
  maxPositions: 5,
  minLiquidity: 1,
  maxSupply: 1000000000,
  blacklistedTokens: [],
  whitelistedTokens: [],
});
