/**
 * Global error handling system for Swift Sniper Fi
 */

export interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  timestamp: string;
  url: string;
  userAgent: string;
  userId?: string;
  context?: string;
  level: 'error' | 'warning' | 'info';
  category: 'ui' | 'api' | 'blockchain' | 'trading' | 'auth' | 'unknown';
  metadata?: Record<string, any>;
}

export interface ErrorHandlerConfig {
  enableConsoleLogging: boolean;
  enableLocalStorage: boolean;
  enableRemoteReporting: boolean;
  maxStoredErrors: number;
  remoteEndpoint?: string;
  apiKey?: string;
}

class GlobalErrorHandler {
  private config: ErrorHandlerConfig;
  private errorQueue: ErrorReport[] = [];
  private isInitialized = false;

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableConsoleLogging: true,
      enableLocalStorage: true,
      enableRemoteReporting: false,
      maxStoredErrors: 50,
      ...config,
    };
  }

  /**
   * Initialize global error handling
   */
  initialize(): void {
    if (this.isInitialized) {
      console.warn('GlobalErrorHandler already initialized');
      return;
    }

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(
        new Error(event.reason?.message || 'Unhandled promise rejection'),
        'promise-rejection',
        'error',
        'unknown',
        { reason: event.reason }
      );
    });

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError(
        new Error(event.message),
        'global-error',
        'error',
        'ui',
        {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        }
      );
    });

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target && event.target !== window) {
        this.handleError(
          new Error(`Resource failed to load: ${(event.target as any).src || (event.target as any).href}`),
          'resource-error',
          'warning',
          'ui',
          { target: event.target }
        );
      }
    }, true);

    this.isInitialized = true;
    console.log('🛡️ Global error handler initialized');
  }

  /**
   * Handle an error with full context
   */
  handleError(
    error: Error,
    context: string = 'unknown',
    level: ErrorReport['level'] = 'error',
    category: ErrorReport['category'] = 'unknown',
    metadata: Record<string, any> = {}
  ): void {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      context,
      level,
      category,
      metadata,
    };

    // Add to queue
    this.errorQueue.push(errorReport);

    // Process error
    this.processError(errorReport);

    // Cleanup old errors
    this.cleanupErrors();
  }

  /**
   * Handle API errors specifically
   */
  handleApiError(
    error: Error,
    endpoint: string,
    method: string = 'GET',
    statusCode?: number,
    responseData?: any
  ): void {
    this.handleError(
      error,
      `api-${method.toLowerCase()}-${endpoint}`,
      'error',
      'api',
      {
        endpoint,
        method,
        statusCode,
        responseData,
      }
    );
  }

  /**
   * Handle blockchain/trading errors
   */
  handleBlockchainError(
    error: Error,
    operation: string,
    transactionSignature?: string,
    additionalData?: any
  ): void {
    this.handleError(
      error,
      `blockchain-${operation}`,
      'error',
      'blockchain',
      {
        operation,
        transactionSignature,
        ...additionalData,
      }
    );
  }

  /**
   * Handle trading errors
   */
  handleTradingError(
    error: Error,
    operation: string,
    tokenMint?: string,
    amount?: number,
    additionalData?: any
  ): void {
    this.handleError(
      error,
      `trading-${operation}`,
      'error',
      'trading',
      {
        operation,
        tokenMint,
        amount,
        ...additionalData,
      }
    );
  }

  /**
   * Log warning
   */
  logWarning(message: string, context: string = 'unknown', metadata: Record<string, any> = {}): void {
    this.handleError(
      new Error(message),
      context,
      'warning',
      'unknown',
      metadata
    );
  }

  /**
   * Log info
   */
  logInfo(message: string, context: string = 'unknown', metadata: Record<string, any> = {}): void {
    this.handleError(
      new Error(message),
      context,
      'info',
      'unknown',
      metadata
    );
  }

  /**
   * Process error report
   */
  private processError(errorReport: ErrorReport): void {
    // Console logging
    if (this.config.enableConsoleLogging) {
      const logMethod = errorReport.level === 'error' ? 'error' : 
                       errorReport.level === 'warning' ? 'warn' : 'info';
      console[logMethod](`[${errorReport.category}] ${errorReport.context}:`, errorReport);
    }

    // Local storage
    if (this.config.enableLocalStorage) {
      this.storeErrorLocally(errorReport);
    }

    // Remote reporting
    if (this.config.enableRemoteReporting && this.config.remoteEndpoint) {
      this.reportErrorRemotely(errorReport);
    }
  }

  /**
   * Store error in local storage
   */
  private storeErrorLocally(errorReport: ErrorReport): void {
    try {
      const stored = JSON.parse(localStorage.getItem('error_reports') || '[]');
      stored.push(errorReport);

      // Keep only recent errors
      if (stored.length > this.config.maxStoredErrors) {
        stored.splice(0, stored.length - this.config.maxStoredErrors);
      }

      localStorage.setItem('error_reports', JSON.stringify(stored));
    } catch (error) {
      console.warn('Failed to store error locally:', error);
    }
  }

  /**
   * Report error to remote service
   */
  private async reportErrorRemotely(errorReport: ErrorReport): Promise<void> {
    try {
      if (!this.config.remoteEndpoint) return;

      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify(errorReport),
      });
    } catch (error) {
      console.warn('Failed to report error remotely:', error);
    }
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup old errors from queue
   */
  private cleanupErrors(): void {
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const cutoff = Date.now() - maxAge;

    this.errorQueue = this.errorQueue.filter(
      error => new Date(error.timestamp).getTime() > cutoff
    );
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byLevel: Record<string, number>;
    byCategory: Record<string, number>;
    recent: ErrorReport[];
  } {
    const byLevel: Record<string, number> = {};
    const byCategory: Record<string, number> = {};

    this.errorQueue.forEach(error => {
      byLevel[error.level] = (byLevel[error.level] || 0) + 1;
      byCategory[error.category] = (byCategory[error.category] || 0) + 1;
    });

    const recent = this.errorQueue
      .slice(-10)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return {
      total: this.errorQueue.length,
      byLevel,
      byCategory,
      recent,
    };
  }

  /**
   * Clear all stored errors
   */
  clearErrors(): void {
    this.errorQueue = [];
    if (this.config.enableLocalStorage) {
      localStorage.removeItem('error_reports');
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export singleton instance
export const globalErrorHandler = new GlobalErrorHandler({
  enableConsoleLogging: true,
  enableLocalStorage: true,
  enableRemoteReporting: false, // Enable when you have a reporting endpoint
  maxStoredErrors: 100,
});

// Convenience functions
export const handleError = (error: Error, context?: string) => 
  globalErrorHandler.handleError(error, context);

export const handleApiError = (error: Error, endpoint: string, method?: string, statusCode?: number) =>
  globalErrorHandler.handleApiError(error, endpoint, method, statusCode);

export const handleBlockchainError = (error: Error, operation: string, signature?: string) =>
  globalErrorHandler.handleBlockchainError(error, operation, signature);

export const handleTradingError = (error: Error, operation: string, tokenMint?: string, amount?: number) =>
  globalErrorHandler.handleTradingError(error, operation, tokenMint, amount);

export const logWarning = (message: string, context?: string) =>
  globalErrorHandler.logWarning(message, context);

export const logInfo = (message: string, context?: string) =>
  globalErrorHandler.logInfo(message, context);

// Initialize on import
if (typeof window !== 'undefined') {
  globalErrorHandler.initialize();
}
