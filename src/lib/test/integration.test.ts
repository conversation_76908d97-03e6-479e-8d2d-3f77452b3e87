/**
 * Integration tests for the Solana Sniper Bot
 * These tests verify that all components work together correctly
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import config from '@/config';
import { connectionManager, getConnection } from '../solana/connection';
import { SolanaWallet, generateWallet } from '../solana/wallet';
import { jupiterAPI } from '../jupiter/api';
import { priceFeedManager } from '../price/feeds';
import { tokenDetectionEngine } from '../token/detection';
import { tokenSafetyAnalyzer } from '../token/safety';
import { NATIVE_SOL_MINT, TOKEN_MINTS } from '../solana/constants';

describe('Solana Integration Tests', () => {
  let testWallet: SolanaWallet;
  
  beforeAll(async () => {
    // Generate a test wallet
    testWallet = generateWallet();
    console.log('Test wallet created:', testWallet.publicKeyString);
    
    // Request airdrop on devnet
    if (config.solana.network === 'devnet') {
      try {
        const signature = await testWallet.airdrop(1);
        console.log('Airdrop successful:', signature);
      } catch (error) {
        console.warn('Airdrop failed (this is normal on mainnet):', error);
      }
    }
  });

  afterAll(async () => {
    // Cleanup
    connectionManager.destroy();
  });

  describe('Configuration', () => {
    it('should have valid configuration', () => {
      expect(config.solana.network).toBeOneOf(['mainnet', 'devnet', 'testnet']);
      expect(config.solana.rpcUrl).toMatch(/^https?:\/\//);
      expect(config.jupiter.apiUrl).toMatch(/^https?:\/\//);
      expect(config.trading.defaultSlippage).toBeGreaterThan(0);
      expect(config.trading.maxSlippage).toBeGreaterThan(config.trading.defaultSlippage);
    });
  });

  describe('Solana Connection', () => {
    it('should connect to Solana network', async () => {
      const connection = getConnection();
      expect(connection).toBeDefined();
      
      const version = await connection.getVersion();
      expect(version).toBeDefined();
      expect(version['solana-core']).toBeDefined();
    });

    it('should get connection info', async () => {
      const info = await connectionManager.getConnectionInfo();
      expect(info.endpoint).toBeDefined();
      expect(info.network).toBe(config.solana.network);
      expect(info.blockHeight).toBeGreaterThan(0);
      expect(info.latestBlockhash).toBeDefined();
    });
  });

  describe('Wallet Management', () => {
    it('should generate a valid wallet', () => {
      const wallet = generateWallet();
      expect(wallet.publicKeyString).toMatch(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/);
      expect(wallet.privateKey).toBeDefined();
      expect(wallet.privateKeyArray).toHaveLength(64);
    });

    it('should get wallet balance', async () => {
      const balance = await testWallet.getSOLBalance();
      expect(balance).toBeGreaterThanOrEqual(0);
    });

    it('should get token balances', async () => {
      const tokenBalances = await testWallet.getTokenBalances();
      expect(Array.isArray(tokenBalances)).toBe(true);
    });

    it('should get all balances', async () => {
      const walletInfo = await testWallet.getAllBalances();
      expect(walletInfo.publicKey).toBe(testWallet.publicKeyString);
      expect(walletInfo.balance).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(walletInfo.tokenBalances)).toBe(true);
    });
  });

  describe('Jupiter API Integration', () => {
    it('should get supported tokens', async () => {
      const tokens = await jupiterAPI.getTokens();
      expect(Array.isArray(tokens)).toBe(true);
      expect(tokens.length).toBeGreaterThan(0);
      
      // Check for SOL token
      const solToken = tokens.find(token => token.address === NATIVE_SOL_MINT.toBase58());
      expect(solToken).toBeDefined();
    });

    it('should get token price', async () => {
      try {
        const price = await jupiterAPI.getPrice(NATIVE_SOL_MINT.toBase58());
        expect(price).toBeDefined();
        expect(price.price).toBeGreaterThan(0);
        expect(price.mintSymbol).toBe('SOL');
      } catch (error) {
        console.warn('Price fetch failed (this may be normal):', error);
      }
    });

    it('should get swap quote', async () => {
      try {
        const quote = await jupiterAPI.getQuote({
          inputMint: NATIVE_SOL_MINT.toBase58(),
          outputMint: TOKEN_MINTS.USDC.toBase58(),
          amount: '1000000000', // 1 SOL
          slippageBps: 50,
        });
        
        expect(quote).toBeDefined();
        expect(quote.inputMint).toBe(NATIVE_SOL_MINT.toBase58());
        expect(quote.outputMint).toBe(TOKEN_MINTS.USDC.toBase58());
        expect(parseInt(quote.outAmount)).toBeGreaterThan(0);
      } catch (error) {
        console.warn('Quote fetch failed (this may be normal):', error);
      }
    });

    it('should validate route', async () => {
      const hasRoute = await jupiterAPI.validateRoute(
        NATIVE_SOL_MINT.toBase58(),
        TOKEN_MINTS.USDC.toBase58()
      );
      expect(typeof hasRoute).toBe('boolean');
    });
  });

  describe('Price Feed Management', () => {
    it('should get token price', async () => {
      try {
        const price = await priceFeedManager.getTokenPrice(NATIVE_SOL_MINT.toBase58());
        if (price) {
          expect(price.mint).toBe(NATIVE_SOL_MINT.toBase58());
          expect(price.price).toBeGreaterThan(0);
          expect(price.lastUpdated).toBeGreaterThan(0);
          expect(['jupiter', 'dexscreener', 'coingecko', 'birdeye']).toContain(price.source);
        }
      } catch (error) {
        console.warn('Price feed test failed (this may be normal):', error);
      }
    });

    it('should format price correctly', () => {
      expect(priceFeedManager.formatPrice(0.000001)).toBe('1.00e-6');
      expect(priceFeedManager.formatPrice(0.001234)).toBe('0.001234');
      expect(priceFeedManager.formatPrice(1.234567)).toBe('1.23');
      expect(priceFeedManager.formatPrice(1234.56)).toBe('1234.56');
    });

    it('should calculate price change', () => {
      expect(priceFeedManager.calculatePriceChange(110, 100)).toBe(10);
      expect(priceFeedManager.calculatePriceChange(90, 100)).toBe(-10);
      expect(priceFeedManager.calculatePriceChange(100, 0)).toBe(0);
    });
  });

  describe('Token Detection Engine', () => {
    it('should initialize without errors', () => {
      expect(tokenDetectionEngine).toBeDefined();
    });

    it('should get detection stats', () => {
      const stats = tokenDetectionEngine.getStats();
      expect(stats.totalDetected).toBeGreaterThanOrEqual(0);
      expect(stats.safeTokens).toBeGreaterThanOrEqual(0);
      expect(stats.riskyTokens).toBeGreaterThanOrEqual(0);
      expect(stats.recentDetections).toBeGreaterThanOrEqual(0);
      expect(stats.averageSafetyScore).toBeGreaterThanOrEqual(0);
    });

    it('should search tokens', () => {
      const results = tokenDetectionEngine.searchTokens('SOL');
      expect(Array.isArray(results)).toBe(true);
    });

    it('should get recent tokens', () => {
      const recent = tokenDetectionEngine.getRecentTokens(60);
      expect(Array.isArray(recent)).toBe(true);
    });

    it('should get top safe tokens', () => {
      const topSafe = tokenDetectionEngine.getTopSafeTokens(5);
      expect(Array.isArray(topSafe)).toBe(true);
      expect(topSafe.length).toBeLessThanOrEqual(5);
    });
  });

  describe('Token Safety Analyzer', () => {
    it('should analyze SOL token safety', async () => {
      try {
        const analysis = await tokenSafetyAnalyzer.analyzeToken(NATIVE_SOL_MINT.toBase58());
        expect(analysis).toBeDefined();
        expect(analysis.mint).toBe(NATIVE_SOL_MINT.toBase58());
        expect(analysis.overallScore).toBeGreaterThanOrEqual(0);
        expect(analysis.overallScore).toBeLessThanOrEqual(100);
        expect(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).toContain(analysis.riskLevel);
        expect(Array.isArray(analysis.checks)).toBe(true);
        expect(analysis.timestamp).toBeGreaterThan(0);
      } catch (error) {
        console.warn('Safety analysis failed (this may be normal):', error);
      }
    });

    it('should get analysis stats', () => {
      const stats = tokenSafetyAnalyzer.getAnalysisStats();
      expect(stats.totalAnalyzed).toBeGreaterThanOrEqual(0);
      expect(stats.safeTokens).toBeGreaterThanOrEqual(0);
      expect(stats.riskyTokens).toBeGreaterThanOrEqual(0);
      expect(stats.honeypots).toBeGreaterThanOrEqual(0);
      expect(stats.cacheSize).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid wallet addresses', () => {
      expect(() => new SolanaWallet.fromPrivateKey('invalid')).toThrow();
    });

    it('should handle network errors gracefully', async () => {
      // This test ensures our error handling works
      try {
        await jupiterAPI.getQuote({
          inputMint: 'invalid-mint',
          outputMint: 'invalid-mint',
          amount: '1000000000',
        });
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('Performance', () => {
    it('should complete basic operations within reasonable time', async () => {
      const start = Date.now();
      
      // Test multiple operations
      await Promise.all([
        testWallet.getSOLBalance(),
        jupiterAPI.getTokens().catch(() => []),
        priceFeedManager.getTokenPrice(NATIVE_SOL_MINT.toBase58()).catch(() => null),
      ]);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(30000); // Should complete within 30 seconds
    });
  });
});

// Helper function to run integration tests
export async function runIntegrationTests(): Promise<void> {
  console.log('🧪 Running integration tests...');
  
  try {
    // Test connection
    const connection = getConnection();
    const version = await connection.getVersion();
    console.log('✅ Solana connection:', version['solana-core']);
    
    // Test wallet
    const wallet = generateWallet();
    const balance = await wallet.getSOLBalance();
    console.log('✅ Wallet balance:', balance, 'SOL');
    
    // Test Jupiter API
    try {
      const tokens = await jupiterAPI.getTokens();
      console.log('✅ Jupiter tokens:', tokens.length);
    } catch (error) {
      console.warn('⚠️ Jupiter API test failed:', error);
    }
    
    // Test price feeds
    try {
      const price = await priceFeedManager.getTokenPrice(NATIVE_SOL_MINT.toBase58());
      console.log('✅ SOL price:', price?.price || 'N/A');
    } catch (error) {
      console.warn('⚠️ Price feed test failed:', error);
    }
    
    // Test token detection
    const stats = tokenDetectionEngine.getStats();
    console.log('✅ Detection stats:', stats);
    
    console.log('🎉 Integration tests completed successfully!');
  } catch (error) {
    console.error('❌ Integration tests failed:', error);
    throw error;
  }
}
