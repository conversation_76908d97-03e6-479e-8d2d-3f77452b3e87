/**
 * Application configuration
 */

export const config = {
  // Application settings
  app: {
    name: 'Swift Sniper Fi',
    version: '1.0.0',
    description: 'Advanced Solana Trading Bot',
    url: import.meta.env.VITE_APP_URL || 'http://localhost:5173',
  },

  // Supabase configuration
  supabase: {
    url: import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co',
    anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key',
    serviceKey: import.meta.env.VITE_SUPABASE_SERVICE_KEY || 'your-service-key',
  },

  // Solana configuration - MAINNET
  solana: {
    network: import.meta.env.VITE_SOLANA_NETWORK || 'mainnet-beta',
    rpcUrl: import.meta.env.VITE_SOLANA_RPC_URL || 'https://mainnet.helius-rpc.com/?api-key=7467031f-f99b-4f0d-b90e-21a70a6cdacf',
    wsUrl: import.meta.env.VITE_SOLANA_WS_URL || 'wss://mainnet.helius-rpc.com/?api-key=7467031f-f99b-4f0d-b90e-21a70a6cdacf',
    commitment: 'confirmed' as const,
    heliusApiKey: import.meta.env.VITE_HELIUS_API_KEY || '7467031f-f99b-4f0d-b90e-21a70a6cdacf',
  },

  // Jupiter configuration
  jupiter: {
    apiUrl: import.meta.env.VITE_JUPITER_API_URL || 'https://quote-api.jup.ag/v6',
    priceApiUrl: import.meta.env.VITE_JUPITER_PRICE_API_URL || 'https://price.jup.ag/v4',
  },

  // External APIs configuration
  apis: {
    dexscreener: import.meta.env.VITE_DEXSCREENER_API_URL || 'https://api.dexscreener.com/latest',
    coingecko: import.meta.env.VITE_COINGECKO_API_URL || 'https://api.coingecko.com/api/v3',
    birdeye: import.meta.env.VITE_BIRDEYE_API_URL || null,
  },

  // Trading configuration - MAINNET (Conservative)
  trading: {
    maxSlippage: 15, // 15% maximum slippage for new tokens
    defaultSlippage: 5, // 5% default slippage
    maxPositionSize: 1, // 1 SOL maximum position (safer for mainnet)
    defaultPositionSize: 0.05, // 0.05 SOL default position (safer)
    priorityFee: 50000, // 50000 lamports for faster execution
    maxRetries: 5,
    retryDelay: 500,
  },

  // Token detection configuration
  tokenDetection: {
    enabled: true,
    minLiquidity: 1, // 1 SOL minimum liquidity
    maxSupply: 1000000000, // 1B maximum supply
    minSafetyScore: 50, // 50% minimum safety score
    scanInterval: 5000, // 5 seconds scan interval
  },

  // Security configuration
  security: {
    encryptionKey: import.meta.env.VITE_ENCRYPTION_KEY || 'default-encryption-key-change-in-production',
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
  },

  // Rate limiting configuration
  rateLimit: {
    api: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100,
    },
    trading: {
      windowMs: 10 * 1000, // 10 seconds
      maxRequests: 5,
    },
    auth: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5,
    },
  },

  // Cache configuration
  cache: {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxSize: 50 * 1024 * 1024, // 50MB
    cleanupInterval: 60 * 1000, // 1 minute
  },

  // Analytics configuration
  analytics: {
    enabled: import.meta.env.VITE_ANALYTICS_ENABLED === 'true',
    trackingId: import.meta.env.VITE_ANALYTICS_TRACKING_ID,
    apiKey: import.meta.env.VITE_ANALYTICS_API_KEY,
  },

  // Error reporting configuration
  errorReporting: {
    enabled: import.meta.env.VITE_ERROR_REPORTING_ENABLED === 'true',
    apiKey: import.meta.env.VITE_ERROR_REPORTING_API_KEY,
    endpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT,
  },

  // Feature flags
  features: {
    autoTrading: true,
    tokenDetection: true,
    portfolioTracking: true,
    multiWallet: true,
    advancedCharts: false,
    socialTrading: false,
    mobileApp: false,
  },

  // API endpoints
  api: {
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
  },

  // UI configuration
  ui: {
    theme: 'dark',
    language: 'en',
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    compactMode: false,
  },

  // Development configuration
  development: {
    enableDevTools: import.meta.env.DEV,
    enableMockData: import.meta.env.VITE_ENABLE_MOCK_DATA === 'true',
    logLevel: import.meta.env.VITE_LOG_LEVEL || 'info',
  },
};

// Environment-specific overrides
if (import.meta.env.PROD) {
  // Production overrides
  config.development.enableDevTools = false;
  config.development.enableMockData = false;
  config.development.logLevel = 'warn';
}

// Validation
const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
];

const missingEnvVars = requiredEnvVars.filter(
  (envVar) => !import.meta.env[envVar]
);

if (missingEnvVars.length > 0 && import.meta.env.PROD) {
  console.error('Missing required environment variables:', missingEnvVars);
}

export default config;
