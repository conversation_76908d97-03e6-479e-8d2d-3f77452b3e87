/**
 * UNIFIED SNIPER BOT HOOK
 * 
 * This hook provides a single interface for all sniper bot functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { 
  unifiedSniperBot, 
  SniperBotSettings, 
  SniperBotStatus, 
  DetectedToken, 
  SniperTrade 
} from '@/lib/services/unifiedSniperBot';
import { toast } from 'sonner';

export function useSniperBot() {
  const { user } = useAuth();
  
  // State
  const [settings, setSettings] = useState<SniperBotSettings | null>(null);
  const [status, setStatus] = useState<SniperBotStatus>({
    isRunning: false,
    tokensDetected: 0,
    tradesExecuted: 0,
    totalProfit: 0,
    lastActivity: 'Never',
    currentPositions: 0
  });
  const [detectedTokens, setDetectedTokens] = useState<DetectedToken[]>([]);
  const [trades, setTrades] = useState<SniperTrade[]>([]);
  const [loading, setLoading] = useState(false);

  /**
   * Load sniper bot settings
   */
  const loadSettings = useCallback(async () => {
    if (!user) return;
    
    try {
      const userSettings = await unifiedSniperBot.getSettings(user.id);
      setSettings(userSettings);
    } catch (error) {
      console.error('Failed to load sniper settings:', error);
    }
  }, [user]);

  /**
   * Load sniper bot status
   */
  const loadStatus = useCallback(async () => {
    if (!user) return;
    
    try {
      const currentStatus = await unifiedSniperBot.getStatus(user.id);
      setStatus(currentStatus);
    } catch (error) {
      console.error('Failed to load sniper status:', error);
    }
  }, [user]);

  /**
   * Load detected tokens
   */
  const loadDetectedTokens = useCallback(async () => {
    try {
      const tokens = await unifiedSniperBot.getDetectedTokens(50);
      setDetectedTokens(tokens);
    } catch (error) {
      console.error('Failed to load detected tokens:', error);
    }
  }, []);

  /**
   * Load sniper trades
   */
  const loadTrades = useCallback(async () => {
    if (!user) return;
    
    try {
      const userTrades = await unifiedSniperBot.getSniperTrades(user.id, 50);
      setTrades(userTrades);
    } catch (error) {
      console.error('Failed to load sniper trades:', error);
    }
  }, [user]);

  /**
   * Update sniper bot settings
   */
  const updateSettings = useCallback(async (newSettings: Partial<SniperBotSettings>) => {
    if (!user) return false;
    
    setLoading(true);
    try {
      const success = await unifiedSniperBot.updateSettings(user.id, newSettings);
      if (success) {
        await loadSettings();
        toast.success('Sniper settings updated successfully');
        return true;
      } else {
        toast.error('Failed to update sniper settings');
        return false;
      }
    } catch (error) {
      console.error('Failed to update settings:', error);
      toast.error('Failed to update sniper settings');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user, loadSettings]);

  /**
   * Start the sniper bot
   */
  const startBot = useCallback(async () => {
    if (!user) return false;
    
    setLoading(true);
    try {
      const success = await unifiedSniperBot.startBot(user.id);
      if (success) {
        await loadStatus();
        toast.success('🚀 Sniper bot started successfully!');
        return true;
      } else {
        toast.error('Failed to start sniper bot');
        return false;
      }
    } catch (error) {
      console.error('Failed to start bot:', error);
      toast.error('Failed to start sniper bot');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user, loadStatus]);

  /**
   * Stop the sniper bot
   */
  const stopBot = useCallback(async () => {
    if (!user) return false;
    
    setLoading(true);
    try {
      const success = await unifiedSniperBot.stopBot(user.id);
      if (success) {
        await loadStatus();
        toast.success('🛑 Sniper bot stopped successfully');
        return true;
      } else {
        toast.error('Failed to stop sniper bot');
        return false;
      }
    } catch (error) {
      console.error('Failed to stop bot:', error);
      toast.error('Failed to stop sniper bot');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user, loadStatus]);

  /**
   * Toggle bot on/off
   */
  const toggleBot = useCallback(async () => {
    if (status.isRunning) {
      return await stopBot();
    } else {
      return await startBot();
    }
  }, [status.isRunning, startBot, stopBot]);

  /**
   * Refresh all data
   */
  const refresh = useCallback(async () => {
    await Promise.all([
      loadSettings(),
      loadStatus(),
      loadDetectedTokens(),
      loadTrades()
    ]);
  }, [loadSettings, loadStatus, loadDetectedTokens, loadTrades]);

  // Load initial data
  useEffect(() => {
    if (user) {
      refresh();
    }
  }, [user, refresh]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!user) return;

    // Subscribe to status updates
    const unsubscribeStatus = unifiedSniperBot.onStatusUpdate((newStatus) => {
      setStatus(newStatus);
    });

    // Subscribe to token detection
    const unsubscribeTokens = unifiedSniperBot.onTokenDetected((newToken) => {
      setDetectedTokens(prev => [newToken, ...prev.slice(0, 49)]);
      toast.info(`🔍 New token detected: ${newToken.token_symbol || newToken.token_address.slice(0, 8)}...`);
    });

    // Subscribe to trade execution
    const unsubscribeTrades = unifiedSniperBot.onTradeExecuted((newTrade) => {
      setTrades(prev => [newTrade, ...prev.slice(0, 49)]);
      const action = newTrade.type === 'snipe_buy' ? 'bought' : 'sold';
      toast.success(`💰 Successfully ${action} token!`);
    });

    return () => {
      unsubscribeStatus();
      unsubscribeTokens();
      unsubscribeTrades();
    };
  }, [user]);

  return {
    // State
    settings,
    status,
    detectedTokens,
    trades,
    loading,
    
    // Actions
    updateSettings,
    startBot,
    stopBot,
    toggleBot,
    refresh,
    
    // Computed
    isRunning: status.isRunning,
    hasSettings: !!settings,
    
    // Quick stats
    stats: {
      tokensDetected: status.tokensDetected,
      tradesExecuted: status.tradesExecuted,
      totalProfit: status.totalProfit,
      currentPositions: status.currentPositions,
      successRate: status.tradesExecuted > 0 ? 
        ((trades.filter(t => t.type === 'snipe_sell' && (t.pnl || 0) > 0).length / status.tradesExecuted) * 100) : 0
    }
  };
}
