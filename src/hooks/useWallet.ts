import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { 
  walletService, 
  StoredWallet, 
  WalletBalance, 
  CreateWalletRequest, 
  ImportWalletRequest 
} from '@/lib/services/walletService';
import { SolanaWallet } from '@/lib/solana/wallet';

export interface UseWalletReturn {
  // Wallet data
  wallets: StoredWallet[];
  primaryWallet: StoredWallet | null;
  balances: WalletBalance[];
  isLoading: boolean;
  error: Error | null;

  // Wallet operations
  createWallet: (request: CreateWalletRequest) => Promise<void>;
  importWallet: (request: ImportWalletRequest) => Promise<void>;
  deleteWallet: (walletId: string) => Promise<void>;
  setPrimaryWallet: (walletId: string) => Promise<void>;
  updateWalletName: (walletId: string, name: string) => Promise<void>;
  
  // Wallet access
  getSolanaWallet: (walletId: string, password: string) => Promise<SolanaWallet>;
  exportPrivateKey: (walletId: string, password: string) => Promise<string>;
  
  // Balance operations
  refreshBalances: () => Promise<void>;
  getWalletBalance: (walletId: string) => WalletBalance | undefined;
  
  // Utility
  isWalletUnlocked: (walletId: string) => boolean;
  lockWallet: (walletId: string) => void;
  unlockWallet: (walletId: string, password: string) => Promise<boolean>;
}

// Cache for unlocked wallets (in memory only)
const unlockedWallets = new Map<string, { wallet: SolanaWallet; expiry: number }>();
const UNLOCK_DURATION = 30 * 60 * 1000; // 30 minutes

export function useWallet(): UseWalletReturn {
  const queryClient = useQueryClient();
  const [error, setError] = useState<Error | null>(null);

  // Query for wallets
  const {
    data: wallets = [],
    isLoading: walletsLoading,
    error: walletsError,
  } = useQuery({
    queryKey: ['wallets'],
    queryFn: () => walletService.getWallets(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Query for primary wallet
  const {
    data: primaryWallet = null,
    isLoading: primaryLoading,
  } = useQuery({
    queryKey: ['wallet', 'primary'],
    queryFn: () => walletService.getPrimaryWallet(),
    staleTime: 5 * 60 * 1000,
  });

  // Query for balances
  const {
    data: balances = [],
    isLoading: balancesLoading,
    error: balancesError,
  } = useQuery({
    queryKey: ['wallet-balances'],
    queryFn: () => walletService.getWalletBalances(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refresh every minute
    enabled: wallets.length > 0,
  });

  const isLoading = walletsLoading || primaryLoading || balancesLoading;

  // Set error state
  useEffect(() => {
    setError(walletsError || balancesError || null);
  }, [walletsError, balancesError]);

  // Create wallet mutation
  const createWalletMutation = useMutation({
    mutationFn: (request: CreateWalletRequest) => walletService.createWallet(request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['wallets'] });
      queryClient.invalidateQueries({ queryKey: ['wallet', 'primary'] });
      toast.success('Wallet created successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create wallet: ${error.message}`);
    },
  });

  // Import wallet mutation
  const importWalletMutation = useMutation({
    mutationFn: (request: ImportWalletRequest) => walletService.importWallet(request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['wallets'] });
      queryClient.invalidateQueries({ queryKey: ['wallet', 'primary'] });
      toast.success('Wallet imported successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to import wallet: ${error.message}`);
    },
  });

  // Delete wallet mutation
  const deleteWalletMutation = useMutation({
    mutationFn: (walletId: string) => walletService.deleteWallet(walletId),
    onSuccess: (_, walletId) => {
      // Remove from unlocked wallets cache
      unlockedWallets.delete(walletId);
      queryClient.invalidateQueries({ queryKey: ['wallets'] });
      queryClient.invalidateQueries({ queryKey: ['wallet', 'primary'] });
      queryClient.invalidateQueries({ queryKey: ['wallet-balances'] });
      toast.success('Wallet deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete wallet: ${error.message}`);
    },
  });

  // Set primary wallet mutation
  const setPrimaryWalletMutation = useMutation({
    mutationFn: (walletId: string) => walletService.setPrimaryWallet(walletId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['wallets'] });
      queryClient.invalidateQueries({ queryKey: ['wallet', 'primary'] });
      toast.success('Primary wallet updated');
    },
    onError: (error: Error) => {
      toast.error(`Failed to set primary wallet: ${error.message}`);
    },
  });

  // Update wallet name mutation
  const updateWalletNameMutation = useMutation({
    mutationFn: ({ walletId, name }: { walletId: string; name: string }) => 
      walletService.updateWalletName(walletId, name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['wallets'] });
      toast.success('Wallet name updated');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update wallet name: ${error.message}`);
    },
  });

  // Wallet operations
  const createWallet = useCallback(async (request: CreateWalletRequest) => {
    await createWalletMutation.mutateAsync(request);
  }, [createWalletMutation]);

  const importWallet = useCallback(async (request: ImportWalletRequest) => {
    await importWalletMutation.mutateAsync(request);
  }, [importWalletMutation]);

  const deleteWallet = useCallback(async (walletId: string) => {
    await deleteWalletMutation.mutateAsync(walletId);
  }, [deleteWalletMutation]);

  const setPrimaryWallet = useCallback(async (walletId: string) => {
    await setPrimaryWalletMutation.mutateAsync(walletId);
  }, [setPrimaryWalletMutation]);

  const updateWalletName = useCallback(async (walletId: string, name: string) => {
    await updateWalletNameMutation.mutateAsync({ walletId, name });
  }, [updateWalletNameMutation]);

  // Get Solana wallet instance
  const getSolanaWallet = useCallback(async (walletId: string, password: string): Promise<SolanaWallet> => {
    // Check if wallet is already unlocked
    const cached = unlockedWallets.get(walletId);
    if (cached && cached.expiry > Date.now()) {
      return cached.wallet;
    }

    // Get wallet from service
    const wallet = await walletService.getSolanaWallet(walletId, password);
    
    // Cache the unlocked wallet
    unlockedWallets.set(walletId, {
      wallet,
      expiry: Date.now() + UNLOCK_DURATION,
    });

    return wallet;
  }, []);

  // Export private key
  const exportPrivateKey = useCallback(async (walletId: string, password: string): Promise<string> => {
    return await walletService.exportWalletPrivateKey(walletId, password);
  }, []);

  // Refresh balances
  const refreshBalances = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['wallet-balances'] });
  }, [queryClient]);

  // Get specific wallet balance
  const getWalletBalance = useCallback((walletId: string): WalletBalance | undefined => {
    return balances.find(balance => balance.walletId === walletId);
  }, [balances]);

  // Check if wallet is unlocked
  const isWalletUnlocked = useCallback((walletId: string): boolean => {
    const cached = unlockedWallets.get(walletId);
    return cached ? cached.expiry > Date.now() : false;
  }, []);

  // Lock wallet
  const lockWallet = useCallback((walletId: string) => {
    unlockedWallets.delete(walletId);
    toast.info('Wallet locked');
  }, []);

  // Unlock wallet
  const unlockWallet = useCallback(async (walletId: string, password: string): Promise<boolean> => {
    try {
      await getSolanaWallet(walletId, password);
      toast.success('Wallet unlocked');
      return true;
    } catch (error) {
      toast.error('Invalid password');
      return false;
    }
  }, [getSolanaWallet]);

  // Cleanup expired unlocked wallets
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      for (const [walletId, cached] of unlockedWallets.entries()) {
        if (cached.expiry <= now) {
          unlockedWallets.delete(walletId);
        }
      }
    }, 60 * 1000); // Check every minute

    return () => clearInterval(cleanup);
  }, []);

  return {
    // Data
    wallets,
    primaryWallet,
    balances,
    isLoading,
    error,

    // Operations
    createWallet,
    importWallet,
    deleteWallet,
    setPrimaryWallet,
    updateWalletName,

    // Access
    getSolanaWallet,
    exportPrivateKey,

    // Balance operations
    refreshBalances,
    getWalletBalance,

    // Utility
    isWalletUnlocked,
    lockWallet,
    unlockWallet,
  };
}
