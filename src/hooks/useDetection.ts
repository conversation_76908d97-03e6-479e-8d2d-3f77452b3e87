/**
 * Detection Hook
 * 
 * React hook for managing token detection state and operations
 */

import { useState, useEffect, useCallback } from 'react';
import { detectionService, DetectionStatus } from '@/lib/services/detectionService';
import { toast } from 'sonner';

export function useDetection() {
  const [status, setStatus] = useState<DetectionStatus>({
    isRunning: false,
    isInitialized: false,
    totalTokensDetected: 0,
    recentTokensCount: 0,
    lastActivity: 'Never',
    detectedTokens: [],
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Initialize detection service
   */
  const initialize = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      await detectionService.initialize();
      setStatus(detectionService.getStatus());
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to initialize detection');
      setError(error);
      console.error('Failed to initialize detection:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Start detection
   */
  const startDetection = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      await detectionService.startDetection();
      toast.success('🔍 Token detection started');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to start detection');
      setError(error);
      toast.error('Failed to start token detection');
      console.error('Failed to start detection:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Stop detection
   */
  const stopDetection = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      await detectionService.stopDetection();
      toast.success('🛑 Token detection stopped');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to stop detection');
      setError(error);
      toast.error('Failed to stop token detection');
      console.error('Failed to stop detection:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Toggle detection on/off
   */
  const toggleDetection = useCallback(async () => {
    if (status.isRunning) {
      await stopDetection();
    } else {
      await startDetection();
    }
  }, [status.isRunning, startDetection, stopDetection]);

  /**
   * Refresh status
   */
  const refreshStatus = useCallback(() => {
    setStatus(detectionService.getStatus());
  }, []);

  // Subscribe to status updates
  useEffect(() => {
    const unsubscribe = detectionService.onStatusUpdate((newStatus) => {
      setStatus(newStatus);
    });

    // Get initial status
    refreshStatus();

    return unsubscribe;
  }, [refreshStatus]);

  // Auto-initialize on mount
  useEffect(() => {
    if (!status.isInitialized && !loading) {
      initialize();
    }
  }, [status.isInitialized, loading, initialize]);

  return {
    // Status
    status,
    loading,
    error,
    
    // Computed values
    isRunning: status.isRunning,
    isInitialized: status.isInitialized,
    totalTokensDetected: status.totalTokensDetected,
    recentTokensCount: status.recentTokensCount,
    lastActivity: status.lastActivity,
    detectedTokens: status.detectedTokens,
    
    // Actions
    initialize,
    startDetection,
    stopDetection,
    toggleDetection,
    refreshStatus,
  };
}
