import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { 
  tradingService, 
  TradingSettings, 
  TradeLog, 
  DashboardData,
  SniperTarget 
} from '@/lib/services/tradingService';
import { SwapParams } from '@/lib/jupiter/swap';
import { DetectedToken } from '@/lib/token/detection';

export interface UseTradingReturn {
  // Dashboard data
  dashboardData: DashboardData | null;
  isLoading: boolean;
  error: Error | null;

  // Trading settings
  tradingSettings: TradingSettings | null;
  updateTradingSettings: (settings: Partial<TradingSettings>) => Promise<void>;

  // Auto trading
  isAutoTradingActive: boolean;
  startAutoTrading: () => Promise<void>;
  stopAutoTrading: () => Promise<void>;

  // Manual trading
  executeTrade: (params: SwapParams) => Promise<void>;

  // Trade history
  recentTrades: TradeLog[];
  refreshTrades: () => Promise<void>;

  // Token detection
  detectedTokens: DetectedToken[];
  sniperTargets: SniperTarget[];

  // Utility
  refreshDashboard: () => Promise<void>;
}

export function useTrading(): UseTradingReturn {
  const queryClient = useQueryClient();
  const [error, setError] = useState<Error | null>(null);
  const [isAutoTradingActive, setIsAutoTradingActive] = useState(false);

  // Query for dashboard data
  const {
    data: dashboardData = null,
    isLoading: dashboardLoading,
    error: dashboardError,
  } = useQuery({
    queryKey: ['dashboard-data'],
    queryFn: () => tradingService.getDashboardData(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refresh every minute
  });

  // Query for trading settings
  const {
    data: tradingSettings = null,
    isLoading: settingsLoading,
    error: settingsError,
  } = useQuery({
    queryKey: ['trading-settings'],
    queryFn: () => tradingService.getTradingSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Query for recent trades
  const {
    data: recentTrades = [],
    isLoading: tradesLoading,
    error: tradesError,
  } = useQuery({
    queryKey: ['recent-trades'],
    queryFn: () => tradingService.getRecentTrades(20),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refresh every 30 seconds
  });

  const isLoading = dashboardLoading || settingsLoading || tradesLoading;

  // Set error state
  useEffect(() => {
    setError(dashboardError || settingsError || tradesError || null);
  }, [dashboardError, settingsError, tradesError]);

  // Check auto trading status
  useEffect(() => {
    setIsAutoTradingActive(tradingService.isAutoTradingActive());
  }, []);

  // Update trading settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: (settings: Partial<TradingSettings>) => 
      tradingService.updateTradingSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trading-settings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-data'] });
      toast.success('Trading settings updated');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update settings: ${error.message}`);
    },
  });

  // Start auto trading mutation
  const startAutoTradingMutation = useMutation({
    mutationFn: () => tradingService.startAutoTrading(),
    onSuccess: () => {
      setIsAutoTradingActive(true);
      queryClient.invalidateQueries({ queryKey: ['trading-settings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-data'] });
      toast.success('Auto trading started');
    },
    onError: (error: Error) => {
      toast.error(`Failed to start auto trading: ${error.message}`);
    },
  });

  // Stop auto trading mutation
  const stopAutoTradingMutation = useMutation({
    mutationFn: () => tradingService.stopAutoTrading(),
    onSuccess: () => {
      setIsAutoTradingActive(false);
      queryClient.invalidateQueries({ queryKey: ['trading-settings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-data'] });
      toast.success('Auto trading stopped');
    },
    onError: (error: Error) => {
      toast.error(`Failed to stop auto trading: ${error.message}`);
    },
  });

  // Execute trade mutation
  const executeTradeMutation = useMutation({
    mutationFn: (params: SwapParams) => tradingService.executeTrade(params),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['recent-trades'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-data'] });
      
      if (result.success) {
        toast.success(`Trade executed successfully! Signature: ${result.signature?.slice(0, 8)}...`);
      } else {
        toast.error(`Trade failed: ${result.error}`);
      }
    },
    onError: (error: Error) => {
      toast.error(`Failed to execute trade: ${error.message}`);
    },
  });

  // Trading operations
  const updateTradingSettings = useCallback(async (settings: Partial<TradingSettings>) => {
    await updateSettingsMutation.mutateAsync(settings);
  }, [updateSettingsMutation]);

  const startAutoTrading = useCallback(async () => {
    await startAutoTradingMutation.mutateAsync();
  }, [startAutoTradingMutation]);

  const stopAutoTrading = useCallback(async () => {
    await stopAutoTradingMutation.mutateAsync();
  }, [stopAutoTradingMutation]);

  const executeTrade = useCallback(async (params: SwapParams) => {
    await executeTradeMutation.mutateAsync(params);
  }, [executeTradeMutation]);

  // Refresh functions
  const refreshTrades = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['recent-trades'] });
  }, [queryClient]);

  const refreshDashboard = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['dashboard-data'] });
  }, [queryClient]);

  // Extract data from dashboard
  const detectedTokens = dashboardData?.detectedTokens || [];
  const sniperTargets = dashboardData?.sniperTargets || [];

  return {
    // Dashboard data
    dashboardData,
    isLoading,
    error,

    // Trading settings
    tradingSettings,
    updateTradingSettings,

    // Auto trading
    isAutoTradingActive,
    startAutoTrading,
    stopAutoTrading,

    // Manual trading
    executeTrade,

    // Trade history
    recentTrades,
    refreshTrades,

    // Token detection
    detectedTokens,
    sniperTargets,

    // Utility
    refreshDashboard,
  };
}
