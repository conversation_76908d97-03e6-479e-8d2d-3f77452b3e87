import { useEffect, useRef, useState, useCallback } from 'react';
import { globalErrorHandler } from '@/lib/error/globalHandler';

export interface PerformanceMetrics {
  renderTime: number;
  componentMountTime: number;
  memoryUsage?: number;
  networkRequests: number;
  cacheHits: number;
  cacheMisses: number;
}

export interface PerformanceEntry {
  name: string;
  startTime: number;
  duration: number;
  type: 'navigation' | 'resource' | 'measure' | 'mark';
}

export interface PerformanceReport {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  timeToInteractive: number;
  totalBlockingTime: number;
}

/**
 * Hook for monitoring component performance
 */
export function useComponentPerformance(componentName: string) {
  const mountTimeRef = useRef<number>(Date.now());
  const renderCountRef = useRef<number>(0);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentMountTime: 0,
    networkRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
  });

  // Track component mount time
  useEffect(() => {
    const mountTime = Date.now() - mountTimeRef.current;
    setMetrics(prev => ({
      ...prev,
      componentMountTime: mountTime,
    }));

    // Log slow mounts
    if (mountTime > 1000) {
      globalErrorHandler.logWarning(
        `Slow component mount: ${componentName} took ${mountTime}ms`,
        'performance',
        { componentName, mountTime }
      );
    }

    return () => {
      // Component unmount cleanup
      const totalLifetime = Date.now() - mountTimeRef.current;
      console.log(`Component ${componentName} lifetime: ${totalLifetime}ms, renders: ${renderCountRef.current}`);
    };
  }, [componentName]);

  // Track render performance
  useEffect(() => {
    renderCountRef.current++;
    const renderStart = performance.now();
    
    // Use requestAnimationFrame to measure render time
    requestAnimationFrame(() => {
      const renderTime = performance.now() - renderStart;
      setMetrics(prev => ({
        ...prev,
        renderTime,
      }));

      // Log slow renders
      if (renderTime > 16) { // 60fps = 16.67ms per frame
        globalErrorHandler.logWarning(
          `Slow render: ${componentName} took ${renderTime.toFixed(2)}ms`,
          'performance',
          { componentName, renderTime, renderCount: renderCountRef.current }
        );
      }
    });
  });

  const trackNetworkRequest = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      networkRequests: prev.networkRequests + 1,
    }));
  }, []);

  const trackCacheHit = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      cacheHits: prev.cacheHits + 1,
    }));
  }, []);

  const trackCacheMiss = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      cacheMisses: prev.cacheMisses + 1,
    }));
  }, []);

  return {
    metrics,
    trackNetworkRequest,
    trackCacheHit,
    trackCacheMiss,
  };
}

/**
 * Hook for monitoring page performance
 */
export function usePagePerformance() {
  const [report, setReport] = useState<Partial<PerformanceReport>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const measurePerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = performance.getEntriesByType('paint');
        
        const newReport: Partial<PerformanceReport> = {
          pageLoadTime: navigation ? navigation.loadEventEnd - navigation.navigationStart : 0,
        };

        // First Contentful Paint
        const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
        if (fcp) {
          newReport.firstContentfulPaint = fcp.startTime;
        }

        // Largest Contentful Paint
        if ('PerformanceObserver' in window) {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            if (lastEntry) {
              setReport(prev => ({
                ...prev,
                largestContentfulPaint: lastEntry.startTime,
              }));
            }
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            setReport(prev => ({
              ...prev,
              cumulativeLayoutShift: clsValue,
            }));
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              setReport(prev => ({
                ...prev,
                firstInputDelay: (entry as any).processingStart - entry.startTime,
              }));
            }
          });
          fidObserver.observe({ entryTypes: ['first-input'] });
        }

        setReport(newReport);
        setIsLoading(false);

        // Log performance issues
        if (newReport.pageLoadTime && newReport.pageLoadTime > 3000) {
          globalErrorHandler.logWarning(
            `Slow page load: ${newReport.pageLoadTime}ms`,
            'performance',
            newReport
          );
        }

      } catch (error) {
        console.warn('Failed to measure performance:', error);
        setIsLoading(false);
      }
    };

    // Wait for page to be fully loaded
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
      return () => window.removeEventListener('load', measurePerformance);
    }
  }, []);

  return { report, isLoading };
}

/**
 * Hook for monitoring memory usage
 */
export function useMemoryMonitoring() {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  } | null>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        });

        // Warn about high memory usage
        const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        if (usagePercent > 80) {
          globalErrorHandler.logWarning(
            `High memory usage: ${usagePercent.toFixed(1)}%`,
            'performance',
            { memoryInfo: memory }
          );
        }
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
}

/**
 * Hook for tracking user interactions
 */
export function useInteractionTracking() {
  const [interactions, setInteractions] = useState<{
    clicks: number;
    scrolls: number;
    keyPresses: number;
    touches: number;
  }>({
    clicks: 0,
    scrolls: 0,
    keyPresses: 0,
    touches: 0,
  });

  useEffect(() => {
    const handleClick = () => {
      setInteractions(prev => ({ ...prev, clicks: prev.clicks + 1 }));
    };

    const handleScroll = () => {
      setInteractions(prev => ({ ...prev, scrolls: prev.scrolls + 1 }));
    };

    const handleKeyPress = () => {
      setInteractions(prev => ({ ...prev, keyPresses: prev.keyPresses + 1 }));
    };

    const handleTouch = () => {
      setInteractions(prev => ({ ...prev, touches: prev.touches + 1 }));
    };

    document.addEventListener('click', handleClick);
    document.addEventListener('scroll', handleScroll);
    document.addEventListener('keypress', handleKeyPress);
    document.addEventListener('touchstart', handleTouch);

    return () => {
      document.removeEventListener('click', handleClick);
      document.removeEventListener('scroll', handleScroll);
      document.removeEventListener('keypress', handleKeyPress);
      document.removeEventListener('touchstart', handleTouch);
    };
  }, []);

  return interactions;
}

/**
 * Performance monitoring utilities
 */
export const performanceUtils = {
  /**
   * Measure function execution time
   */
  measureFunction: <T extends (...args: any[]) => any>(
    fn: T,
    name: string
  ): T => {
    return ((...args: any[]) => {
      const start = performance.now();
      const result = fn(...args);
      const duration = performance.now() - start;

      if (duration > 100) {
        globalErrorHandler.logWarning(
          `Slow function execution: ${name} took ${duration.toFixed(2)}ms`,
          'performance',
          { functionName: name, duration, args: args.length }
        );
      }

      return result;
    }) as T;
  },

  /**
   * Measure async function execution time
   */
  measureAsyncFunction: <T extends (...args: any[]) => Promise<any>>(
    fn: T,
    name: string
  ): T => {
    return (async (...args: any[]) => {
      const start = performance.now();
      try {
        const result = await fn(...args);
        const duration = performance.now() - start;

        if (duration > 1000) {
          globalErrorHandler.logWarning(
            `Slow async function: ${name} took ${duration.toFixed(2)}ms`,
            'performance',
            { functionName: name, duration, args: args.length }
          );
        }

        return result;
      } catch (error) {
        const duration = performance.now() - start;
        globalErrorHandler.handleError(
          error as Error,
          `async-function-${name}`,
          'error',
          'unknown',
          { functionName: name, duration, args: args.length }
        );
        throw error;
      }
    }) as T;
  },

  /**
   * Create a performance mark
   */
  mark: (name: string) => {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  },

  /**
   * Measure between two marks
   */
  measure: (name: string, startMark: string, endMark?: string) => {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name, 'measure')[0];
        return measure ? measure.duration : 0;
      } catch (error) {
        console.warn('Failed to create performance measure:', error);
        return 0;
      }
    }
    return 0;
  },

  /**
   * Get all performance entries
   */
  getAllEntries: (): PerformanceEntry[] => {
    if ('performance' in window) {
      return performance.getEntries().map(entry => ({
        name: entry.name,
        startTime: entry.startTime,
        duration: entry.duration,
        type: entry.entryType as any,
      }));
    }
    return [];
  },

  /**
   * Clear performance entries
   */
  clearEntries: () => {
    if ('performance' in window && 'clearMarks' in performance) {
      performance.clearMarks();
      performance.clearMeasures();
    }
  },
};
