import { Navigation } from "@/components/Navigation";
import { useAuth } from "@/hooks/useAuth";
import { useTrading } from "@/hooks/useTrading";
import { useWallet } from "@/hooks/useWallet";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Activity,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Zap,
  Target,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Play,
  Pause,
  Settings,
  Eye,
  Clock,
  Wallet,
  Bot
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { formatPrice } from "@/lib/price/feeds";
import { toast } from "sonner";

const Dashboard = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const { wallets, primaryWallet, balances, isLoading: walletsLoading } = useWallet();
  const {
    dashboardData,
    isLoading: tradingLoading,
    tradingSettings,
    updateTradingSettings,
    isAutoTradingActive,
    startAutoTrading,
    stopAutoTrading,
    recentTrades,
    detectedTokens,
    sniperTargets,
    refreshDashboard,
  } = useTrading();

  // Settings form state
  const [settingsForm, setSettingsForm] = useState({
    maxPositionSizeSol: 0.1,
    minSafetyScore: 70,
    maxSlippagePercent: 3,
    stopLossPercent: 20,
    takeProfitPercent: 100,
    priorityFeeLamports: 5000,
  });

  useEffect(() => {
    if (!authLoading && !user) {
      navigate("/auth");
    }
  }, [user, authLoading, navigate]);

  // Update form when settings load
  useEffect(() => {
    if (tradingSettings) {
      setSettingsForm({
        maxPositionSizeSol: tradingSettings.max_position_size_sol,
        minSafetyScore: tradingSettings.min_safety_score,
        maxSlippagePercent: tradingSettings.max_slippage_percent,
        stopLossPercent: tradingSettings.stop_loss_percent,
        takeProfitPercent: tradingSettings.take_profit_percent,
        priorityFeeLamports: tradingSettings.priority_fee_lamports,
      });
    }
  }, [tradingSettings]);

  if (authLoading || walletsLoading || tradingLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground flex items-center gap-2">
          <RefreshCw className="w-4 h-4 animate-spin" />
          Loading dashboard...
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Calculate portfolio stats with safety checks
  const safeBalances = balances || [];
  const safeWallets = wallets || [];
  const safeRecentTrades = recentTrades || [];
  const safeDetectedTokens = detectedTokens || [];
  const safeSniperTargets = sniperTargets || [];

  const totalValue = safeBalances.reduce((sum, balance) => sum + (balance.totalValue || 0), 0);
  const totalSolBalance = safeBalances.reduce((sum, balance) => sum + (balance.solBalance || 0), 0);
  const totalTokens = safeBalances.reduce((sum, balance) => sum + (balance.tokens?.length || 0), 0);

  const handleSaveSettings = async () => {
    try {
      await updateTradingSettings({
        max_position_size_sol: settingsForm.maxPositionSizeSol,
        min_safety_score: settingsForm.minSafetyScore,
        max_slippage_percent: settingsForm.maxSlippagePercent,
        stop_loss_percent: settingsForm.stopLossPercent,
        take_profit_percent: settingsForm.takeProfitPercent,
        priority_fee_lamports: settingsForm.priorityFeeLamports,
      });
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  const handleToggleAutoTrading = async () => {
    try {
      if (isAutoTradingActive) {
        await stopAutoTrading();
      } else {
        if (!primaryWallet) {
          toast.error('Please set a primary wallet before starting auto trading');
          return;
        }
        await startAutoTrading();
      }
    } catch (error) {
      console.error('Failed to toggle auto trading:', error);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
                <Activity className="w-8 h-8 text-primary" />
                Trading Dashboard
              </h1>
              <p className="text-muted-foreground mt-2">
                Monitor and control your Solana sniper bot operations.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshDashboard}
                className="flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Refresh
              </Button>
              <Button
                variant={isAutoTradingActive ? "destructive" : "hero"}
                onClick={handleToggleAutoTrading}
                className="flex items-center gap-2"
              >
                {isAutoTradingActive ? (
                  <>
                    <Pause className="w-4 h-4" />
                    Stop Bot
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    Start Bot
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Status Alert */}
          {!primaryWallet && (
            <Card className="border-yellow-500/50 bg-yellow-500/10 mb-6">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  <div>
                    <p className="text-foreground font-medium">No Primary Wallet Set</p>
                    <p className="text-muted-foreground text-sm">
                      Please set a primary wallet in the{" "}
                      <Button
                        variant="link"
                        className="p-0 h-auto text-primary"
                        onClick={() => navigate("/wallet")}
                      >
                        Wallet Management
                      </Button>{" "}
                      section to enable auto trading.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Portfolio Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="border-border bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">Portfolio Value</p>
                    <p className="text-2xl font-bold text-foreground">
                      ${formatPrice(totalValue)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {totalSolBalance.toFixed(4)} SOL
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-border bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">Active Wallets</p>
                    <p className="text-2xl font-bold text-foreground">{safeWallets.length}</p>
                    <p className="text-xs text-muted-foreground">
                      {primaryWallet ? 'Primary set' : 'No primary'}
                    </p>
                  </div>
                  <Wallet className="w-8 h-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-border bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">Bot Status</p>
                    <p className="text-2xl font-bold text-foreground flex items-center gap-2">
                      {isAutoTradingActive ? (
                        <>
                          <span className="text-green-500">Active</span>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        </>
                      ) : (
                        <>
                          <span className="text-muted-foreground">Inactive</span>
                          <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                        </>
                      )}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {safeRecentTrades.length} total trades
                    </p>
                  </div>
                  <Bot className="w-8 h-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-border bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">Detected Tokens</p>
                    <p className="text-2xl font-bold text-foreground">{safeDetectedTokens.length}</p>
                    <p className="text-xs text-muted-foreground">
                      {safeSniperTargets.length} targets
                    </p>
                  </div>
                  <Target className="w-8 h-8 text-primary" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Dashboard Tabs */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tokens">Detected Tokens</TabsTrigger>
              <TabsTrigger value="trades">Trade History</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Trades */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="w-5 h-5" />
                      Recent Trades
                    </CardTitle>
                    <CardDescription>
                      Latest trading activity from your bot
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {safeRecentTrades.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p>No trades yet</p>
                          <p className="text-sm">Start the bot to begin trading</p>
                        </div>
                      ) : (
                        safeRecentTrades.slice(0, 5).map((trade) => (
                          <div key={trade.id} className="flex items-center justify-between p-3 rounded-lg bg-background/50">
                            <div className="flex items-center gap-3">
                              {trade.status === 'success' ? (
                                <CheckCircle className="w-4 h-4 text-green-500" />
                              ) : trade.status === 'failed' ? (
                                <XCircle className="w-4 h-4 text-red-500" />
                              ) : (
                                <Clock className="w-4 h-4 text-yellow-500" />
                              )}
                              <div>
                                <p className="text-sm font-medium text-foreground">
                                  {trade.input_amount.toFixed(4)} SOL → Token
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {new Date(trade.created_at).toLocaleTimeString()}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <Badge variant={
                                trade.status === 'success' ? 'default' :
                                trade.status === 'failed' ? 'destructive' : 'secondary'
                              }>
                                {trade.status}
                              </Badge>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Sniper Targets */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Sniper Targets
                    </CardTitle>
                    <CardDescription>
                      Tokens being monitored for trading opportunities
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {safeSniperTargets.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          <Target className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p>No targets detected</p>
                          <p className="text-sm">Bot will find targets automatically</p>
                        </div>
                      ) : (
                        safeSniperTargets.slice(0, 5).map((target, index) => (
                          <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-background/50">
                            <div className="flex items-center gap-3">
                              <div className={`w-3 h-3 rounded-full ${target.isActive ? 'bg-green-500' : 'bg-muted-foreground'}`}></div>
                              <div>
                                <p className="text-sm font-medium text-foreground">
                                  {target.symbol || target.mint.slice(0, 8) + '...'}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  Safety: {target.safetyScore}/100
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-foreground">
                                {target.maxBuyAmount} SOL
                              </p>
                              <Badge variant={target.safetyScore >= 70 ? 'default' : 'secondary'}>
                                {target.safetyScore >= 70 ? 'Safe' : 'Risky'}
                              </Badge>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Detected Tokens Tab */}
            <TabsContent value="tokens" className="space-y-6">
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="w-5 h-5" />
                    Recently Detected Tokens
                  </CardTitle>
                  <CardDescription>
                    New tokens discovered by the detection engine
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {safeDetectedTokens.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No tokens detected recently</p>
                        <p className="text-sm">Start the bot to begin token detection</p>
                      </div>
                    ) : (
                      safeDetectedTokens.map((token) => (
                        <div key={token.mint} className="flex items-center justify-between p-4 rounded-lg bg-background/50 border border-border/50">
                          <div className="flex items-center gap-4">
                            <div className="flex flex-col items-center">
                              <Shield className={`w-5 h-5 ${
                                token.safetyScore >= 70 ? 'text-green-500' :
                                token.safetyScore >= 50 ? 'text-yellow-500' : 'text-red-500'
                              }`} />
                              <span className="text-xs text-muted-foreground mt-1">
                                {token.safetyScore}
                              </span>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-foreground">
                                {token.symbol || 'Unknown'}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {token.name || token.mint.slice(0, 16) + '...'}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Detected {new Date(token.detectedAt).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={
                                token.safetyScore >= 70 ? 'default' :
                                token.safetyScore >= 50 ? 'secondary' : 'destructive'
                              }>
                                {token.safetyScore >= 70 ? 'Safe' :
                                 token.safetyScore >= 50 ? 'Medium' : 'Risky'}
                              </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              Supply: {(token.supply / 1e6).toFixed(1)}M
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Pools: {token.liquidityPools.length}
                            </p>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Trade History Tab */}
            <TabsContent value="trades" className="space-y-6">
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Complete Trade History
                  </CardTitle>
                  <CardDescription>
                    All trades executed by your bot
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {safeRecentTrades.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No trades executed yet</p>
                        <p className="text-sm">Your trading history will appear here</p>
                      </div>
                    ) : (
                      safeRecentTrades.map((trade) => (
                        <div key={trade.id} className="flex items-center justify-between p-4 rounded-lg bg-background/50 border border-border/50">
                          <div className="flex items-center gap-4">
                            {trade.status === 'success' ? (
                              <CheckCircle className="w-5 h-5 text-green-500" />
                            ) : trade.status === 'failed' ? (
                              <XCircle className="w-5 h-5 text-red-500" />
                            ) : (
                              <Clock className="w-5 h-5 text-yellow-500" />
                            )}
                            <div>
                              <p className="text-sm font-medium text-foreground">
                                {trade.input_amount.toFixed(4)} SOL → {trade.output_amount.toFixed(2)} Tokens
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Slippage: {trade.slippage_percent.toFixed(2)}% |
                                Impact: {trade.price_impact_percent.toFixed(2)}%
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(trade.created_at).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge variant={
                              trade.status === 'success' ? 'default' :
                              trade.status === 'failed' ? 'destructive' : 'secondary'
                            }>
                              {trade.status}
                            </Badge>
                            {trade.transaction_signature && trade.transaction_signature !== 'simulated' && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {trade.transaction_signature.slice(0, 8)}...
                              </p>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Trading Settings
                  </CardTitle>
                  <CardDescription>
                    Configure your bot's trading parameters and risk management
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="max-position">Max Position Size (SOL)</Label>
                        <Input
                          id="max-position"
                          type="number"
                          step="0.01"
                          min="0.01"
                          max="10"
                          value={settingsForm.maxPositionSizeSol}
                          onChange={(e) => setSettingsForm(prev => ({
                            ...prev,
                            maxPositionSizeSol: parseFloat(e.target.value) || 0
                          }))}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Maximum SOL amount to spend per trade
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="min-safety">Minimum Safety Score</Label>
                        <Input
                          id="min-safety"
                          type="number"
                          min="0"
                          max="100"
                          value={settingsForm.minSafetyScore}
                          onChange={(e) => setSettingsForm(prev => ({
                            ...prev,
                            minSafetyScore: parseInt(e.target.value) || 0
                          }))}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Only trade tokens with safety score above this threshold
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="max-slippage">Max Slippage (%)</Label>
                        <Input
                          id="max-slippage"
                          type="number"
                          step="0.1"
                          min="0.1"
                          max="50"
                          value={settingsForm.maxSlippagePercent}
                          onChange={(e) => setSettingsForm(prev => ({
                            ...prev,
                            maxSlippagePercent: parseFloat(e.target.value) || 0
                          }))}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Maximum acceptable slippage for trades
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="stop-loss">Stop Loss (%)</Label>
                        <Input
                          id="stop-loss"
                          type="number"
                          step="1"
                          min="1"
                          max="90"
                          value={settingsForm.stopLossPercent}
                          onChange={(e) => setSettingsForm(prev => ({
                            ...prev,
                            stopLossPercent: parseFloat(e.target.value) || 0
                          }))}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Automatically sell if price drops by this percentage
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="take-profit">Take Profit (%)</Label>
                        <Input
                          id="take-profit"
                          type="number"
                          step="1"
                          min="1"
                          max="1000"
                          value={settingsForm.takeProfitPercent}
                          onChange={(e) => setSettingsForm(prev => ({
                            ...prev,
                            takeProfitPercent: parseFloat(e.target.value) || 0
                          }))}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Automatically sell if price increases by this percentage
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="priority-fee">Priority Fee (Lamports)</Label>
                        <Input
                          id="priority-fee"
                          type="number"
                          step="1000"
                          min="1000"
                          max="100000"
                          value={settingsForm.priorityFeeLamports}
                          onChange={(e) => setSettingsForm(prev => ({
                            ...prev,
                            priorityFeeLamports: parseInt(e.target.value) || 0
                          }))}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Higher fees increase transaction priority
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-foreground">Auto Trading</h4>
                      <p className="text-xs text-muted-foreground">
                        Enable automatic token detection and trading
                      </p>
                    </div>
                    <Switch
                      checked={isAutoTradingActive}
                      onCheckedChange={handleToggleAutoTrading}
                    />
                  </div>

                  <div className="flex gap-3 pt-4">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => {
                        // Reset form to current settings
                        if (tradingSettings) {
                          setSettingsForm({
                            maxPositionSizeSol: tradingSettings.max_position_size_sol,
                            minSafetyScore: tradingSettings.min_safety_score,
                            maxSlippagePercent: tradingSettings.max_slippage_percent,
                            stopLossPercent: tradingSettings.stop_loss_percent,
                            takeProfitPercent: tradingSettings.take_profit_percent,
                            priorityFeeLamports: tradingSettings.priority_fee_lamports,
                          });
                        }
                      }}
                    >
                      Reset
                    </Button>
                    <Button
                      className="flex-1"
                      onClick={handleSaveSettings}
                    >
                      Save Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;