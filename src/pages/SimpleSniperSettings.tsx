/**
 * <PERSON><PERSON><PERSON><PERSON>IED SNIPER BOT SETTINGS
 * Focus ONLY on essential sniper bot configuration
 */

import { Navigation } from "@/components/Navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Bot,
  Zap,
  DollarSign,
  Percent,
  Users,
  Shield,
  Save,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { startSniperBot, stopSniperBot, getSniperBotStatus } from "@/lib/services/simpleSniperBot";

interface SniperSettings {
  is_active: boolean;
  min_liquidity_sol: number;
  max_buy_amount_sol: number;
  target_profit_percent: number;
  stop_loss_percent: number;
  check_honeypot: boolean;
  check_mint_authority: boolean;
  check_freeze_authority: boolean;
  min_holders: number;
}

const SimpleSniperSettings = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<SniperSettings>({
    is_active: false,
    min_liquidity_sol: 5.0,
    max_buy_amount_sol: 0.1,
    target_profit_percent: 100,
    stop_loss_percent: 50,
    check_honeypot: true,
    check_mint_authority: true,
    check_freeze_authority: true,
    min_holders: 10,
  });
  const [loading, setLoading] = useState(false);
  const [botStatus, setBotStatus] = useState({ isRunning: false, settings: null });

  useEffect(() => {
    if (user) {
      loadSettings();
      updateBotStatus();
    }
  }, [user]);

  const loadSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('sniper_settings')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setSettings(data);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      toast.error('Failed to load settings');
    }
  };

  const saveSettings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('sniper_settings')
        .upsert({
          user_id: user.id,
          ...settings,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      toast.success('Settings saved successfully!');
      
      // Restart bot if it was running
      if (botStatus.isRunning) {
        stopSniperBot();
        await startSniperBot(user.id);
        updateBotStatus();
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const toggleBot = async () => {
    if (!user) return;

    try {
      if (botStatus.isRunning) {
        stopSniperBot();
        toast.success('Sniper bot stopped');
      } else {
        await startSniperBot(user.id);
        toast.success('Sniper bot started!');
      }
      updateBotStatus();
    } catch (error) {
      console.error('Failed to toggle bot:', error);
      toast.error('Failed to toggle bot');
    }
  };

  const updateBotStatus = () => {
    setBotStatus(getSniperBotStatus());
  };

  const updateSetting = (key: keyof SniperSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
                <Zap className="w-8 h-8 text-primary" />
                Sniper Bot Settings
              </h1>
              <p className="text-muted-foreground mt-2">
                Configure your automated token sniper bot
              </p>
              <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-yellow-600 text-sm flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4" />
                  <strong>DEMO MODE:</strong> Bot will simulate trades without spending real SOL
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${botStatus.isRunning ? 'bg-green-500' : 'bg-red-500'}`} />
                <span className="text-sm text-muted-foreground">
                  {botStatus.isRunning ? 'Running' : 'Stopped'}
                </span>
              </div>
              <Button
                onClick={toggleBot}
                variant={botStatus.isRunning ? "destructive" : "default"}
                className="flex items-center gap-2"
              >
                <Bot className="w-4 h-4" />
                {botStatus.isRunning ? 'Stop Bot' : 'Start Bot'}
              </Button>
            </div>
          </div>

          {/* Settings Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="w-5 h-5" />
                Sniper Configuration
              </CardTitle>
              <CardDescription>
                Configure when and how your bot should snipe new tokens
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Bot Active */}
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base font-medium">Enable Auto-Sniping</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically buy tokens that meet your criteria
                  </p>
                </div>
                <Switch
                  checked={settings.is_active}
                  onCheckedChange={(checked) => updateSetting('is_active', checked)}
                />
              </div>

              {/* Buy Amount */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Max Buy Amount (SOL)
                </Label>
                <Input
                  type="number"
                  step="0.01"
                  min="0.01"
                  max="10"
                  value={settings.max_buy_amount_sol}
                  onChange={(e) => updateSetting('max_buy_amount_sol', parseFloat(e.target.value))}
                  placeholder="0.1"
                />
                <p className="text-sm text-muted-foreground">
                  Maximum SOL to spend per token purchase
                </p>
              </div>

              {/* Min Liquidity */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Minimum Liquidity (SOL)
                </Label>
                <Input
                  type="number"
                  step="0.1"
                  min="1"
                  value={settings.min_liquidity_sol}
                  onChange={(e) => updateSetting('min_liquidity_sol', parseFloat(e.target.value))}
                  placeholder="5.0"
                />
                <p className="text-sm text-muted-foreground">
                  Minimum SOL liquidity required in the pool
                </p>
              </div>

              {/* Profit Target */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Percent className="w-4 h-4" />
                  Take Profit (%)
                </Label>
                <Input
                  type="number"
                  min="10"
                  max="1000"
                  value={settings.target_profit_percent}
                  onChange={(e) => updateSetting('target_profit_percent', parseInt(e.target.value))}
                  placeholder="100"
                />
                <p className="text-sm text-muted-foreground">
                  Sell when profit reaches this percentage
                </p>
              </div>

              {/* Stop Loss */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Percent className="w-4 h-4" />
                  Stop Loss (%)
                </Label>
                <Input
                  type="number"
                  min="10"
                  max="90"
                  value={settings.stop_loss_percent}
                  onChange={(e) => updateSetting('stop_loss_percent', parseInt(e.target.value))}
                  placeholder="50"
                />
                <p className="text-sm text-muted-foreground">
                  Sell when loss reaches this percentage
                </p>
              </div>

              {/* Min Holders */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Minimum Holders
                </Label>
                <Input
                  type="number"
                  min="1"
                  value={settings.min_holders}
                  onChange={(e) => updateSetting('min_holders', parseInt(e.target.value))}
                  placeholder="10"
                />
                <p className="text-sm text-muted-foreground">
                  Minimum number of token holders required
                </p>
              </div>

              {/* Safety Checks */}
              <div className="space-y-4">
                <Label className="flex items-center gap-2 text-base font-medium">
                  <Shield className="w-4 h-4" />
                  Safety Checks
                </Label>
                
                <div className="space-y-3 pl-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Check for Honeypots</Label>
                      <p className="text-sm text-muted-foreground">
                        Skip tokens that can't be sold
                      </p>
                    </div>
                    <Switch
                      checked={settings.check_honeypot}
                      onCheckedChange={(checked) => updateSetting('check_honeypot', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Check Mint Authority</Label>
                      <p className="text-sm text-muted-foreground">
                        Skip tokens with mint authority
                      </p>
                    </div>
                    <Switch
                      checked={settings.check_mint_authority}
                      onCheckedChange={(checked) => updateSetting('check_mint_authority', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Check Freeze Authority</Label>
                      <p className="text-sm text-muted-foreground">
                        Skip tokens with freeze authority
                      </p>
                    </div>
                    <Switch
                      checked={settings.check_freeze_authority}
                      onCheckedChange={(checked) => updateSetting('check_freeze_authority', checked)}
                    />
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="pt-6">
                <Button 
                  onClick={saveSettings} 
                  disabled={loading}
                  className="w-full flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? 'Saving...' : 'Save Settings'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SimpleSniperSettings;
