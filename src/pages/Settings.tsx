import { Navigation } from "@/components/Navigation";
import { useAuth } from "@/hooks/useAuth";
import { useTrading } from "@/hooks/useTrading";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  Settings as SettingsIcon,
  Bot,
  Shield,
  Key,
  Bell,
  Palette,
  Database,
  Zap,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Save,
  Download,
  Upload,
  Trash2,
  Eye,
  EyeOff
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { config } from "@/config";

const Settings = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const {
    tradingSettings,
    updateTradingSettings,
    isAutoTradingActive,
    startAutoTrading,
    stopAutoTrading,
  } = useTrading();

  // Settings state
  const [botSettings, setBotSettings] = useState({
    autoTradingEnabled: false,
    maxPositionSizeSol: 0.1,
    minSafetyScore: 70,
    maxSlippagePercent: 3,
    stopLossPercent: 20,
    takeProfitPercent: 100,
    priorityFeeLamports: 5000,
    maxPositions: 5,
    minLiquiditySol: 1,
    maxSupply: 1000000000,
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    sessionTimeout: 30,
    autoLockWallets: true,
    requirePasswordForTrades: true,
    enableAuditLog: true,
  });

  const [notificationSettings, setNotificationSettings] = useState({
    tradeNotifications: true,
    tokenDetectionAlerts: true,
    priceAlerts: true,
    securityAlerts: true,
    emailNotifications: false,
    pushNotifications: true,
  });

  const [apiSettings, setApiSettings] = useState({
    rpcEndpoint: config.solana.rpcUrl,
    jupiterApiUrl: config.jupiter.apiUrl,
    dexscreenerApiKey: '',
    coingeckoApiKey: '',
    birdeyeApiKey: '',
    heliusApiKey: '',
    customRpcUrl: '',
  });

  const [uiSettings, setUiSettings] = useState({
    theme: 'dark',
    language: 'en',
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    compactMode: false,
    showAdvancedFeatures: true,
  });

  const [showApiKeys, setShowApiKeys] = useState(false);

  useEffect(() => {
    if (!authLoading && !user) {
      navigate("/auth");
    }
  }, [user, authLoading, navigate]);

  // Load settings when trading settings are available
  useEffect(() => {
    if (tradingSettings) {
      setBotSettings(prev => ({
        ...prev,
        autoTradingEnabled: tradingSettings.auto_trading_enabled,
        maxPositionSizeSol: tradingSettings.max_position_size_sol,
        minSafetyScore: tradingSettings.min_safety_score,
        maxSlippagePercent: tradingSettings.max_slippage_percent,
        stopLossPercent: tradingSettings.stop_loss_percent,
        takeProfitPercent: tradingSettings.take_profit_percent,
        priorityFeeLamports: tradingSettings.priority_fee_lamports,
      }));
    }
  }, [tradingSettings]);

  if (authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground flex items-center gap-2">
          <RefreshCw className="w-4 h-4 animate-spin" />
          Loading settings...
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Handler functions
  const handleSaveBotSettings = async () => {
    try {
      await updateTradingSettings({
        auto_trading_enabled: botSettings.autoTradingEnabled,
        max_position_size_sol: botSettings.maxPositionSizeSol,
        min_safety_score: botSettings.minSafetyScore,
        max_slippage_percent: botSettings.maxSlippagePercent,
        stop_loss_percent: botSettings.stopLossPercent,
        take_profit_percent: botSettings.takeProfitPercent,
        priority_fee_lamports: botSettings.priorityFeeLamports,
      });
      toast.success('Bot settings saved successfully');
    } catch (error) {
      toast.error('Failed to save bot settings');
    }
  };

  const handleExportSettings = () => {
    const settings = {
      botSettings,
      securitySettings,
      notificationSettings,
      uiSettings,
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `swift-sniper-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Settings exported successfully');
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const settings = JSON.parse(e.target?.result as string);
        if (settings.botSettings) setBotSettings(settings.botSettings);
        if (settings.securitySettings) setSecuritySettings(settings.securitySettings);
        if (settings.notificationSettings) setNotificationSettings(settings.notificationSettings);
        if (settings.uiSettings) setUiSettings(settings.uiSettings);
        toast.success('Settings imported successfully');
      } catch (error) {
        toast.error('Failed to import settings - invalid file format');
      }
    };
    reader.readAsText(file);
  };

  const handleResetToDefaults = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      setBotSettings({
        autoTradingEnabled: false,
        maxPositionSizeSol: 0.1,
        minSafetyScore: 70,
        maxSlippagePercent: 3,
        stopLossPercent: 20,
        takeProfitPercent: 100,
        priorityFeeLamports: 5000,
        maxPositions: 5,
        minLiquiditySol: 1,
        maxSupply: 1000000000,
      });
      setSecuritySettings({
        twoFactorEnabled: false,
        sessionTimeout: 30,
        autoLockWallets: true,
        requirePasswordForTrades: true,
        enableAuditLog: true,
      });
      setNotificationSettings({
        tradeNotifications: true,
        tokenDetectionAlerts: true,
        priceAlerts: true,
        securityAlerts: true,
        emailNotifications: false,
        pushNotifications: true,
      });
      setUiSettings({
        theme: 'dark',
        language: 'en',
        currency: 'USD',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        compactMode: false,
        showAdvancedFeatures: true,
      });
      toast.success('Settings reset to defaults');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
                <SettingsIcon className="w-8 h-8 text-primary" />
                Settings
              </h1>
              <p className="text-muted-foreground mt-2">
                Configure your trading bot, security, and account preferences.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" onClick={handleExportSettings}>
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm" asChild>
                <label htmlFor="import-settings" className="cursor-pointer">
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </label>
              </Button>
              <input
                id="import-settings"
                type="file"
                accept=".json"
                className="hidden"
                onChange={handleImportSettings}
              />
              <Button variant="destructive" size="sm" onClick={handleResetToDefaults}>
                <Trash2 className="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>

          {/* Settings Tabs */}
          <Tabs defaultValue="bot" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="bot" className="flex items-center gap-2">
                <Bot className="w-4 h-4" />
                Bot
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="w-4 h-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="api" className="flex items-center gap-2">
                <Key className="w-4 h-4" />
                API
              </TabsTrigger>
              <TabsTrigger value="ui" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Interface
              </TabsTrigger>
            </TabsList>

            {/* Bot Settings Tab */}
            <TabsContent value="bot" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Trading Configuration */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5" />
                      Trading Configuration
                    </CardTitle>
                    <CardDescription>
                      Configure your bot's trading parameters and risk management
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Auto Trading</Label>
                        <p className="text-xs text-muted-foreground">Enable automated trading</p>
                      </div>
                      <Switch
                        checked={botSettings.autoTradingEnabled}
                        onCheckedChange={(checked) =>
                          setBotSettings(prev => ({ ...prev, autoTradingEnabled: checked }))
                        }
                      />
                    </div>

                    <Separator />

                    <div>
                      <Label htmlFor="max-position">Max Position Size (SOL)</Label>
                      <div className="flex items-center space-x-4 mt-2">
                        <Slider
                          value={[botSettings.maxPositionSizeSol]}
                          onValueChange={([value]) =>
                            setBotSettings(prev => ({ ...prev, maxPositionSizeSol: value }))
                          }
                          max={10}
                          min={0.01}
                          step={0.01}
                          className="flex-1"
                        />
                        <Input
                          id="max-position"
                          type="number"
                          value={botSettings.maxPositionSizeSol}
                          onChange={(e) =>
                            setBotSettings(prev => ({ ...prev, maxPositionSizeSol: parseFloat(e.target.value) || 0 }))
                          }
                          className="w-20"
                          step="0.01"
                          min="0.01"
                          max="10"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Maximum SOL amount to spend per trade
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="min-safety">Minimum Safety Score</Label>
                      <div className="flex items-center space-x-4 mt-2">
                        <Slider
                          value={[botSettings.minSafetyScore]}
                          onValueChange={([value]) =>
                            setBotSettings(prev => ({ ...prev, minSafetyScore: value }))
                          }
                          max={100}
                          min={0}
                          step={1}
                          className="flex-1"
                        />
                        <Input
                          id="min-safety"
                          type="number"
                          value={botSettings.minSafetyScore}
                          onChange={(e) =>
                            setBotSettings(prev => ({ ...prev, minSafetyScore: parseInt(e.target.value) || 0 }))
                          }
                          className="w-20"
                          min="0"
                          max="100"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Only trade tokens with safety score above this threshold
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="max-slippage">Max Slippage (%)</Label>
                      <div className="flex items-center space-x-4 mt-2">
                        <Slider
                          value={[botSettings.maxSlippagePercent]}
                          onValueChange={([value]) =>
                            setBotSettings(prev => ({ ...prev, maxSlippagePercent: value }))
                          }
                          max={50}
                          min={0.1}
                          step={0.1}
                          className="flex-1"
                        />
                        <Input
                          id="max-slippage"
                          type="number"
                          value={botSettings.maxSlippagePercent}
                          onChange={(e) =>
                            setBotSettings(prev => ({ ...prev, maxSlippagePercent: parseFloat(e.target.value) || 0 }))
                          }
                          className="w-20"
                          step="0.1"
                          min="0.1"
                          max="50"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Maximum acceptable slippage for trades
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* Risk Management */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Risk Management
                    </CardTitle>
                    <CardDescription>
                      Configure stop-loss, take-profit, and position limits
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="stop-loss">Stop Loss (%)</Label>
                      <div className="flex items-center space-x-4 mt-2">
                        <Slider
                          value={[botSettings.stopLossPercent]}
                          onValueChange={([value]) =>
                            setBotSettings(prev => ({ ...prev, stopLossPercent: value }))
                          }
                          max={90}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <Input
                          id="stop-loss"
                          type="number"
                          value={botSettings.stopLossPercent}
                          onChange={(e) =>
                            setBotSettings(prev => ({ ...prev, stopLossPercent: parseFloat(e.target.value) || 0 }))
                          }
                          className="w-20"
                          min="1"
                          max="90"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Automatically sell if price drops by this percentage
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="take-profit">Take Profit (%)</Label>
                      <div className="flex items-center space-x-4 mt-2">
                        <Slider
                          value={[botSettings.takeProfitPercent]}
                          onValueChange={([value]) =>
                            setBotSettings(prev => ({ ...prev, takeProfitPercent: value }))
                          }
                          max={1000}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <Input
                          id="take-profit"
                          type="number"
                          value={botSettings.takeProfitPercent}
                          onChange={(e) =>
                            setBotSettings(prev => ({ ...prev, takeProfitPercent: parseFloat(e.target.value) || 0 }))
                          }
                          className="w-20"
                          min="1"
                          max="1000"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Automatically sell if price increases by this percentage
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="max-positions">Max Positions</Label>
                      <Input
                        id="max-positions"
                        type="number"
                        value={botSettings.maxPositions}
                        onChange={(e) =>
                          setBotSettings(prev => ({ ...prev, maxPositions: parseInt(e.target.value) || 0 }))
                        }
                        min="1"
                        max="20"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Maximum number of simultaneous positions
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="priority-fee">Priority Fee (Lamports)</Label>
                      <Select
                        value={botSettings.priorityFeeLamports.toString()}
                        onValueChange={(value) =>
                          setBotSettings(prev => ({ ...prev, priorityFeeLamports: parseInt(value) }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1000">Low (1,000)</SelectItem>
                          <SelectItem value="5000">Medium (5,000)</SelectItem>
                          <SelectItem value="10000">High (10,000)</SelectItem>
                          <SelectItem value="50000">Ultra (50,000)</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground mt-1">
                        Higher fees increase transaction priority
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Token Filters */}
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="w-5 h-5" />
                    Token Filters
                  </CardTitle>
                  <CardDescription>
                    Configure which tokens the bot should consider for trading
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="min-liquidity">Min Liquidity (SOL)</Label>
                      <Input
                        id="min-liquidity"
                        type="number"
                        value={botSettings.minLiquiditySol}
                        onChange={(e) =>
                          setBotSettings(prev => ({ ...prev, minLiquiditySol: parseFloat(e.target.value) || 0 }))
                        }
                        step="0.1"
                        min="0.1"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Minimum liquidity required in SOL
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="max-supply">Max Supply</Label>
                      <Input
                        id="max-supply"
                        type="number"
                        value={botSettings.maxSupply}
                        onChange={(e) =>
                          setBotSettings(prev => ({ ...prev, maxSupply: parseInt(e.target.value) || 0 }))
                        }
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Maximum token supply to consider
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button onClick={handleSaveBotSettings} className="flex items-center gap-2">
                  <Save className="w-4 h-4" />
                  Save Bot Settings
                </Button>
              </div>
            </TabsContent>

            {/* Security Settings Tab */}
            <TabsContent value="security" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Authentication */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Authentication
                    </CardTitle>
                    <CardDescription>
                      Manage your account security and authentication settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Two-Factor Authentication</Label>
                        <p className="text-xs text-muted-foreground">Add an extra layer of security</p>
                      </div>
                      <Switch
                        checked={securitySettings.twoFactorEnabled}
                        onCheckedChange={(checked) =>
                          setSecuritySettings(prev => ({ ...prev, twoFactorEnabled: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Auto-Lock Wallets</Label>
                        <p className="text-xs text-muted-foreground">Automatically lock wallets after inactivity</p>
                      </div>
                      <Switch
                        checked={securitySettings.autoLockWallets}
                        onCheckedChange={(checked) =>
                          setSecuritySettings(prev => ({ ...prev, autoLockWallets: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Require Password for Trades</Label>
                        <p className="text-xs text-muted-foreground">Require password confirmation for trades</p>
                      </div>
                      <Switch
                        checked={securitySettings.requirePasswordForTrades}
                        onCheckedChange={(checked) =>
                          setSecuritySettings(prev => ({ ...prev, requirePasswordForTrades: checked }))
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                      <Select
                        value={securitySettings.sessionTimeout.toString()}
                        onValueChange={(value) =>
                          setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(value) }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="15">15 minutes</SelectItem>
                          <SelectItem value="30">30 minutes</SelectItem>
                          <SelectItem value="60">1 hour</SelectItem>
                          <SelectItem value="120">2 hours</SelectItem>
                          <SelectItem value="480">8 hours</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground mt-1">
                        Automatically log out after this period of inactivity
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* Audit & Logging */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="w-5 h-5" />
                      Audit & Logging
                    </CardTitle>
                    <CardDescription>
                      Configure security logging and audit trail settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Enable Audit Log</Label>
                        <p className="text-xs text-muted-foreground">Log all trading and security events</p>
                      </div>
                      <Switch
                        checked={securitySettings.enableAuditLog}
                        onCheckedChange={(checked) =>
                          setSecuritySettings(prev => ({ ...prev, enableAuditLog: checked }))
                        }
                      />
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Security Status</Label>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 rounded-lg bg-background/50">
                          <span className="text-sm">Password Strength</span>
                          <Badge variant="default">Strong</Badge>
                        </div>
                        <div className="flex items-center justify-between p-2 rounded-lg bg-background/50">
                          <span className="text-sm">Last Login</span>
                          <span className="text-sm text-muted-foreground">
                            {new Date().toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center justify-between p-2 rounded-lg bg-background/50">
                          <span className="text-sm">Active Sessions</span>
                          <Badge variant="secondary">1</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Security Actions */}
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    Security Actions
                  </CardTitle>
                  <CardDescription>
                    Manage your account security and emergency actions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button variant="outline" className="flex items-center gap-2">
                      <Key className="w-4 h-4" />
                      Change Password
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2">
                      <Download className="w-4 h-4" />
                      Download Backup
                    </Button>
                    <Button variant="destructive" className="flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4" />
                      Emergency Stop
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Notifications Settings Tab */}
            <TabsContent value="notifications" className="space-y-6">
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="w-5 h-5" />
                    Notification Preferences
                  </CardTitle>
                  <CardDescription>
                    Configure when and how you receive notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">Trading Notifications</h4>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Trade Executions</Label>
                          <p className="text-xs text-muted-foreground">Notify when trades are executed</p>
                        </div>
                        <Switch
                          checked={notificationSettings.tradeNotifications}
                          onCheckedChange={(checked) =>
                            setNotificationSettings(prev => ({ ...prev, tradeNotifications: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Token Detection</Label>
                          <p className="text-xs text-muted-foreground">Notify when new tokens are detected</p>
                        </div>
                        <Switch
                          checked={notificationSettings.tokenDetectionAlerts}
                          onCheckedChange={(checked) =>
                            setNotificationSettings(prev => ({ ...prev, tokenDetectionAlerts: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Price Alerts</Label>
                          <p className="text-xs text-muted-foreground">Notify on significant price changes</p>
                        </div>
                        <Switch
                          checked={notificationSettings.priceAlerts}
                          onCheckedChange={(checked) =>
                            setNotificationSettings(prev => ({ ...prev, priceAlerts: checked }))
                          }
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">System Notifications</h4>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Security Alerts</Label>
                          <p className="text-xs text-muted-foreground">Notify on security events</p>
                        </div>
                        <Switch
                          checked={notificationSettings.securityAlerts}
                          onCheckedChange={(checked) =>
                            setNotificationSettings(prev => ({ ...prev, securityAlerts: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Email Notifications</Label>
                          <p className="text-xs text-muted-foreground">Send notifications via email</p>
                        </div>
                        <Switch
                          checked={notificationSettings.emailNotifications}
                          onCheckedChange={(checked) =>
                            setNotificationSettings(prev => ({ ...prev, emailNotifications: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Push Notifications</Label>
                          <p className="text-xs text-muted-foreground">Send browser push notifications</p>
                        </div>
                        <Switch
                          checked={notificationSettings.pushNotifications}
                          onCheckedChange={(checked) =>
                            setNotificationSettings(prev => ({ ...prev, pushNotifications: checked }))
                          }
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* API Settings Tab */}
            <TabsContent value="api" className="space-y-6">
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="w-5 h-5" />
                    API Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure API endpoints and keys for external services
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between mb-4">
                    <Label className="text-sm font-medium">Show API Keys</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowApiKeys(!showApiKeys)}
                    >
                      {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="rpc-endpoint">Solana RPC Endpoint</Label>
                      <Select
                        value={apiSettings.rpcEndpoint}
                        onValueChange={(value) =>
                          setApiSettings(prev => ({ ...prev, rpcEndpoint: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="https://api.mainnet-beta.solana.com">Mainnet (Public)</SelectItem>
                          <SelectItem value="https://api.devnet.solana.com">Devnet (Public)</SelectItem>
                          <SelectItem value="custom">Custom RPC</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {apiSettings.rpcEndpoint === 'custom' && (
                      <div>
                        <Label htmlFor="custom-rpc">Custom RPC URL</Label>
                        <Input
                          id="custom-rpc"
                          type="url"
                          value={apiSettings.customRpcUrl}
                          onChange={(e) =>
                            setApiSettings(prev => ({ ...prev, customRpcUrl: e.target.value }))
                          }
                          placeholder="https://your-rpc-endpoint.com"
                        />
                      </div>
                    )}

                    <div>
                      <Label htmlFor="helius-key">Helius API Key</Label>
                      <Input
                        id="helius-key"
                        type={showApiKeys ? "text" : "password"}
                        value={apiSettings.heliusApiKey}
                        onChange={(e) =>
                          setApiSettings(prev => ({ ...prev, heliusApiKey: e.target.value }))
                        }
                        placeholder="Enter Helius API key"
                      />
                    </div>

                    <div>
                      <Label htmlFor="coingecko-key">CoinGecko API Key</Label>
                      <Input
                        id="coingecko-key"
                        type={showApiKeys ? "text" : "password"}
                        value={apiSettings.coingeckoApiKey}
                        onChange={(e) =>
                          setApiSettings(prev => ({ ...prev, coingeckoApiKey: e.target.value }))
                        }
                        placeholder="Enter CoinGecko API key"
                      />
                    </div>

                    <div>
                      <Label htmlFor="birdeye-key">Birdeye API Key</Label>
                      <Input
                        id="birdeye-key"
                        type={showApiKeys ? "text" : "password"}
                        value={apiSettings.birdeyeApiKey}
                        onChange={(e) =>
                          setApiSettings(prev => ({ ...prev, birdeyeApiKey: e.target.value }))
                        }
                        placeholder="Enter Birdeye API key"
                      />
                    </div>

                    <div>
                      <Label htmlFor="dexscreener-key">DexScreener API Key</Label>
                      <Input
                        id="dexscreener-key"
                        type={showApiKeys ? "text" : "password"}
                        value={apiSettings.dexscreenerApiKey}
                        onChange={(e) =>
                          setApiSettings(prev => ({ ...prev, dexscreenerApiKey: e.target.value }))
                        }
                        placeholder="Enter DexScreener API key"
                      />
                    </div>
                  </div>

                  <div className="pt-4">
                    <Button className="flex items-center gap-2">
                      <Save className="w-4 h-4" />
                      Save API Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* UI Settings Tab */}
            <TabsContent value="ui" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Appearance */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      Appearance
                    </CardTitle>
                    <CardDescription>
                      Customize the look and feel of the interface
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="theme">Theme</Label>
                      <Select
                        value={uiSettings.theme}
                        onValueChange={(value) =>
                          setUiSettings(prev => ({ ...prev, theme: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="language">Language</Label>
                      <Select
                        value={uiSettings.language}
                        onValueChange={(value) =>
                          setUiSettings(prev => ({ ...prev, language: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="es">Español</SelectItem>
                          <SelectItem value="fr">Français</SelectItem>
                          <SelectItem value="de">Deutsch</SelectItem>
                          <SelectItem value="zh">中文</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="currency">Display Currency</Label>
                      <Select
                        value={uiSettings.currency}
                        onValueChange={(value) =>
                          setUiSettings(prev => ({ ...prev, currency: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                          <SelectItem value="JPY">JPY (¥)</SelectItem>
                          <SelectItem value="SOL">SOL (◎)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Compact Mode</Label>
                        <p className="text-xs text-muted-foreground">Use smaller UI elements</p>
                      </div>
                      <Switch
                        checked={uiSettings.compactMode}
                        onCheckedChange={(checked) =>
                          setUiSettings(prev => ({ ...prev, compactMode: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Advanced Features</Label>
                        <p className="text-xs text-muted-foreground">Show advanced trading options</p>
                      </div>
                      <Switch
                        checked={uiSettings.showAdvancedFeatures}
                        onCheckedChange={(checked) =>
                          setUiSettings(prev => ({ ...prev, showAdvancedFeatures: checked }))
                        }
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Format Settings */}
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <SettingsIcon className="w-5 h-5" />
                      Format Settings
                    </CardTitle>
                    <CardDescription>
                      Configure date, time, and number formats
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="date-format">Date Format</Label>
                      <Select
                        value={uiSettings.dateFormat}
                        onValueChange={(value) =>
                          setUiSettings(prev => ({ ...prev, dateFormat: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                          <SelectItem value="DD MMM YYYY">DD MMM YYYY</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="time-format">Time Format</Label>
                      <Select
                        value={uiSettings.timeFormat}
                        onValueChange={(value) =>
                          setUiSettings(prev => ({ ...prev, timeFormat: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="12h">12 Hour (AM/PM)</SelectItem>
                          <SelectItem value="24h">24 Hour</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Preview</Label>
                      <div className="p-3 rounded-lg bg-background/50 space-y-1">
                        <div className="text-sm">
                          Date: {new Date().toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: uiSettings.dateFormat.includes('MMM') ? 'short' : '2-digit',
                            day: '2-digit'
                          })}
                        </div>
                        <div className="text-sm">
                          Time: {new Date().toLocaleTimeString('en-US', {
                            hour12: uiSettings.timeFormat === '12h'
                          })}
                        </div>
                        <div className="text-sm">
                          Currency: {uiSettings.currency === 'SOL' ? '◎' : '$'}123.45
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button className="flex items-center gap-2">
                  <Save className="w-4 h-4" />
                  Save UI Settings
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Settings;