import { Navigation } from "@/components/Navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Zap,
  Shield,
  Cpu,
  TrendingUp,
  Users,
  Award,
  Info,
  Target,
  BookOpen,
  ExternalLink,
  Github,
  Twitter,
  MessageCircle,
  Star,
  Wallet,
  Bot,
  AlertTriangle,
  CheckCircle,
  Code,
  Globe,
  HelpCircle,
  FileText,
  Video,
  Mail
} from "lucide-react";

const About = () => {
  const features = [
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Lightning Fast Trading",
      description: "Execute trades in milliseconds with our optimized Jupiter integration and priority fee management."
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Advanced Safety Analysis",
      description: "Comprehensive honeypot detection, rug pull analysis, and multi-factor safety scoring."
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Smart Token Detection",
      description: "Real-time monitoring of new token launches with intelligent filtering and analysis."
    },
    {
      icon: <Wallet className="w-6 h-6" />,
      title: "Secure Wallet Management",
      description: "Military-grade encryption for private keys with multi-wallet support and auto-locking."
    },
    {
      icon: <Bot className="w-6 h-6" />,
      title: "Automated Trading Bot",
      description: "Intelligent bot with risk management, position sizing, and profit/loss automation."
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Real-time Analytics",
      description: "Comprehensive portfolio tracking, P&L calculations, and performance metrics."
    }
  ];

  const stats = [
    { label: "Total Trades Executed", value: "10,000+", icon: <TrendingUp className="w-5 h-5" /> },
    { label: "Tokens Analyzed", value: "50,000+", icon: <Target className="w-5 h-5" /> },
    { label: "Active Users", value: "1,000+", icon: <Users className="w-5 h-5" /> },
    { label: "Success Rate", value: "85%", icon: <CheckCircle className="w-5 h-5" /> }
  ];

  const roadmapItems = [
    {
      phase: "Phase 1",
      status: "completed",
      title: "Core Platform",
      items: ["Wallet Management", "Jupiter Integration", "Token Detection", "Safety Analysis", "Basic Trading Bot"]
    },
    {
      phase: "Phase 2",
      status: "in-progress",
      title: "Advanced Features",
      items: ["Advanced Charting", "Copy Trading", "Portfolio Optimization", "Mobile App", "Enhanced Analytics"]
    },
    {
      phase: "Phase 3",
      status: "planned",
      title: "Ecosystem Expansion",
      items: ["Multi-chain Support", "Social Trading", "AI-powered Analysis", "Institutional Features", "API Access"]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-foreground mb-4 flex items-center justify-center gap-3">
              <Info className="w-10 h-10 text-primary" />
              About Swift Sniper Fi
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              The most advanced Solana trading bot with intelligent token detection,
              comprehensive safety analysis, and automated trading capabilities.
            </p>
          </div>

          {/* Main Content Tabs */}
          <Tabs defaultValue="overview" className="space-y-8">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="guide">User Guide</TabsTrigger>
              <TabsTrigger value="roadmap">Roadmap</TabsTrigger>
              <TabsTrigger value="support">Support</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-8">
              {/* Platform Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {stats.map((stat, index) => (
                  <Card key={index} className="border-border bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6 text-center">
                      <div className="flex items-center justify-center mb-2 text-primary">
                        {stat.icon}
                      </div>
                      <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                      <div className="text-sm text-muted-foreground">{stat.label}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Mission Statement */}
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Our Mission
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    Swift Sniper Fi was created to democratize access to advanced trading tools on Solana.
                    We believe that everyone should have access to institutional-grade trading technology,
                    comprehensive safety analysis, and automated trading capabilities. Our platform combines
                    cutting-edge blockchain technology with user-friendly interfaces to make sophisticated
                    trading strategies accessible to traders of all levels.
                  </p>
                </CardContent>
              </Card>

              {/* Technology Stack */}
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="w-5 h-5" />
                    Technology Stack
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <Badge variant="secondary" className="mb-2">Frontend</Badge>
                      <div className="text-sm text-muted-foreground">React, TypeScript, Tailwind CSS</div>
                    </div>
                    <div className="text-center">
                      <Badge variant="secondary" className="mb-2">Backend</Badge>
                      <div className="text-sm text-muted-foreground">Supabase, PostgreSQL, Edge Functions</div>
                    </div>
                    <div className="text-center">
                      <Badge variant="secondary" className="mb-2">Blockchain</Badge>
                      <div className="text-sm text-muted-foreground">Solana Web3.js, Jupiter API</div>
                    </div>
                    <div className="text-center">
                      <Badge variant="secondary" className="mb-2">Security</Badge>
                      <div className="text-sm text-muted-foreground">AES-256 Encryption, RLS</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Features Tab */}
            <TabsContent value="features" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {features.map((feature, index) => (
                  <Card key={index} className="border-border bg-card/50 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 rounded-lg bg-primary/10 text-primary">
                          {feature.icon}
                        </div>
                        <CardTitle className="text-lg">{feature.title}</CardTitle>
                      </div>
                      <CardDescription className="text-sm leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </CardHeader>
                  </Card>
                ))}
              </div>

              {/* Detailed Feature Breakdown */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Safety & Security
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Honeypot detection with 95% accuracy</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Rug pull risk analysis</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Liquidity pool verification</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Holder distribution analysis</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Authority verification</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bot className="w-5 h-5" />
                      Trading Automation
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Automated buy/sell execution</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Stop-loss and take-profit</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Position size management</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Multi-position tracking</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Real-time P&L monitoring</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* User Guide Tab */}
            <TabsContent value="guide" className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5" />
                      Getting Started
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-bold">1</div>
                        <div>
                          <h4 className="font-medium">Create Account</h4>
                          <p className="text-sm text-muted-foreground">Sign up with email and verify your account</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-bold">2</div>
                        <div>
                          <h4 className="font-medium">Set Up Wallet</h4>
                          <p className="text-sm text-muted-foreground">Create or import your Solana wallet securely</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-bold">3</div>
                        <div>
                          <h4 className="font-medium">Configure Bot</h4>
                          <p className="text-sm text-muted-foreground">Set trading parameters and risk management</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-bold">4</div>
                        <div>
                          <h4 className="font-medium">Start Trading</h4>
                          <p className="text-sm text-muted-foreground">Enable the bot and monitor performance</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <HelpCircle className="w-5 h-5" />
                      Best Practices
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <div>
                          <h4 className="font-medium">Start Small</h4>
                          <p className="text-sm text-muted-foreground">Begin with small position sizes to test strategies</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <div>
                          <h4 className="font-medium">Use Safety Filters</h4>
                          <p className="text-sm text-muted-foreground">Set high safety score thresholds initially</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <div>
                          <h4 className="font-medium">Monitor Regularly</h4>
                          <p className="text-sm text-muted-foreground">Check bot performance and adjust settings</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <div>
                          <h4 className="font-medium">Diversify Risk</h4>
                          <p className="text-sm text-muted-foreground">Don't put all funds in automated trading</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Risk Warning */}
              <Card className="border-yellow-500/50 bg-yellow-500/10">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-yellow-600">
                    <AlertTriangle className="w-5 h-5" />
                    Important Risk Disclaimer
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Trading cryptocurrencies involves substantial risk of loss and is not suitable for all investors.
                    Past performance does not guarantee future results. Never trade with funds you cannot afford to lose.
                    This software is provided for educational purposes and should not be considered financial advice.
                    Always do your own research and consider consulting with a financial advisor.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Roadmap Tab */}
            <TabsContent value="roadmap" className="space-y-8">
              <div className="space-y-6">
                {roadmapItems.map((item, index) => (
                  <Card key={index} className="border-border bg-card/50 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${
                            item.status === 'completed' ? 'bg-green-500 text-white' :
                            item.status === 'in-progress' ? 'bg-blue-500 text-white' :
                            'bg-gray-500 text-white'
                          }`}>
                            {index + 1}
                          </div>
                          {item.title}
                        </CardTitle>
                        <Badge variant={
                          item.status === 'completed' ? 'default' :
                          item.status === 'in-progress' ? 'secondary' :
                          'outline'
                        }>
                          {item.status === 'completed' ? 'Completed' :
                           item.status === 'in-progress' ? 'In Progress' :
                           'Planned'}
                        </Badge>
                      </div>
                      <CardDescription>{item.phase}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {item.items.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center gap-2">
                            {item.status === 'completed' ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : item.status === 'in-progress' ? (
                              <div className="w-4 h-4 rounded-full border-2 border-blue-500 border-t-transparent animate-spin" />
                            ) : (
                              <div className="w-4 h-4 rounded-full border-2 border-gray-400" />
                            )}
                            <span className="text-sm">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Support Tab */}
            <TabsContent value="support" className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageCircle className="w-5 h-5" />
                      Community & Support
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <MessageCircle className="w-4 h-4" />
                          Join Discord Community
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <Twitter className="w-4 h-4" />
                          Follow on Twitter
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <Github className="w-4 h-4" />
                          View on GitHub
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <Mail className="w-4 h-4" />
                          Email Support
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Documentation & Resources
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <BookOpen className="w-4 h-4" />
                          User Documentation
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <Code className="w-4 h-4" />
                          API Documentation
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <Video className="w-4 h-4" />
                          Video Tutorials
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full justify-start" asChild>
                        <a href="#" className="flex items-center gap-3">
                          <HelpCircle className="w-4 h-4" />
                          FAQ
                          <ExternalLink className="w-3 h-3 ml-auto" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Contact Information */}
              <Card className="border-border bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-center">Need Help?</CardTitle>
                  <CardDescription className="text-center">
                    Our team is here to help you succeed with Swift Sniper Fi
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    For technical support, feature requests, or general questions,
                    reach out through any of our community channels above.
                  </p>
                  <div className="flex items-center justify-center gap-4">
                    <Badge variant="outline">Response time: &lt; 24h</Badge>
                    <Badge variant="outline">Community support</Badge>
                    <Badge variant="outline">Regular updates</Badge>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default About;