import { Navigation } from "@/components/Navigation";
import { useAuth } from "@/hooks/useAuth";
import { useWallet } from "@/hooks/useWallet";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Wallet,
  Plus,
  DollarSign,
  TrendingUp,
  Copy,
  Eye,
  EyeOff,
  Send,
  Download,
  Trash2,
  Star,
  StarOff,
  RefreshCw,
  Lock,
  Unlock,
  Zap
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { formatPrice } from "@/lib/price/feeds";

const WalletPage = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const {
    wallets,
    primaryWallet,
    balances,
    isLoading,
    createWallet,
    importWallet,
    deleteWallet,
    setPrimaryWallet,
    updateWalletName,
    refreshBalances,
    getWalletBalance,
    isWalletUnlocked,
    lockWallet,
    unlockWallet,
    exportPrivateKey,
  } = useWallet();

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [unlockDialogOpen, setUnlockDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [selectedWalletId, setSelectedWalletId] = useState<string>('');

  // Form states
  const [walletName, setWalletName] = useState('');
  const [walletPassword, setWalletPassword] = useState('');
  const [privateKey, setPrivateKey] = useState('');
  const [unlockPassword, setUnlockPassword] = useState('');
  const [exportedKey, setExportedKey] = useState('');
  const [showExportedKey, setShowExportedKey] = useState(false);

  useEffect(() => {
    if (!authLoading && !user) {
      navigate("/auth");
    }
  }, [user, authLoading, navigate]);

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground flex items-center gap-2">
          <RefreshCw className="w-4 h-4 animate-spin" />
          Loading wallets...
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Calculate total portfolio value
  const totalValue = balances.reduce((sum, balance) => sum + balance.totalValue, 0);
  const totalSolBalance = balances.reduce((sum, balance) => sum + balance.solBalance, 0);

  // Helper functions
  const copyToClipboard = (text: string | undefined) => {
    if (!text) {
      toast.error('Nothing to copy');
      return;
    }
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const formatAddress = (address: string | undefined) => {
    if (!address) return 'N/A';
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // Event handlers
  const handleCreateWallet = async () => {
    if (!walletName || !walletPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    try {
      await createWallet({
        name: walletName,
        password: walletPassword,
        isPrimary: wallets.length === 0, // First wallet is primary
      });
      setCreateDialogOpen(false);
      setWalletName('');
      setWalletPassword('');
    } catch (error) {
      console.error('Failed to create wallet:', error);
    }
  };

  const handleImportWallet = async () => {
    if (!walletName || !walletPassword || !privateKey) {
      toast.error('Please fill in all fields');
      return;
    }

    try {
      await importWallet({
        name: walletName,
        password: walletPassword,
        privateKey: privateKey.trim(),
        isPrimary: wallets.length === 0,
      });
      setImportDialogOpen(false);
      setWalletName('');
      setWalletPassword('');
      setPrivateKey('');
    } catch (error) {
      console.error('Failed to import wallet:', error);
    }
  };

  const handleUnlockWallet = async () => {
    if (!unlockPassword) {
      toast.error('Please enter password');
      return;
    }

    const success = await unlockWallet(selectedWalletId, unlockPassword);
    if (success) {
      setUnlockDialogOpen(false);
      setUnlockPassword('');
      setSelectedWalletId('');
    }
  };

  const handleExportPrivateKey = async () => {
    if (!unlockPassword) {
      toast.error('Please enter password');
      return;
    }

    try {
      const key = await exportPrivateKey(selectedWalletId, unlockPassword);
      setExportedKey(key);
      setUnlockPassword('');
    } catch (error) {
      toast.error('Invalid password');
    }
  };

  const handleDeleteWallet = async (walletId: string) => {
    if (confirm('Are you sure you want to delete this wallet? This action cannot be undone.')) {
      await deleteWallet(walletId);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
                <Zap className="w-8 h-8 text-primary" />
                Sniper Wallets
              </h1>
              <p className="text-muted-foreground mt-2">
                Manage your sniper bot wallets and monitor trading performance.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshBalances()}
                className="flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Refresh
              </Button>
              <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="hero" className="flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Create Sniper Wallet
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Sniper Wallet</DialogTitle>
                    <DialogDescription>
                      Generate a new Solana wallet optimized for sniper bot trading.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="wallet-name">Wallet Name</Label>
                      <Input
                        id="wallet-name"
                        placeholder="My Trading Wallet"
                        value={walletName}
                        onChange={(e) => setWalletName(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="wallet-password">Password</Label>
                      <Input
                        id="wallet-password"
                        type="password"
                        placeholder="Enter a secure password"
                        value={walletPassword}
                        onChange={(e) => setWalletPassword(e.target.value)}
                      />
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => setCreateDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="flex-1"
                        onClick={handleCreateWallet}
                      >
                        Create Wallet
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Wallet Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {wallets.map((wallet) => {
              const balance = getWalletBalance(wallet.id);
              const isUnlocked = isWalletUnlocked(wallet.id);
              const isPrimary = wallet.is_primary;

              return (
                <Card key={wallet.id} className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-foreground flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {wallet.name}
                        {isPrimary && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                      </div>
                      <div className="flex items-center gap-2">
                        {isUnlocked ? (
                          <Unlock className="w-4 h-4 text-green-500" />
                        ) : (
                          <Lock className="w-4 h-4 text-muted-foreground" />
                        )}
                        <div className={`w-3 h-3 rounded-full ${isPrimary ? 'bg-green-500' : 'bg-muted-foreground'}`}></div>
                      </div>
                    </CardTitle>
                    <CardDescription className="text-muted-foreground">
                      {isPrimary ? 'Primary trading wallet' : 'Secondary wallet'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">SOL Balance</span>
                        <span className="text-foreground font-mono flex items-center gap-1">
                          <DollarSign className="w-4 h-4" />
                          {balance ? balance.solBalance.toFixed(4) : '0.0000'} SOL
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">USD Value</span>
                        <span className="text-foreground font-mono">
                          ${balance ? formatPrice(balance.totalValue) : '0.00'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Address</span>
                        <div className="flex items-center gap-2">
                          <span className="text-foreground font-mono text-sm">
                            {formatAddress(wallet.address)}
                          </span>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            onClick={() => copyToClipboard(wallet.address)}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Token balances */}
                      {balance && balance.tokens && balance.tokens.length > 0 && (
                        <div className="pt-2">
                          <Separator className="mb-2" />
                          <div className="text-xs text-muted-foreground mb-2">Token Holdings</div>
                          <div className="space-y-1 max-h-20 overflow-y-auto">
                            {balance.tokens.slice(0, 3).map((token, index) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <span className="text-muted-foreground">
                                  {token.symbol || formatAddress(token.mint)}
                                </span>
                                <span className="text-foreground font-mono">
                                  {token.uiAmount.toFixed(2)}
                                </span>
                              </div>
                            ))}
                            {balance.tokens.length > 3 && (
                              <div className="text-xs text-muted-foreground text-center">
                                +{balance.tokens.length - 3} more tokens
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      <div className="flex gap-2 pt-2">
                        {!isPrimary && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1"
                            onClick={() => setPrimaryWallet(wallet.id)}
                          >
                            <Star className="w-3 h-3 mr-1" />
                            Set Primary
                          </Button>
                        )}
                        {isUnlocked ? (
                          <Button
                            size="sm"
                            variant="secondary"
                            className="flex-1"
                            onClick={() => lockWallet(wallet.id)}
                          >
                            <Lock className="w-3 h-3 mr-1" />
                            Lock
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="secondary"
                            className="flex-1"
                            onClick={() => {
                              setSelectedWalletId(wallet.id);
                              setUnlockDialogOpen(true);
                            }}
                          >
                            <Unlock className="w-3 h-3 mr-1" />
                            Unlock
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedWalletId(wallet.id);
                            setExportDialogOpen(true);
                          }}
                        >
                          <Download className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteWallet(wallet.id)}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {/* Add Wallet Card */}
            <Card className="border-dashed border-2 border-border bg-card/30 hover:bg-card/50 transition-colors">
              <CardContent className="flex flex-col items-center justify-center h-full min-h-[300px] text-center">
                <Plus className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-foreground font-medium mb-2">Add Sniper Wallet</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  Create or import a wallet for sniper bot trading
                </p>
                <div className="flex gap-2">
                  <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        Create
                      </Button>
                    </DialogTrigger>
                  </Dialog>
                  <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        Import
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Import Existing Wallet</DialogTitle>
                        <DialogDescription>
                          Import a wallet using your private key.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="import-name">Wallet Name</Label>
                          <Input
                            id="import-name"
                            placeholder="Imported Wallet"
                            value={walletName}
                            onChange={(e) => setWalletName(e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="private-key">Private Key</Label>
                          <Input
                            id="private-key"
                            placeholder="Enter your private key (base58 or array format)"
                            value={privateKey}
                            onChange={(e) => setPrivateKey(e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="import-password">Password</Label>
                          <Input
                            id="import-password"
                            type="password"
                            placeholder="Enter a secure password"
                            value={walletPassword}
                            onChange={(e) => setWalletPassword(e.target.value)}
                          />
                        </div>
                        <div className="flex gap-2 pt-4">
                          <Button
                            variant="outline"
                            className="flex-1"
                            onClick={() => setImportDialogOpen(false)}
                          >
                            Cancel
                          </Button>
                          <Button
                            className="flex-1"
                            onClick={handleImportWallet}
                          >
                            Import Wallet
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <Card className="border-border bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">Total SOL Balance</p>
                    <p className="text-2xl font-bold text-foreground">
                      {totalSolBalance.toFixed(4)} SOL
                    </p>
                    <p className="text-sm text-muted-foreground">
                      ${formatPrice(totalValue)}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-border bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">Active Wallets</p>
                    <p className="text-2xl font-bold text-foreground">{wallets.length}</p>
                    <p className="text-sm text-muted-foreground">
                      {wallets.filter(w => isWalletUnlocked(w.id)).length} unlocked
                    </p>
                  </div>
                  <Wallet className="w-8 h-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-border bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-muted-foreground text-sm">Total Tokens</p>
                    <p className="text-2xl font-bold text-foreground">
                      {balances.reduce((sum, b) => sum + (b.tokens?.length || 0), 0)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Across all wallets
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-primary" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Unlock Wallet Dialog */}
          <Dialog open={unlockDialogOpen} onOpenChange={setUnlockDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Unlock Wallet</DialogTitle>
                <DialogDescription>
                  Enter your password to unlock this wallet for trading.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="unlock-password">Password</Label>
                  <Input
                    id="unlock-password"
                    type="password"
                    placeholder="Enter wallet password"
                    value={unlockPassword}
                    onChange={(e) => setUnlockPassword(e.target.value)}
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => {
                      setUnlockDialogOpen(false);
                      setUnlockPassword('');
                      setSelectedWalletId('');
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="flex-1"
                    onClick={handleUnlockWallet}
                  >
                    Unlock
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Export Private Key Dialog */}
          <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Export Private Key</DialogTitle>
                <DialogDescription>
                  Export your private key. Keep this secure and never share it.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                {!exportedKey ? (
                  <>
                    <div>
                      <Label htmlFor="export-password">Password</Label>
                      <Input
                        id="export-password"
                        type="password"
                        placeholder="Enter wallet password"
                        value={unlockPassword}
                        onChange={(e) => setUnlockPassword(e.target.value)}
                      />
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => {
                          setExportDialogOpen(false);
                          setUnlockPassword('');
                          setSelectedWalletId('');
                          setExportedKey('');
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="flex-1"
                        onClick={handleExportPrivateKey}
                      >
                        Export
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <Label>Private Key</Label>
                      <div className="relative">
                        <Input
                          value={showExportedKey ? exportedKey : '•'.repeat(exportedKey.length)}
                          readOnly
                          className="font-mono text-xs"
                        />
                        <Button
                          size="sm"
                          variant="ghost"
                          className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
                          onClick={() => setShowExportedKey(!showExportedKey)}
                        >
                          {showExportedKey ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                        </Button>
                      </div>
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => copyToClipboard(exportedKey)}
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        Copy
                      </Button>
                      <Button
                        className="flex-1"
                        onClick={() => {
                          setExportDialogOpen(false);
                          setUnlockPassword('');
                          setSelectedWalletId('');
                          setExportedKey('');
                          setShowExportedKey(false);
                        }}
                      >
                        Done
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default WalletPage;