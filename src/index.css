@tailwind base;
@tailwind components;
@tailwind utilities;

/* Solana Sniper Bot Design System - Crypto Trading Aesthetic */

@layer base {
  :root {
    /* Dark crypto theme */
    --background: 220 15% 6%;
    --foreground: 220 10% 95%;

    --card: 220 15% 8%;
    --card-foreground: 220 10% 95%;

    --popover: 220 15% 8%;
    --popover-foreground: 220 10% 95%;

    /* Electric blue primary for Solana theme */
    --primary: 258 100% 65%;
    --primary-foreground: 220 15% 6%;

    /* Subtle dark secondary */
    --secondary: 220 15% 12%;
    --secondary-foreground: 220 10% 95%;

    --muted: 220 15% 10%;
    --muted-foreground: 220 10% 65%;

    /* Neon accent colors */
    --accent: 258 100% 65%;
    --accent-foreground: 220 15% 6%;

    /* Trading colors */
    --success: 142 76% 36%;
    --success-foreground: 220 15% 6%;
    --danger: 0 84% 60%;
    --danger-foreground: 220 15% 6%;
    --warning: 48 96% 53%;
    --warning-foreground: 220 15% 6%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 220 10% 95%;

    --border: 220 15% 15%;
    --input: 220 15% 12%;
    --ring: 258 100% 65%;

    --radius: 0.75rem;

    /* Gradients for crypto aesthetic */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(258 100% 75%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 76% 46%));
    --gradient-danger: linear-gradient(135deg, hsl(var(--danger)), hsl(0 84% 70%));
    --gradient-hero: linear-gradient(135deg, hsl(220 15% 6%), hsl(258 100% 15%));
    
    /* Shadows with glow effects */
    --shadow-primary: 0 0 30px hsl(var(--primary) / 0.3);
    --shadow-success: 0 0 20px hsl(var(--success) / 0.3);
    --shadow-danger: 0 0 20px hsl(var(--danger) / 0.3);
    --shadow-card: 0 8px 32px hsl(220 15% 3% / 0.6);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }
  
  .glow-primary {
    box-shadow: var(--shadow-primary);
  }
  
  .glow-success {
    box-shadow: var(--shadow-success);
  }
  
  .glow-danger {
    box-shadow: var(--shadow-danger);
  }
}