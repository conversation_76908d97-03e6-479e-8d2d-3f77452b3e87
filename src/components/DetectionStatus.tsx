/**
 * Detection Status Component
 * 
 * Shows real-time status of the token detection engine
 */

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useDetection } from "@/hooks/useDetection";
import { 
  Activity, 
  Eye, 
  Target, 
  Clock, 
  Zap,
  RefreshCw,
  Play,
  Pause
} from "lucide-react";

export function DetectionStatus() {
  const {
    status,
    isRunning,
    totalTokensDetected,
    recentTokensCount,
    lastActivity,
    loading,
    toggleDetection,
    refreshStatus,
  } = useDetection();

  const formatLastActivity = (activity: string) => {
    if (activity === 'Never') return 'Never';
    try {
      const date = new Date(activity);
      return date.toLocaleTimeString();
    } catch {
      return activity;
    }
  };

  return (
    <Card className="border-border bg-card/50 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Token Detection Engine
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshStatus}
              className="flex items-center gap-1"
            >
              <RefreshCw className="w-3 h-3" />
              Refresh
            </Button>
            <Button
              variant={isRunning ? "secondary" : "default"}
              size="sm"
              onClick={toggleDetection}
              disabled={loading}
              className="flex items-center gap-1"
            >
              {isRunning ? (
                <>
                  <Pause className="w-3 h-3" />
                  Stop
                </>
              ) : (
                <>
                  <Play className="w-3 h-3" />
                  Start
                </>
              )}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Eye className={`w-6 h-6 ${isRunning ? 'text-green-500' : 'text-muted-foreground'}`} />
            </div>
            <p className="text-sm text-muted-foreground">Status</p>
            <Badge variant={isRunning ? "default" : "secondary"}>
              {isRunning ? "Active" : "Inactive"}
            </Badge>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Target className="w-6 h-6 text-primary" />
            </div>
            <p className="text-sm text-muted-foreground">Total Detected</p>
            <p className="text-lg font-bold text-foreground">{totalTokensDetected}</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Zap className="w-6 h-6 text-yellow-500" />
            </div>
            <p className="text-sm text-muted-foreground">Recent Activity</p>
            <p className="text-lg font-bold text-foreground">{recentTokensCount}</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-6 h-6 text-blue-500" />
            </div>
            <p className="text-sm text-muted-foreground">Last Activity</p>
            <p className="text-sm font-medium text-foreground">
              {formatLastActivity(lastActivity)}
            </p>
          </div>
        </div>

        {/* Activity Indicator */}
        {isRunning && (
          <div className="flex items-center justify-center p-4 rounded-lg bg-green-500/10 border border-green-500/20">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-700 dark:text-green-300 font-medium">
                Monitoring Raydium for new token pools...
              </span>
            </div>
          </div>
        )}

        {!isRunning && (
          <div className="flex items-center justify-center p-4 rounded-lg bg-muted/50 border border-border">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
              <span className="text-muted-foreground">
                Detection engine is stopped. Click Start to begin monitoring.
              </span>
            </div>
          </div>
        )}

        {/* Performance Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 pt-2 border-t border-border/50">
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Monitoring</p>
            <p className="text-sm font-medium">Raydium V4 Pools</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Network</p>
            <p className="text-sm font-medium">Solana Mainnet</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Update Rate</p>
            <p className="text-sm font-medium">Real-time</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
