import { Card, CardContent } from "@/components/ui/card";
import { Zap, Shield, TrendingUp, Target, Clock, Lock } from "lucide-react";

export const FeaturesSection = () => {
  const features = [
    {
      icon: Zap,
      title: "Lightning Speed",
      description: "Execute trades in milliseconds with our optimized Solana integration",
      gradient: "from-primary to-accent"
    },
    {
      icon: Shield,
      title: "Advanced Safety",
      description: "Built-in honeypot detection and rug pull protection mechanisms",
      gradient: "from-success to-primary"
    },
    {
      icon: TrendingUp,
      title: "Smart Auto-Sell",
      description: "Automated profit-taking with customizable target prices",
      gradient: "from-accent to-success"
    },
    {
      icon: Target,
      title: "Precision Targeting",
      description: "Filter tokens by liquidity, holders, and custom criteria",
      gradient: "from-primary to-danger"
    },
    {
      icon: Clock,
      title: "24/7 Monitoring",
      description: "Never miss an opportunity with continuous market scanning",
      gradient: "from-success to-accent"
    },
    {
      icon: Lock,
      title: "Secure Trading",
      description: "Your private keys stay secure with client-side wallet management",
      gradient: "from-danger to-primary"
    }
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Powerful Features for 
          <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ml-2">
            Pro Traders
          </span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Everything you need to dominate the Solana token market with speed and precision.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <Card 
            key={index} 
            className="group relative bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300 hover:shadow-card-dark hover:scale-105"
          >
            <CardContent className="p-6">
              <div className="relative">
                {/* Icon with gradient background */}
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${feature.gradient} p-3 mb-4 shadow-lg`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                
                <h3 className="text-xl font-semibold text-foreground mb-3">
                  {feature.title}
                </h3>
                
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};