import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ErrorBoundary, withErrorBoundary, useErrorHandler } from './ErrorBoundary';
import { mockLocalStorage, suppressConsoleError } from '@/test/setup';

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Test component that throws an error
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

// Test component for async errors
const AsyncErrorComponent = () => {
  const { handleAsyncError } = useErrorHandler();
  
  const triggerAsyncError = () => {
    handleAsyncError(new Error('Async test error'), 'test-context');
  };

  return (
    <div>
      <button onClick={triggerAsyncError}>Trigger Async Error</button>
    </div>
  );
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.clear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Error Handling', () => {
    it('should render children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
    });

    it('should catch and display component errors', () => {
      const restoreConsole = suppressConsoleError();

      render(
        <ErrorBoundary level="component">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Component Error')).toBeInTheDocument();
      expect(screen.getByText(/This component encountered an error/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();

      restoreConsole();
    });

    it('should display page-level error for page errors', () => {
      const restoreConsole = suppressConsoleError();

      render(
        <ErrorBoundary level="page">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();
      expect(screen.getByText(/Something went wrong/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();

      restoreConsole();
    });

    it('should display critical error for critical errors', () => {
      const restoreConsole = suppressConsoleError();

      render(
        <ErrorBoundary level="critical">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Critical Error')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reload page/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /go home/i })).toBeInTheDocument();

      restoreConsole();
    });
  });

  describe('Error Recovery', () => {
    it('should retry and recover from error', async () => {
      const restoreConsole = suppressConsoleError();
      let shouldThrow = true;

      const RetryableComponent = () => {
        if (shouldThrow) {
          throw new Error('Retryable error');
        }
        return <div>Recovered</div>;
      };

      render(
        <ErrorBoundary level="component">
          <RetryableComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText('Component Error')).toBeInTheDocument();

      // Simulate recovery
      shouldThrow = false;
      fireEvent.click(screen.getByRole('button', { name: /retry/i }));

      await waitFor(() => {
        expect(screen.getByText('Recovered')).toBeInTheDocument();
      });

      restoreConsole();
    });

    it('should reload page when reload button is clicked', () => {
      const restoreConsole = suppressConsoleError();
      const mockReload = vi.fn();
      Object.defineProperty(window, 'location', {
        value: { reload: mockReload },
        writable: true,
      });

      render(
        <ErrorBoundary level="page">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      fireEvent.click(screen.getByRole('button', { name: /reload page/i }));

      expect(mockReload).toHaveBeenCalled();

      restoreConsole();
    });

    it('should navigate home when go home button is clicked', () => {
      const restoreConsole = suppressConsoleError();
      const originalHref = window.location.href;
      
      render(
        <ErrorBoundary level="critical">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      fireEvent.click(screen.getByRole('button', { name: /go home/i }));

      // Note: In a real test environment, you'd mock window.location.href
      // For now, we just verify the button exists and is clickable
      expect(screen.getByRole('button', { name: /go home/i })).toBeInTheDocument();

      restoreConsole();
    });
  });

  describe('Error Details', () => {
    it('should show/hide technical details', async () => {
      const restoreConsole = suppressConsoleError();

      render(
        <ErrorBoundary level="page">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      // Details should be hidden initially
      expect(screen.queryByText(/Error Details/)).not.toBeInTheDocument();

      // Click to show details
      fireEvent.click(screen.getByRole('button', { name: /show technical details/i }));

      await waitFor(() => {
        expect(screen.getByText(/Error Details/)).toBeInTheDocument();
      });

      // Click to hide details
      fireEvent.click(screen.getByRole('button', { name: /hide technical details/i }));

      await waitFor(() => {
        expect(screen.queryByText(/Error Details/)).not.toBeInTheDocument();
      });

      restoreConsole();
    });

    it('should copy error details to clipboard', async () => {
      const restoreConsole = suppressConsoleError();
      const mockWriteText = vi.fn().mockResolvedValue(undefined);
      Object.defineProperty(navigator, 'clipboard', {
        value: { writeText: mockWriteText },
        writable: true,
      });

      render(
        <ErrorBoundary level="page">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      // Show details first
      fireEvent.click(screen.getByRole('button', { name: /show technical details/i }));

      await waitFor(() => {
        expect(screen.getByText(/Error Details/)).toBeInTheDocument();
      });

      // Copy details
      const copyButtons = screen.getAllByRole('button', { name: /copy/i });
      fireEvent.click(copyButtons[0]);

      expect(mockWriteText).toHaveBeenCalled();

      restoreConsole();
    });
  });

  describe('Custom Error Handler', () => {
    it('should call custom error handler when provided', () => {
      const restoreConsole = suppressConsoleError();
      const mockErrorHandler = vi.fn();

      render(
        <ErrorBoundary onError={mockErrorHandler}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(mockErrorHandler).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String),
        })
      );

      restoreConsole();
    });
  });

  describe('Custom Fallback', () => {
    it('should render custom fallback when provided', () => {
      const restoreConsole = suppressConsoleError();
      const customFallback = <div>Custom Error UI</div>;

      render(
        <ErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Custom Error UI')).toBeInTheDocument();

      restoreConsole();
    });
  });

  describe('Error Storage', () => {
    it('should store error reports in localStorage', () => {
      const restoreConsole = suppressConsoleError();

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'error_reports',
        expect.stringContaining('Test error')
      );

      restoreConsole();
    });
  });
});

describe('withErrorBoundary HOC', () => {
  it('should wrap component with error boundary', () => {
    const TestComponent = () => <div>Test Component</div>;
    const WrappedComponent = withErrorBoundary(TestComponent, { level: 'component' });

    render(<WrappedComponent />);

    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });

  it('should catch errors in wrapped component', () => {
    const restoreConsole = suppressConsoleError();
    const WrappedComponent = withErrorBoundary(ThrowError, { level: 'component' });

    render(<WrappedComponent shouldThrow={true} />);

    expect(screen.getByText('Component Error')).toBeInTheDocument();

    restoreConsole();
  });
});

describe('useErrorHandler hook', () => {
  it('should handle async errors', () => {
    const { toast } = require('sonner');

    render(<AsyncErrorComponent />);

    fireEvent.click(screen.getByRole('button', { name: /trigger async error/i }));

    expect(toast.error).toHaveBeenCalledWith('Error: Async test error');
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'async_errors',
      expect.stringContaining('Async test error')
    );
  });

  it('should store async errors with context', () => {
    render(<AsyncErrorComponent />);

    fireEvent.click(screen.getByRole('button', { name: /trigger async error/i }));

    const storedErrors = JSON.parse(mockLocalStorage.setItem.mock.calls[0][1]);
    expect(storedErrors[0]).toMatchObject({
      message: 'Async test error',
      context: 'test-context',
      type: 'async',
    });
  });

  it('should limit stored async errors', () => {
    // Pre-populate with 20 errors
    const existingErrors = Array.from({ length: 20 }, (_, i) => ({
      message: `Error ${i}`,
      timestamp: new Date().toISOString(),
    }));
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingErrors));

    render(<AsyncErrorComponent />);

    fireEvent.click(screen.getByRole('button', { name: /trigger async error/i }));

    const storedErrors = JSON.parse(mockLocalStorage.setItem.mock.calls[0][1]);
    expect(storedErrors).toHaveLength(20); // Should still be 20 (oldest removed)
    expect(storedErrors[19].message).toBe('Async test error'); // New error at end
  });
});
