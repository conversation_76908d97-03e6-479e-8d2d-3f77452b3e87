import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Play, Square, Wallet, TrendingUp, Activity } from "lucide-react";
import { <PERSON> } from "react-router-dom";

export const DashboardPreview = () => {
  return (
    <section id="dashboard-preview" className="py-20 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Your Trading 
          <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ml-2">
            Command Center
          </span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
          Access all your trading tools in one powerful dashboard.
        </p>
        <Link to="/dashboard">
          <Button variant="hero" size="lg">
            Open Dashboard
          </Button>
        </Link>
      </div>

      {/* Dashboard Preview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sniper Controls */}
        <Card className="bg-card/50 backdrop-blur-sm border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="w-5 h-5 text-primary" />
              Sniper Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm text-muted-foreground">Target Buy Price</label>
              <div className="bg-input border border-border rounded-md px-3 py-2 text-foreground">
                0.00001 SOL
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm text-muted-foreground">Target Sell Price</label>
              <div className="bg-input border border-border rounded-md px-3 py-2 text-foreground">
                0.0001 SOL
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="success" size="sm" className="flex-1">
                <Play className="w-4 h-4 mr-2" />
                Start
              </Button>
              <Button variant="danger" size="sm" className="flex-1">
                <Square className="w-4 h-4 mr-2" />
                Stop
              </Button>
            </div>
            <Badge variant="outline" className="w-full justify-center border-success text-success">
              Status: Running
            </Badge>
          </CardContent>
        </Card>

        {/* Wallet Info */}
        <Card className="bg-card/50 backdrop-blur-sm border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wallet className="w-5 h-5 text-accent" />
              Wallet
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm text-muted-foreground">SOL Balance</label>
              <div className="text-2xl font-bold text-foreground">
                15.2341 SOL
              </div>
              <div className="text-sm text-muted-foreground">
                ~$1,847.32 USD
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm text-muted-foreground">Address</label>
              <div className="bg-input border border-border rounded-md px-3 py-2 text-xs font-mono text-foreground truncate">
                9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
              </div>
            </div>
            <Button variant="outline" size="sm" className="w-full">
              Refresh Balance
            </Button>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="bg-card/50 backdrop-blur-sm border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-warning" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Bought PEPE</span>
              <span className="text-success">+245%</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Sold BONK</span>
              <span className="text-success">+1,024%</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Detected WIF</span>
              <Badge variant="outline" className="text-xs">Analyzing</Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Avoided honeypot</span>
              <span className="text-danger">Saved 2 SOL</span>
            </div>
            <Button variant="ghost" size="sm" className="w-full text-primary">
              View All Activity
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Token List Preview */}
      <Card className="mt-8 bg-card/50 backdrop-blur-sm border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-success" />
            Live Token Feed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-border">
                  <th className="text-left py-2 text-muted-foreground">Token</th>
                  <th className="text-left py-2 text-muted-foreground">Price</th>
                  <th className="text-left py-2 text-muted-foreground">Liquidity</th>
                  <th className="text-left py-2 text-muted-foreground">Holders</th>
                  <th className="text-left py-2 text-muted-foreground">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-border/50">
                  <td className="py-3">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-gradient-primary rounded-full"></div>
                      <span className="font-medium">PEPE</span>
                    </div>
                  </td>
                  <td className="py-3 text-success">$0.00001234</td>
                  <td className="py-3">45.2 SOL</td>
                  <td className="py-3">1,234</td>
                  <td className="py-3">
                    <Button variant="success" size="sm">Buy</Button>
                  </td>
                </tr>
                <tr className="border-b border-border/50">
                  <td className="py-3">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-gradient-danger rounded-full"></div>
                      <span className="font-medium">SCAM</span>
                    </div>
                  </td>
                  <td className="py-3 text-danger">$0.00000001</td>
                  <td className="py-3">12.1 SOL</td>
                  <td className="py-3">89</td>
                  <td className="py-3">
                    <Badge variant="destructive" className="text-xs">Honeypot</Badge>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </section>
  );
};