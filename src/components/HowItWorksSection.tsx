import { Card, CardContent } from "@/components/ui/card";
import { Search, Filter, Zap } from "lucide-react";

export const HowItWorksSection = () => {
  const steps = [
    {
      step: "01",
      icon: Search,
      title: "Real-time Detection",
      description: "Our bot continuously monitors Raydium v4 for newly created liquidity pools and emerging tokens.",
      color: "primary"
    },
    {
      step: "02", 
      icon: Filter,
      title: "Smart Filtering",
      description: "Advanced filters check liquidity levels, holder counts, honeypot status, and your custom criteria.",
      color: "accent"
    },
    {
      step: "03",
      icon: Zap,
      title: "Instant Execution",
      description: "When a token meets your criteria, the bot instantly executes the trade via Jupiter Swap API.",
      color: "success"
    }
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary',
          text: 'text-primary',
          border: 'border-primary/20',
          glow: 'shadow-glow-primary'
        };
      case 'accent':
        return {
          bg: 'bg-accent',
          text: 'text-accent', 
          border: 'border-accent/20',
          glow: 'shadow-glow-primary'
        };
      case 'success':
        return {
          bg: 'bg-success',
          text: 'text-success',
          border: 'border-success/20', 
          glow: 'shadow-glow-success'
        };
      default:
        return {
          bg: 'bg-primary',
          text: 'text-primary',
          border: 'border-primary/20',
          glow: 'shadow-glow-primary'
        };
    }
  };

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          How It 
          <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ml-2">
            Works
          </span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Three simple steps to start sniping profitable Solana tokens automatically.
        </p>
      </div>

      <div className="relative">
        {/* Connection lines */}
        <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary via-accent to-success opacity-30 transform -translate-y-1/2" />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
          {steps.map((step, index) => {
            const colors = getColorClasses(step.color);
            
            return (
              <Card 
                key={index}
                className={`relative group bg-card/50 backdrop-blur-sm border-border hover:${colors.border} transition-all duration-500 hover:${colors.glow}`}
              >
                <CardContent className="p-8 text-center">
                  {/* Step number */}
                  <div className={`inline-flex items-center justify-center w-16 h-16 ${colors.bg} rounded-full mb-6 ${colors.glow} transition-all duration-300 group-hover:scale-110`}>
                    <span className="text-2xl font-bold text-white">{step.step}</span>
                  </div>

                  {/* Icon */}
                  <div className={`inline-flex items-center justify-center w-12 h-12 ${colors.text} mb-4`}>
                    <step.icon className="w-8 h-8" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold text-foreground mb-4">
                    {step.title}
                  </h3>
                  
                  <p className="text-muted-foreground leading-relaxed">
                    {step.description}
                  </p>

                  {/* Connecting arrow for mobile */}
                  {index < steps.length - 1 && (
                    <div className="lg:hidden flex justify-center mt-8">
                      <div className="w-px h-12 bg-gradient-to-b from-primary to-transparent" />
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};