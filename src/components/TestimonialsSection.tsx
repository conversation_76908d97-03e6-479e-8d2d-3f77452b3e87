import { Card, CardContent } from "@/components/ui/card";
import { Star, Quote } from "lucide-react";

export const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "De<PERSON>i Trader",
      avatar: "AC",
      content: "<PERSON><PERSON><PERSON><PERSON> helped me catch 15 tokens in their first hour. The speed is unmatched.",
      rating: 5,
      profit: "+234%"
    },
    {
      name: "<PERSON>", 
      role: "Crypto Investor",
      avatar: "SK",
      content: "The honeypot detection saved me from multiple rug pulls. Best investment I've made.",
      rating: 5,
      profit: "+189%"
    },
    {
      name: "<PERSON>",
      role: "Day Trader",
      avatar: "MR", 
      content: "24/7 monitoring means I never miss opportunities. Made 10x on BONK early entry.",
      rating: 5,
      profit: "+1,024%"
    }
  ];

  const stats = [
    { value: "$2.4M+", label: "Total Volume Traded" },
    { value: "50,000+", label: "Successful Snipes" },
    { value: "99.7%", label: "Success Rate" },
    { value: "24/7", label: "Uptime" }
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      {/* Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
        {stats.map((stat, index) => (
          <div key={index} className="text-center">
            <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-2">
              {stat.value}
            </div>
            <div className="text-muted-foreground text-sm">
              {stat.label}
            </div>
          </div>
        ))}
      </div>

      {/* Testimonials */}
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Trusted by 
          <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ml-2">
            Top Traders
          </span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          See what our community is saying about their trading success.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {testimonials.map((testimonial, index) => (
          <Card 
            key={index}
            className="group relative bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300 hover:shadow-card-dark"
          >
            <CardContent className="p-6">
              {/* Quote icon */}
              <Quote className="w-8 h-8 text-primary/50 mb-4" />
              
              {/* Content */}
              <p className="text-foreground mb-6 leading-relaxed">
                "{testimonial.content}"
              </p>

              {/* Rating */}
              <div className="flex items-center gap-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-warning text-warning" />
                ))}
              </div>

              {/* Author */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-sm font-bold text-primary-foreground">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-foreground text-sm">
                      {testimonial.name}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
                
                {/* Profit badge */}
                <div className="bg-gradient-success text-success-foreground px-3 py-1 rounded-full text-xs font-bold">
                  {testimonial.profit}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};