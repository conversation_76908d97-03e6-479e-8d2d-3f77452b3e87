import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Home, 
  Copy,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { toast } from 'sonner';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'critical';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  errorId: string;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      errorInfo,
    });

    // Log error to console
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report error to monitoring service (if available)
    this.reportError(error, errorInfo);

    // Show toast notification for component-level errors
    if (this.props.level === 'component') {
      toast.error('A component error occurred. Please try refreshing.');
    }
  }

  private reportError(error: Error, errorInfo: ErrorInfo) {
    try {
      // This would integrate with error reporting services like Sentry
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        level: this.props.level || 'component',
      };

      // Log to console for now (replace with actual error reporting)
      console.error('Error Report:', errorReport);

      // Store in localStorage for debugging
      const existingErrors = JSON.parse(localStorage.getItem('error_reports') || '[]');
      existingErrors.push(errorReport);
      
      // Keep only last 10 errors
      if (existingErrors.length > 10) {
        existingErrors.splice(0, existingErrors.length - 10);
      }
      
      localStorage.setItem('error_reports', JSON.stringify(existingErrors));
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private copyErrorDetails = () => {
    const errorDetails = {
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
    toast.success('Error details copied to clipboard');
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo, showDetails, errorId } = this.state;
      const { level = 'component' } = this.props;

      // Critical errors get full-page treatment
      if (level === 'critical' || level === 'page') {
        return (
          <div className="min-h-screen bg-background flex items-center justify-center p-4">
            <Card className="w-full max-w-2xl border-destructive/50 bg-destructive/5">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center">
                    <AlertTriangle className="w-8 h-8 text-destructive" />
                  </div>
                </div>
                <CardTitle className="text-2xl text-destructive">
                  {level === 'critical' ? 'Critical Error' : 'Application Error'}
                </CardTitle>
                <CardDescription>
                  Something went wrong. We're sorry for the inconvenience.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-center space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Error ID: <Badge variant="outline" className="font-mono">{errorId}</Badge>
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {error?.message || 'An unexpected error occurred'}
                  </p>
                </div>

                <Separator />

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button onClick={this.handleRetry} className="flex items-center gap-2">
                    <RefreshCw className="w-4 h-4" />
                    Try Again
                  </Button>
                  <Button variant="outline" onClick={this.handleReload} className="flex items-center gap-2">
                    <RefreshCw className="w-4 h-4" />
                    Reload Page
                  </Button>
                  <Button variant="outline" onClick={this.handleGoHome} className="flex items-center gap-2">
                    <Home className="w-4 h-4" />
                    Go Home
                  </Button>
                </div>

                <Separator />

                <div className="space-y-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={this.toggleDetails}
                    className="w-full flex items-center justify-center gap-2"
                  >
                    <Bug className="w-4 h-4" />
                    {showDetails ? 'Hide' : 'Show'} Technical Details
                    {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </Button>

                  {showDetails && (
                    <div className="space-y-3">
                      <div className="p-3 bg-muted rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium">Error Details</h4>
                          <Button size="sm" variant="ghost" onClick={this.copyErrorDetails}>
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                        <pre className="text-xs text-muted-foreground overflow-auto max-h-32">
                          {error?.stack || error?.message || 'No error details available'}
                        </pre>
                      </div>

                      {errorInfo && (
                        <div className="p-3 bg-muted rounded-lg">
                          <h4 className="text-sm font-medium mb-2">Component Stack</h4>
                          <pre className="text-xs text-muted-foreground overflow-auto max-h-32">
                            {errorInfo.componentStack}
                          </pre>
                        </div>
                      )}

                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" asChild>
                          <a href="https://github.com/your-repo/issues" target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="w-3 h-3 mr-1" />
                            Report Issue
                          </a>
                        </Button>
                        <Button size="sm" variant="outline" onClick={this.copyErrorDetails}>
                          <Copy className="w-3 h-3 mr-1" />
                          Copy Details
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );
      }

      // Component-level errors get inline treatment
      return (
        <Card className="border-destructive/50 bg-destructive/5">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-destructive mt-0.5" />
              <div className="flex-1 space-y-2">
                <div>
                  <h4 className="text-sm font-medium text-destructive">Component Error</h4>
                  <p className="text-xs text-muted-foreground">
                    {error?.message || 'This component encountered an error'}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={this.handleRetry}>
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry
                  </Button>
                  {this.props.showDetails && (
                    <Button size="sm" variant="ghost" onClick={this.toggleDetails}>
                      <Bug className="w-3 h-3 mr-1" />
                      Details
                    </Button>
                  )}
                </div>
                {showDetails && this.props.showDetails && (
                  <div className="mt-2 p-2 bg-muted rounded text-xs">
                    <pre className="overflow-auto max-h-20">
                      {error?.stack || 'No details available'}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easy wrapping
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}

// Hook for error reporting
export function useErrorHandler() {
  const reportError = (error: Error, context?: string) => {
    console.error(`Error in ${context || 'unknown context'}:`, error);
    
    // Report to error boundary
    throw error;
  };

  const handleAsyncError = (error: Error, context?: string) => {
    console.error(`Async error in ${context || 'unknown context'}:`, error);
    
    // Show toast for async errors
    toast.error(`Error: ${error.message}`);
    
    // Store for debugging
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      type: 'async',
    };
    
    const existingErrors = JSON.parse(localStorage.getItem('async_errors') || '[]');
    existingErrors.push(errorReport);
    
    if (existingErrors.length > 20) {
      existingErrors.splice(0, existingErrors.length - 20);
    }
    
    localStorage.setItem('async_errors', JSON.stringify(existingErrors));
  };

  return { reportError, handleAsyncError };
}
