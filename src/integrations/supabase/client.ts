// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dhgjnxhyrgtczcodfsmq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRoZ2pueGh5cmd0Y3pjb2Rmc21xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUxMjkxMDYsImV4cCI6MjA3MDcwNTEwNn0.68QED__-6Nu3uIPFZOjbeBkeiiM_dXZ3Th9j8k5qXJM";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});