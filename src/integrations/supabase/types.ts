export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      bot_logs: {
        Row: {
          action_type: string
          amount_sol: number | null
          created_at: string
          error_message: string | null
          id: string
          price: number | null
          success: boolean | null
          token_address: string | null
          transaction_signature: string | null
          user_id: string
          wallet_id: string | null
        }
        Insert: {
          action_type: string
          amount_sol?: number | null
          created_at?: string
          error_message?: string | null
          id?: string
          price?: number | null
          success?: boolean | null
          token_address?: string | null
          transaction_signature?: string | null
          user_id: string
          wallet_id?: string | null
        }
        Update: {
          action_type?: string
          amount_sol?: number | null
          created_at?: string
          error_message?: string | null
          id?: string
          price?: number | null
          success?: boolean | null
          token_address?: string | null
          transaction_signature?: string | null
          user_id?: string
          wallet_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bot_logs_wallet_id_fkey"
            columns: ["wallet_id"]
            isOneToOne: false
            referencedRelation: "wallets"
            referencedColumns: ["id"]
          },
        ]
      }
      detected_tokens: {
        Row: {
          can_sell: boolean | null
          creation_timestamp: string | null
          current_price: number | null
          detected_at: string
          freeze_authority: string | null
          holders_count: number | null
          id: string
          initial_liquidity_sol: number | null
          is_honeypot: boolean | null
          liquidity_locked: boolean | null
          mint_authority: string | null
          pair_address: string
          token_address: string
          token_name: string | null
          token_symbol: string | null
        }
        Insert: {
          can_sell?: boolean | null
          creation_timestamp?: string | null
          current_price?: number | null
          detected_at?: string
          freeze_authority?: string | null
          holders_count?: number | null
          id?: string
          initial_liquidity_sol?: number | null
          is_honeypot?: boolean | null
          liquidity_locked?: boolean | null
          mint_authority?: string | null
          pair_address: string
          token_address: string
          token_name?: string | null
          token_symbol?: string | null
        }
        Update: {
          can_sell?: boolean | null
          creation_timestamp?: string | null
          current_price?: number | null
          detected_at?: string
          freeze_authority?: string | null
          holders_count?: number | null
          id?: string
          initial_liquidity_sol?: number | null
          is_honeypot?: boolean | null
          liquidity_locked?: boolean | null
          mint_authority?: string | null
          pair_address?: string
          token_address?: string
          token_name?: string | null
          token_symbol?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string
          id: string
          updated_at: string
          user_id: string
          username: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          updated_at?: string
          user_id: string
          username?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          updated_at?: string
          user_id?: string
          username?: string | null
        }
        Relationships: []
      }
      sniper_settings: {
        Row: {
          check_freeze_authority: boolean | null
          check_honeypot: boolean | null
          check_mint_authority: boolean | null
          created_at: string
          id: string
          is_active: boolean | null
          max_buy_amount_sol: number | null
          min_holders: number | null
          min_liquidity_sol: number | null
          stop_loss_percent: number | null
          target_profit_percent: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          check_freeze_authority?: boolean | null
          check_honeypot?: boolean | null
          check_mint_authority?: boolean | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          max_buy_amount_sol?: number | null
          min_holders?: number | null
          min_liquidity_sol?: number | null
          stop_loss_percent?: number | null
          target_profit_percent?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          check_freeze_authority?: boolean | null
          check_honeypot?: boolean | null
          check_mint_authority?: boolean | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          max_buy_amount_sol?: number | null
          min_holders?: number | null
          min_liquidity_sol?: number | null
          stop_loss_percent?: number | null
          target_profit_percent?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      wallets: {
        Row: {
          address: string
          balance_sol: number | null
          created_at: string
          id: string
          is_active: boolean | null
          name: string | null
          private_key_encrypted: string
          updated_at: string
          user_id: string
        }
        Insert: {
          address: string
          balance_sol?: number | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          name?: string | null
          private_key_encrypted: string
          updated_at?: string
          user_id: string
        }
        Update: {
          address?: string
          balance_sol?: number | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          name?: string | null
          private_key_encrypted?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
