/**
 * Polyfills for Node.js globals in browser environment
 * Required for Solana libraries to work properly
 */

import { Buff<PERSON> } from 'buffer';

// Make Buffer available globally
if (typeof window !== 'undefined') {
  window.Buffer = Buffer;
  (window as any).global = window;
  
  // Polyfill process.env if it doesn't exist
  if (typeof (window as any).process === 'undefined') {
    (window as any).process = {
      env: {},
      version: '',
      platform: 'browser',
      nextTick: (fn: Function) => setTimeout(fn, 0),
    };
  }
}

// Make Buffer available globally for all environments
(globalThis as any).Buffer = Buffer;

export { Buffer };
