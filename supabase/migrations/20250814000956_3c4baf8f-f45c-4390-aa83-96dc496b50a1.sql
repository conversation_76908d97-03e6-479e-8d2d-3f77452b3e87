-- Create user profiles table
CREATE TABLE public.profiles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create wallets table
CREATE TABLE public.wallets (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  address TEXT NOT NULL,
  private_key_encrypted TEXT NOT NULL,
  name TEXT DEFAULT 'Main Wallet',
  balance_sol DECIMAL DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id, address)
);

-- <PERSON><PERSON> detected tokens table
CREATE TABLE public.detected_tokens (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  token_address TEXT NOT NULL,
  pair_address TEXT NOT NULL,
  token_name TEXT,
  token_symbol TEXT,
  initial_liquidity_sol DECIMAL,
  current_price DECIMAL,
  holders_count INTEGER DEFAULT 0,
  creation_timestamp TIMESTAMP WITH TIME ZONE,
  mint_authority TEXT,
  freeze_authority TEXT,
  is_honeypot BOOLEAN DEFAULT false,
  can_sell BOOLEAN DEFAULT true,
  liquidity_locked BOOLEAN DEFAULT false,
  detected_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(token_address)
);

-- Create bot logs table
CREATE TABLE public.bot_logs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE,
  token_address TEXT,
  action_type TEXT NOT NULL, -- 'buy', 'sell', 'detect', 'error'
  amount_sol DECIMAL,
  price DECIMAL,
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  transaction_signature TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create sniper settings table
CREATE TABLE public.sniper_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT false,
  min_liquidity_sol DECIMAL DEFAULT 1.0,
  max_buy_amount_sol DECIMAL DEFAULT 0.1,
  target_profit_percent DECIMAL DEFAULT 50.0,
  stop_loss_percent DECIMAL DEFAULT 20.0,
  check_honeypot BOOLEAN DEFAULT true,
  check_mint_authority BOOLEAN DEFAULT true,
  check_freeze_authority BOOLEAN DEFAULT true,
  min_holders INTEGER DEFAULT 10,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.detected_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bot_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sniper_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles
CREATE POLICY "Users can view their own profile" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own profile" 
ON public.profiles 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" 
ON public.profiles 
FOR UPDATE 
USING (auth.uid() = user_id);

-- Create RLS policies for wallets
CREATE POLICY "Users can view their own wallets" 
ON public.wallets 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own wallets" 
ON public.wallets 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own wallets" 
ON public.wallets 
FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own wallets" 
ON public.wallets 
FOR DELETE 
USING (auth.uid() = user_id);

-- Create RLS policies for bot_logs
CREATE POLICY "Users can view their own bot logs" 
ON public.bot_logs 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own bot logs" 
ON public.bot_logs 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for sniper_settings
CREATE POLICY "Users can view their own sniper settings" 
ON public.sniper_settings 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own sniper settings" 
ON public.sniper_settings 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sniper settings" 
ON public.sniper_settings 
FOR UPDATE 
USING (auth.uid() = user_id);

-- Public access to detected tokens (read-only)
CREATE POLICY "Anyone can view detected tokens" 
ON public.detected_tokens 
FOR SELECT 
USING (true);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
NEW.updated_at = now();
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_wallets_updated_at
BEFORE UPDATE ON public.wallets
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_sniper_settings_updated_at
BEFORE UPDATE ON public.sniper_settings
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Enable realtime for tables
ALTER TABLE public.detected_tokens REPLICA IDENTITY FULL;
ALTER TABLE public.bot_logs REPLICA IDENTITY FULL;

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.detected_tokens;
ALTER PUBLICATION supabase_realtime ADD TABLE public.bot_logs;