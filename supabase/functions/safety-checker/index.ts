import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface SafetyCheckRequest {
  tokenAddress: string;
  pairAddress?: string;
}

interface SafetyCheckResult {
  tokenAddress: string;
  isHoneypot: boolean;
  canSell: boolean;
  mintAuthority: string | null;
  freezeAuthority: string | null;
  liquidityLocked: boolean;
  holdersCount: number;
  riskScore: number;
  warnings: string[];
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    );

    if (req.method === 'POST') {
      const { tokenAddress, pairAddress }: SafetyCheckRequest = await req.json();

      if (!tokenAddress) {
        throw new Error('Token address is required');
      }

      console.log('Performing safety check for token:', tokenAddress);

      // Perform comprehensive safety checks
      const safetyResult = await performSafetyChecks(tokenAddress, pairAddress);

      // Update the detected_tokens table with safety data
      const { error: updateError } = await supabaseClient
        .from('detected_tokens')
        .upsert([{
          token_address: tokenAddress,
          pair_address: pairAddress || 'unknown',
          is_honeypot: safetyResult.isHoneypot,
          can_sell: safetyResult.canSell,
          mint_authority: safetyResult.mintAuthority,
          freeze_authority: safetyResult.freezeAuthority,
          liquidity_locked: safetyResult.liquidityLocked,
          holders_count: safetyResult.holdersCount,
        }]);

      if (updateError) {
        console.error('Error updating token safety data:', updateError);
      }

      return new Response(
        JSON.stringify(safetyResult),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (req.method === 'GET') {
      const url = new URL(req.url);
      const tokenAddress = url.searchParams.get('token');

      if (!tokenAddress) {
        throw new Error('Token address is required');
      }

      // Get cached safety data
      const { data: tokenData, error } = await supabaseClient
        .from('detected_tokens')
        .select('*')
        .eq('token_address', tokenAddress)
        .maybeSingle();

      if (error) throw error;

      if (tokenData) {
        const result: SafetyCheckResult = {
          tokenAddress,
          isHoneypot: tokenData.is_honeypot,
          canSell: tokenData.can_sell,
          mintAuthority: tokenData.mint_authority,
          freezeAuthority: tokenData.freeze_authority,
          liquidityLocked: tokenData.liquidity_locked,
          holdersCount: tokenData.holders_count || 0,
          riskScore: calculateRiskScore(tokenData),
          warnings: generateWarnings(tokenData),
        };

        return new Response(
          JSON.stringify(result),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      } else {
        throw new Error('Token not found in database');
      }
    }

  } catch (error) {
    console.error('Error in safety-checker:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function performSafetyChecks(tokenAddress: string, pairAddress?: string): Promise<SafetyCheckResult> {
  // In a real implementation, these would be actual Solana RPC calls
  
  // Simulate honeypot check (would normally simulate a sell transaction)
  const isHoneypot = Math.random() < 0.1; // 10% chance of honeypot
  
  // Simulate sell simulation
  const canSell = !isHoneypot && Math.random() > 0.05; // 95% can sell if not honeypot
  
  // Simulate authority checks
  const mintAuthority = Math.random() > 0.7 ? null : 'authority_address'; // 70% renounced
  const freezeAuthority = Math.random() > 0.8 ? null : 'freeze_authority'; // 80% disabled
  
  // Simulate liquidity lock check
  const liquidityLocked = Math.random() > 0.3; // 70% locked
  
  // Simulate holders count
  const holdersCount = Math.floor(Math.random() * 1000) + 1;
  
  const warnings: string[] = [];
  
  if (isHoneypot) warnings.push('⚠️ Potential honeypot detected');
  if (!canSell) warnings.push('⚠️ Cannot simulate sell transaction');
  if (mintAuthority) warnings.push('⚠️ Mint authority not renounced');
  if (freezeAuthority) warnings.push('⚠️ Freeze authority not disabled');
  if (!liquidityLocked) warnings.push('⚠️ Liquidity not locked');
  if (holdersCount < 10) warnings.push('⚠️ Very few holders');
  
  const riskScore = calculateRiskScoreFromChecks({
    isHoneypot,
    canSell,
    mintAuthority: !!mintAuthority,
    freezeAuthority: !!freezeAuthority,
    liquidityLocked,
    holdersCount,
  });

  return {
    tokenAddress,
    isHoneypot,
    canSell,
    mintAuthority,
    freezeAuthority,
    liquidityLocked,
    holdersCount,
    riskScore,
    warnings,
  };
}

function calculateRiskScore(tokenData: any): number {
  return calculateRiskScoreFromChecks({
    isHoneypot: tokenData.is_honeypot,
    canSell: tokenData.can_sell,
    mintAuthority: !!tokenData.mint_authority,
    freezeAuthority: !!tokenData.freeze_authority,
    liquidityLocked: tokenData.liquidity_locked,
    holdersCount: tokenData.holders_count || 0,
  });
}

function calculateRiskScoreFromChecks(checks: any): number {
  let risk = 0;
  
  if (checks.isHoneypot) risk += 50;
  if (!checks.canSell) risk += 40;
  if (checks.mintAuthority) risk += 20;
  if (checks.freezeAuthority) risk += 15;
  if (!checks.liquidityLocked) risk += 25;
  if (checks.holdersCount < 10) risk += 30;
  else if (checks.holdersCount < 50) risk += 15;
  
  return Math.min(risk, 100);
}

function generateWarnings(tokenData: any): string[] {
  const warnings: string[] = [];
  
  if (tokenData.is_honeypot) warnings.push('⚠️ Potential honeypot detected');
  if (!tokenData.can_sell) warnings.push('⚠️ Cannot simulate sell transaction');
  if (tokenData.mint_authority) warnings.push('⚠️ Mint authority not renounced');
  if (tokenData.freeze_authority) warnings.push('⚠️ Freeze authority not disabled');
  if (!tokenData.liquidity_locked) warnings.push('⚠️ Liquidity not locked');
  if (tokenData.holders_count < 10) warnings.push('⚠️ Very few holders');
  
  return warnings;
}