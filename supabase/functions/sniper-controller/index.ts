import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabaseClient.auth.getUser(token);

    if (!user.user) {
      throw new Error('Unauthorized');
    }

    if (req.method === 'GET') {
      // Get sniper settings and status
      const { data: settings, error } = await supabaseClient
        .from('sniper_settings')
        .select('*')
        .eq('user_id', user.user.id)
        .maybeSingle();

      if (error) throw error;

      // Get recent logs
      const { data: logs, error: logsError } = await supabaseClient
        .from('bot_logs')
        .select('*')
        .eq('user_id', user.user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (logsError) throw logsError;

      return new Response(
        JSON.stringify({ settings, logs }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (req.method === 'POST') {
      const body = await req.json();
      const { action, settings } = body;

      if (action === 'start') {
        // Update settings to active
        const { data, error } = await supabaseClient
          .from('sniper_settings')
          .upsert([{
            user_id: user.user.id,
            is_active: true,
            ...settings,
          }])
          .select()
          .single();

        if (error) throw error;

        // Log sniper start
        await supabaseClient
          .from('bot_logs')
          .insert([{
            user_id: user.user.id,
            action_type: 'start',
            success: true,
          }]);

        console.log('Sniper bot started for user:', user.user.id);

        return new Response(
          JSON.stringify({ success: true, settings: data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      if (action === 'stop') {
        // Update settings to inactive
        const { data, error } = await supabaseClient
          .from('sniper_settings')
          .update({ is_active: false })
          .eq('user_id', user.user.id)
          .select()
          .single();

        if (error) throw error;

        // Log sniper stop
        await supabaseClient
          .from('bot_logs')
          .insert([{
            user_id: user.user.id,
            action_type: 'stop',
            success: true,
          }]);

        console.log('Sniper bot stopped for user:', user.user.id);

        return new Response(
          JSON.stringify({ success: true, settings: data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      if (action === 'update_settings') {
        const { data, error } = await supabaseClient
          .from('sniper_settings')
          .upsert([{
            user_id: user.user.id,
            ...settings,
          }])
          .select()
          .single();

        if (error) throw error;

        return new Response(
          JSON.stringify({ success: true, settings: data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

  } catch (error) {
    console.error('Error in sniper-controller:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});