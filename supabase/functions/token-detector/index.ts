import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface DetectedToken {
  token_address: string;
  pair_address: string;
  token_name?: string;
  token_symbol?: string;
  initial_liquidity_sol: number;
  current_price: number;
  holders_count: number;
  creation_timestamp: string;
  mint_authority?: string;
  freeze_authority?: string;
  is_honeypot: boolean;
  can_sell: boolean;
  liquidity_locked: boolean;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabaseClient.auth.getUser(token);

    if (!user.user) {
      throw new Error('Unauthorized');
    }

    if (req.method === 'POST') {
      const { action } = await req.json();

      if (action === 'start') {
        console.log('Starting token detection for user:', user.user.id);
        
        // In a real implementation, this would connect to Raydium WebSocket
        // For now, we'll simulate token detection
        await simulateTokenDetection(supabaseClient);
        
        return new Response(
          JSON.stringify({ success: true, message: 'Token detection started' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      if (action === 'stop') {
        console.log('Stopping token detection for user:', user.user.id);
        
        return new Response(
          JSON.stringify({ success: true, message: 'Token detection stopped' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    if (req.method === 'GET') {
      // Return detected tokens
      const { data: tokens, error } = await supabaseClient
        .from('detected_tokens')
        .select('*')
        .order('detected_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      return new Response(
        JSON.stringify({ tokens }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

  } catch (error) {
    console.error('Error in token-detector:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function simulateTokenDetection(supabaseClient: any) {
  // Simulate detecting a new token
  const mockToken: DetectedToken = {
    token_address: `${Math.random().toString(36).substring(2)}mock`,
    pair_address: `${Math.random().toString(36).substring(2)}pair`,
    token_name: 'Mock Token',
    token_symbol: 'MOCK',
    initial_liquidity_sol: Math.random() * 10,
    current_price: Math.random() * 0.001,
    holders_count: Math.floor(Math.random() * 100),
    creation_timestamp: new Date().toISOString(),
    mint_authority: null,
    freeze_authority: null,
    is_honeypot: Math.random() > 0.8,
    can_sell: Math.random() > 0.1,
    liquidity_locked: Math.random() > 0.5,
  };

  const { error } = await supabaseClient
    .from('detected_tokens')
    .insert([mockToken]);

  if (error) {
    console.error('Error inserting mock token:', error);
  } else {
    console.log('Mock token detected and saved:', mockToken.token_symbol);
  }
}