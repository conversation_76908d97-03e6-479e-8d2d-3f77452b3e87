import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface TradeRequest {
  action: 'buy' | 'sell';
  tokenAddress: string;
  amountSol: number;
  walletId: string;
  slippage?: number;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabaseClient.auth.getUser(token);

    if (!user.user) {
      throw new Error('Unauthorized');
    }

    if (req.method === 'POST') {
      const tradeRequest: TradeRequest = await req.json();
      const { action, tokenAddress, amountSol, walletId, slippage = 1 } = tradeRequest;

      // Validate wallet ownership
      const { data: wallet, error: walletError } = await supabaseClient
        .from('wallets')
        .select('*')
        .eq('id', walletId)
        .eq('user_id', user.user.id)
        .single();

      if (walletError || !wallet) {
        throw new Error('Wallet not found or unauthorized');
      }

      // In a real implementation, this would:
      // 1. Get Jupiter quote
      // 2. Execute swap transaction
      // 3. Return transaction signature

      const mockTrade = await simulateJupiterTrade(
        supabaseClient,
        user.user.id,
        walletId,
        action,
        tokenAddress,
        amountSol
      );

      return new Response(
        JSON.stringify(mockTrade),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (req.method === 'GET') {
      const url = new URL(req.url);
      const tokenAddress = url.searchParams.get('token');
      
      if (!tokenAddress) {
        throw new Error('Token address is required');
      }

      // Mock Jupiter quote
      const quote = {
        inputMint: 'So11111111111111111111111111111111111111112', // SOL
        outputMint: tokenAddress,
        inAmount: '1000000000', // 1 SOL in lamports
        outAmount: Math.floor(Math.random() * 1000000).toString(),
        priceImpactPct: (Math.random() * 5).toFixed(2),
        marketInfos: [],
      };

      return new Response(
        JSON.stringify({ quote }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

  } catch (error) {
    console.error('Error in jupiter-trader:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function simulateJupiterTrade(
  supabaseClient: any,
  userId: string,
  walletId: string,
  action: 'buy' | 'sell',
  tokenAddress: string,
  amountSol: number
) {
  const success = Math.random() > 0.1; // 90% success rate for simulation
  const transactionSignature = success ? 
    `${Math.random().toString(36).substring(2)}${Date.now()}` : null;

  // Log the trade attempt
  const logEntry = {
    user_id: userId,
    wallet_id: walletId,
    token_address: tokenAddress,
    action_type: action,
    amount_sol: amountSol,
    price: Math.random() * 0.001,
    success,
    error_message: success ? null : 'Simulated trade failure',
    transaction_signature: transactionSignature,
  };

  const { error: logError } = await supabaseClient
    .from('bot_logs')
    .insert([logEntry]);

  if (logError) {
    console.error('Error logging trade:', logError);
  }

  return {
    success,
    transactionSignature,
    error: success ? null : 'Trade simulation failed',
    amountSol,
    action,
    tokenAddress,
  };
}