import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabaseClient.auth.getUser(token);

    if (!user.user) {
      throw new Error('Unauthorized');
    }

    if (req.method === 'GET') {
      // Get user's wallets with balance
      const { data: wallets, error } = await supabaseClient
        .from('wallets')
        .select('*')
        .eq('user_id', user.user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // In a real implementation, fetch live SOL balances from Solana RPC
      const walletsWithBalance = wallets?.map(wallet => ({
        ...wallet,
        balance_sol: Math.random() * 5, // Mock balance
      }));

      return new Response(
        JSON.stringify({ wallets: walletsWithBalance }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (req.method === 'POST') {
      const body = await req.json();
      const { action, address, privateKey, name } = body;

      if (action === 'create') {
        // In a real implementation, generate a new Solana keypair
        const newWallet = {
          user_id: user.user.id,
          address: address || `mock_${Math.random().toString(36).substring(2)}`,
          private_key_encrypted: 'encrypted_' + (privateKey || 'generated_key'),
          name: name || 'New Wallet',
          balance_sol: 0,
          is_active: true,
        };

        const { data, error } = await supabaseClient
          .from('wallets')
          .insert([newWallet])
          .select()
          .single();

        if (error) throw error;

        return new Response(
          JSON.stringify({ wallet: data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      if (action === 'import') {
        if (!address || !privateKey) {
          throw new Error('Address and private key are required');
        }

        // In a real implementation, validate the private key and derive address
        const importedWallet = {
          user_id: user.user.id,
          address,
          private_key_encrypted: 'encrypted_' + privateKey,
          name: name || 'Imported Wallet',
          balance_sol: 0,
          is_active: true,
        };

        const { data, error } = await supabaseClient
          .from('wallets')
          .insert([importedWallet])
          .select()
          .single();

        if (error) throw error;

        return new Response(
          JSON.stringify({ wallet: data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      if (action === 'update_balance') {
        const { walletId } = body;
        
        // In a real implementation, fetch balance from Solana RPC
        const mockBalance = Math.random() * 5;

        const { data, error } = await supabaseClient
          .from('wallets')
          .update({ balance_sol: mockBalance })
          .eq('id', walletId)
          .eq('user_id', user.user.id)
          .select()
          .single();

        if (error) throw error;

        return new Response(
          JSON.stringify({ wallet: data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    if (req.method === 'DELETE') {
      const url = new URL(req.url);
      const walletId = url.searchParams.get('id');

      if (!walletId) {
        throw new Error('Wallet ID is required');
      }

      const { error } = await supabaseClient
        .from('wallets')
        .delete()
        .eq('id', walletId)
        .eq('user_id', user.user.id);

      if (error) throw error;

      return new Response(
        JSON.stringify({ success: true }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

  } catch (error) {
    console.error('Error in wallet-manager:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});