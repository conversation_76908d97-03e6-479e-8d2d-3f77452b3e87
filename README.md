# Swift Sniper Fi - Solana Trading Bot

A comprehensive Solana trading bot with advanced token detection, safety analysis, and automated sniping capabilities.

## 🚀 Features

### Core Functionality
- **Real Solana Integration**: Full blockchain integration with wallet management
- **Jupiter Aggregator**: Real token swapping through Jupiter v6 API
- **Token Detection**: Automated detection of new token launches
- **Safety Analysis**: Comprehensive honeypot and rug pull detection
- **Automated Trading**: Smart sniper bot with risk management
- **Portfolio Management**: Real-time balance tracking and P&L calculation
- **Multi-Wallet Support**: Manage multiple Solana wallets securely

### Advanced Features
- **Real-time Price Feeds**: Multiple price sources with fallback
- **Risk Management**: Stop-loss, take-profit, and position sizing
- **Security**: Encrypted private key storage with user passwords
- **Performance Monitoring**: Detailed trading statistics and analytics
- **Customizable Settings**: Flexible bot configuration
- **Error Handling**: Robust error handling and recovery

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Pages**: Dashboard, Wallet Management, Settings, Auth
- **Components**: Reusable UI components with shadcn/ui
- **Hooks**: Custom hooks for wallet, trading, and data management
- **State Management**: React Query for server state, React Context for app state

### Backend (Supabase)
- **Database**: PostgreSQL with Row Level Security
- **Authentication**: Supabase Auth with email/password
- **Edge Functions**: Serverless functions for trading logic
- **Real-time**: WebSocket subscriptions for live updates

### Blockchain Integration
- **Solana Web3.js**: Direct blockchain interaction
- **SPL Token**: Token account management
- **Jupiter API**: DEX aggregation and routing
- **Price Feeds**: Multiple price sources (Jupiter, DexScreener, CoinGecko)

## 📦 Installation

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account
- Solana RPC endpoint (Helius, QuickNode, or public)

### Setup

1. **Clone the repository**
```bash
git clone https://github.com/your-username/swift-sniper-fi.git
cd swift-sniper-fi
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Configuration**
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Solana Configuration
VITE_SOLANA_NETWORK=devnet
VITE_SOLANA_RPC_URL=https://api.devnet.solana.com

# Jupiter API
VITE_JUPITER_API_URL=https://quote-api.jup.ag/v6
VITE_JUPITER_PRICE_API_URL=https://price.jup.ag/v4

# Supabase
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Trading Configuration
VITE_DEFAULT_SLIPPAGE=1
VITE_MAX_SLIPPAGE=10
VITE_DEFAULT_PRIORITY_FEE=0.0001

# Security
VITE_ENCRYPTION_KEY=your-32-character-encryption-key
```

4. **Database Setup**
Run the Supabase migrations:
```bash
npx supabase db push
```

5. **Start Development Server**
```bash
npm run dev
```
