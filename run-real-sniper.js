/**
 * REAL SNIPER BOT RUNNER
 * 
 * This script runs the real sniper bot with actual transactions
 * WARNING: This involves real money and risk!
 */

import { realSniperBot } from './src/lib/sniper/realSniperBot.ts';
import dotenv from 'dotenv';
import readline from 'readline';

// Load environment variables
dotenv.config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🎯 REAL SOLANA SNIPER BOT');
console.log('=' .repeat(50));
console.log('⚠️  WARNING: This bot trades with REAL MONEY!');
console.log('⚠️  Only use funds you can afford to lose!');
console.log('⚠️  New token trading is extremely risky!');
console.log('=' .repeat(50));

async function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  try {
    // Check if sniper is enabled
    if (process.env.SNIPER_ENABLED !== 'true') {
      console.log('❌ Sniper bot is disabled in .env file');
      console.log('   Set SNIPER_ENABLED=true to enable');
      process.exit(1);
    }

    // Check for wallet private key
    if (!process.env.SNIPER_WALLET_PRIVATE_KEY || process.env.SNIPER_WALLET_PRIVATE_KEY === 'your-wallet-private-key-here') {
      console.log('❌ No wallet private key configured');
      console.log('   Set SNIPER_WALLET_PRIVATE_KEY in .env file');
      process.exit(1);
    }

    // Show configuration
    console.log('\n⚙️ CONFIGURATION:');
    console.log(`   🌐 Network: ${process.env.VITE_SOLANA_NETWORK || 'devnet'}`);
    console.log(`   💧 Min Liquidity: ${process.env.SNIPER_MIN_LIQUIDITY_SOL || '5.0'} SOL`);
    console.log(`   💰 Max Buy: ${process.env.SNIPER_MAX_BUY_AMOUNT_SOL || '0.1'} SOL`);
    console.log(`   👥 Min Holders: ${process.env.SNIPER_MIN_HOLDERS || '10'}`);
    console.log(`   📈 Take Profit: ${process.env.SNIPER_TAKE_PROFIT_PERCENT || '100'}%`);
    console.log(`   📉 Stop Loss: ${process.env.SNIPER_STOP_LOSS_PERCENT || '50'}%`);
    console.log(`   🍯 Honeypot Check: ${process.env.SNIPER_CHECK_HONEYPOT || 'true'}`);

    // Safety confirmation
    const confirm1 = await askQuestion('\n❓ Do you understand the risks? (yes/no): ');
    if (confirm1.toLowerCase() !== 'yes') {
      console.log('❌ Aborted by user');
      process.exit(0);
    }

    const confirm2 = await askQuestion('❓ Are you ready to start real trading? (yes/no): ');
    if (confirm2.toLowerCase() !== 'yes') {
      console.log('❌ Aborted by user');
      process.exit(0);
    }

    const maxBuy = process.env.SNIPER_MAX_BUY_AMOUNT_SOL || '0.1';
    const confirm3 = await askQuestion(`❓ Confirm max buy amount of ${maxBuy} SOL per token? (yes/no): `);
    if (confirm3.toLowerCase() !== 'yes') {
      console.log('❌ Aborted by user');
      process.exit(0);
    }

    console.log('\n🚀 Starting Real Sniper Bot...');

    // Initialize the bot
    await realSniperBot.initialize(
      'demo-user-id', // You'd get this from your auth system
      process.env.SNIPER_WALLET_PRIVATE_KEY
    );

    // Start the bot
    await realSniperBot.start();

    console.log('\n✅ Sniper bot is now running!');
    console.log('📊 Monitoring for new tokens...');
    console.log('💰 Will execute real trades when criteria are met');
    console.log('\nPress Ctrl+C to stop the bot');

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping sniper bot...');
      realSniperBot.stop();
      rl.close();
      process.exit(0);
    });

    // Keep the process running
    setInterval(() => {
      // Bot status check
    }, 30000);

  } catch (error) {
    console.error('❌ Error starting sniper bot:', error);
    rl.close();
    process.exit(1);
  }
}

// Run the bot
main();
