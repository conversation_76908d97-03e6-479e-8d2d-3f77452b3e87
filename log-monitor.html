<!DOCTYPE html>
<html>
<head>
    <title>Sniper <PERSON>t Log <PERSON></title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .log-container {
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid #00ff00;
            padding: 10px;
            background: #001100;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #00ff00;
            padding-left: 10px;
        }
        .timestamp {
            color: #ffff00;
        }
        .error {
            color: #ff0000;
            border-left-color: #ff0000;
        }
        .success {
            color: #00ffff;
            border-left-color: #00ffff;
        }
        .warning {
            color: #ffa500;
            border-left-color: #ffa500;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            background: #003300;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #005500;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .stat-box {
            border: 1px solid #00ff00;
            padding: 10px;
            text-align: center;
            background: #001100;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #00ffff;
        }
    </style>
</head>
<body>
    <h1>🎯 SNIPER BOT LOG MONITOR</h1>
    
    <div class="controls">
        <button onclick="clearLogs()">Clear Logs</button>
        <button onclick="toggleAutoScroll()">Toggle Auto-Scroll</button>
        <button onclick="exportLogs()">Export Logs</button>
        <button onclick="testDetection()">Test Detection</button>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <div class="stat-number" id="totalLogs">0</div>
            <div>Total Logs</div>
        </div>
        <div class="stat-box">
            <div class="stat-number" id="tokenDetections">0</div>
            <div>Token Detections</div>
        </div>
        <div class="stat-box">
            <div class="stat-number" id="raydiumActivity">0</div>
            <div>Raydium Activity</div>
        </div>
        <div class="stat-box">
            <div class="stat-number" id="errors">0</div>
            <div>Errors</div>
        </div>
    </div>
    
    <div class="log-container" id="logContainer">
        <div class="log-entry">
            <span class="timestamp">[12:15:00]</span> 
            🚀 Log monitor started - Waiting for sniper bot activity...
        </div>
    </div>

    <script>
        let autoScroll = true;
        let logCount = 0;
        let tokenDetections = 0;
        let raydiumActivity = 0;
        let errorCount = 0;
        
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('info', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog('warning', args.join(' '));
        };
        
        function addLog(type, message) {
            logCount++;
            
            // Count specific activities
            if (message.includes('token') || message.includes('TOKEN')) {
                tokenDetections++;
            }
            if (message.includes('raydium') || message.includes('RAYDIUM')) {
                raydiumActivity++;
            }
            if (type === 'error') {
                errorCount++;
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            
            logContainer.appendChild(logEntry);
            
            // Update stats
            document.getElementById('totalLogs').textContent = logCount;
            document.getElementById('tokenDetections').textContent = tokenDetections;
            document.getElementById('raydiumActivity').textContent = raydiumActivity;
            document.getElementById('errors').textContent = errorCount;
            
            // Auto-scroll
            if (autoScroll) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            // Keep only last 1000 logs
            if (logContainer.children.length > 1000) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            logCount = 0;
            tokenDetections = 0;
            raydiumActivity = 0;
            errorCount = 0;
            document.getElementById('totalLogs').textContent = '0';
            document.getElementById('tokenDetections').textContent = '0';
            document.getElementById('raydiumActivity').textContent = '0';
            document.getElementById('errors').textContent = '0';
            addLog('info', '🧹 Logs cleared');
        }
        
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            addLog('info', `📜 Auto-scroll ${autoScroll ? 'enabled' : 'disabled'}`);
        }
        
        function exportLogs() {
            const logs = Array.from(document.querySelectorAll('.log-entry')).map(el => el.textContent).join('\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sniper-logs-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            addLog('info', '💾 Logs exported');
        }
        
        function testDetection() {
            addLog('success', '🆕 TEST: New token detected - 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU');
            addLog('info', '🏊 TEST: New raydium market detected at slot 359987000');
            addLog('warning', '⚠️ TEST: Token failed honeypot check');
            addLog('error', '❌ TEST: Failed to execute trade');
        }
        
        // Simulate some activity for demo
        setInterval(() => {
            const activities = [
                '📊 Monitoring active - Slot: ' + (359987000 + Math.floor(Math.random() * 1000)),
                '🔍 Checking for new tokens...',
                '📡 WebSocket connection stable',
                '⚡ Priority fee: 50000 lamports'
            ];
            
            if (Math.random() < 0.1) { // 10% chance
                addLog('info', activities[Math.floor(Math.random() * activities.length)]);
            }
        }, 5000);
        
        // Listen for actual browser console logs
        window.addEventListener('error', (e) => {
            addLog('error', `💥 ${e.message} at ${e.filename}:${e.lineno}`);
        });
        
        // Monitor for detection events
        document.addEventListener('DOMContentLoaded', () => {
            addLog('success', '✅ Log monitor ready - Listening for sniper bot activity');
        });
    </script>
</body>
</html>
