-- Swift Sniper Fi Database Deployment Script
-- This script sets up all required tables and functions for the sniper bot

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  display_name <PERSON><PERSON><PERSON><PERSON>(255),
  avatar_url TEXT,
  theme VARCHAR(20) DEFAULT 'dark',
  language VARCHAR(10) DEFAULT 'en',
  currency VARCHAR(10) DEFAULT 'USD',
  notifications_enabled BOOLEAN DEFAULT true,
  email_notifications B<PERSON><PERSON>EAN DEFAULT true,
  trade_notifications BOOLEAN DEFAULT true,
  security_notifications BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to wallets table
ALTER TABLE wallets ADD COLUMN IF NOT EXISTS is_primary BOOLEAN DEFAULT false;

-- Add missing columns to detected_tokens table
ALTER TABLE detected_tokens ADD COLUMN IF NOT EXISTS safety_score INTEGER DEFAULT 0;
ALTER TABLE detected_tokens ADD COLUMN IF NOT EXISTS risk_factors JSONB DEFAULT '[]'::jsonb;
ALTER TABLE detected_tokens ADD COLUMN IF NOT EXISTS liquidity_pools JSONB DEFAULT '[]'::jsonb;
ALTER TABLE detected_tokens ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;
ALTER TABLE detected_tokens ADD COLUMN IF NOT EXISTS decimals INTEGER DEFAULT 9;
ALTER TABLE detected_tokens ADD COLUMN IF NOT EXISTS supply BIGINT DEFAULT 0;

-- Create trading_settings table
CREATE TABLE IF NOT EXISTS trading_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  auto_trading_enabled BOOLEAN DEFAULT false,
  max_position_size_sol DECIMAL(10,4) DEFAULT 1.0,
  min_safety_score INTEGER DEFAULT 70,
  max_slippage_percent DECIMAL(5,2) DEFAULT 5.0,
  stop_loss_percent DECIMAL(5,2) DEFAULT 20.0,
  take_profit_percent DECIMAL(5,2) DEFAULT 100.0,
  priority_fee_lamports INTEGER DEFAULT 5000,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create trades table
CREATE TABLE IF NOT EXISTS trades (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE,
  type VARCHAR(10) NOT NULL CHECK (type IN ('buy', 'sell', 'snipe_buy', 'snipe_sell')),
  input_mint VARCHAR(255) NOT NULL,
  output_mint VARCHAR(255) NOT NULL,
  input_amount DECIMAL(20,9) NOT NULL,
  output_amount DECIMAL(20,9) NOT NULL,
  price_impact DECIMAL(5,2),
  slippage DECIMAL(5,2),
  signature VARCHAR(255) UNIQUE,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'failed', 'cancelled')),
  safety_score INTEGER,
  response_time_ms INTEGER,
  pnl DECIMAL(20,9) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id);
CREATE INDEX IF NOT EXISTS idx_wallets_is_primary ON wallets(user_id, is_primary);
CREATE INDEX IF NOT EXISTS idx_trades_user_id ON trades(user_id);
CREATE INDEX IF NOT EXISTS idx_trades_wallet_id ON trades(wallet_id);
CREATE INDEX IF NOT EXISTS idx_trades_type ON trades(type);
CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);
CREATE INDEX IF NOT EXISTS idx_trades_created_at ON trades(created_at);
CREATE INDEX IF NOT EXISTS idx_detected_tokens_safety_score ON detected_tokens(safety_score);
CREATE INDEX IF NOT EXISTS idx_detected_tokens_token_symbol ON detected_tokens(token_symbol);

-- Enable Row Level Security on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE detected_tokens ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
CREATE POLICY "Users can view their own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
CREATE POLICY "Users can insert their own profile" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- Create RLS policies for wallets
DROP POLICY IF EXISTS "Users can view their own wallets" ON wallets;
CREATE POLICY "Users can view their own wallets" ON wallets
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own wallets" ON wallets;
CREATE POLICY "Users can insert their own wallets" ON wallets
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own wallets" ON wallets;
CREATE POLICY "Users can update their own wallets" ON wallets
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own wallets" ON wallets;
CREATE POLICY "Users can delete their own wallets" ON wallets
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for trading_settings
DROP POLICY IF EXISTS "Users can view their own trading settings" ON trading_settings;
CREATE POLICY "Users can view their own trading settings" ON trading_settings
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own trading settings" ON trading_settings;
CREATE POLICY "Users can insert their own trading settings" ON trading_settings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own trading settings" ON trading_settings;
CREATE POLICY "Users can update their own trading settings" ON trading_settings
  FOR UPDATE USING (auth.uid() = user_id);

-- Create RLS policies for trades
DROP POLICY IF EXISTS "Users can view their own trades" ON trades;
CREATE POLICY "Users can view their own trades" ON trades
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own trades" ON trades;
CREATE POLICY "Users can insert their own trades" ON trades
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own trades" ON trades;
CREATE POLICY "Users can update their own trades" ON trades
  FOR UPDATE USING (auth.uid() = user_id);

-- Create RLS policies for detected_tokens (public read access)
DROP POLICY IF EXISTS "Anyone can view detected tokens" ON detected_tokens;
CREATE POLICY "Anyone can view detected tokens" ON detected_tokens
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "System can insert detected tokens" ON detected_tokens;
CREATE POLICY "System can insert detected tokens" ON detected_tokens
  FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "System can update detected tokens" ON detected_tokens;
CREATE POLICY "System can update detected tokens" ON detected_tokens
  FOR UPDATE USING (true);

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, display_name)
  VALUES (new.id, COALESCE(new.email, 'User'));
  
  INSERT INTO public.trading_settings (user_id)
  VALUES (new.id);
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function when a new user signs up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

DROP TRIGGER IF EXISTS update_wallets_updated_at ON wallets;
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

DROP TRIGGER IF EXISTS update_trading_settings_updated_at ON trading_settings;
CREATE TRIGGER update_trading_settings_updated_at BEFORE UPDATE ON trading_settings FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

DROP TRIGGER IF EXISTS update_trades_updated_at ON trades;
CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON trades FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

DROP TRIGGER IF EXISTS update_detected_tokens_updated_at ON detected_tokens;
CREATE TRIGGER update_detected_tokens_updated_at BEFORE UPDATE ON detected_tokens FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Success message
SELECT 'Swift Sniper Fi database deployment completed successfully!' as status;
