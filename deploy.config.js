/**
 * Deployment configuration for Swift Sniper Fi
 */

const deploymentConfig = {
  // Environment configurations
  environments: {
    development: {
      name: 'Development',
      url: 'http://localhost:5173',
      solanaNetwork: 'devnet',
      solanaRpcUrl: 'https://api.devnet.solana.com',
      jupiterApiUrl: 'https://quote-api.jup.ag/v6',
      enableAnalytics: false,
      enableErrorReporting: false,
      logLevel: 'debug',
      cacheEnabled: true,
      rateLimitEnabled: false,
    },
    staging: {
      name: 'Staging',
      url: 'https://staging.swiftsniper.fi',
      solanaNetwork: 'devnet',
      solanaRpcUrl: 'https://api.devnet.solana.com',
      jupiterApiUrl: 'https://quote-api.jup.ag/v6',
      enableAnalytics: true,
      enableErrorReporting: true,
      logLevel: 'info',
      cacheEnabled: true,
      rateLimitEnabled: true,
    },
    production: {
      name: 'Production',
      url: 'https://swiftsniper.fi',
      solanaNetwork: 'mainnet-beta',
      solanaRpcUrl: process.env.VITE_SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
      jupiterApiUrl: 'https://quote-api.jup.ag/v6',
      enableAnalytics: true,
      enableErrorReporting: true,
      logLevel: 'warn',
      cacheEnabled: true,
      rateLimitEnabled: true,
    },
  },

  // Build configurations
  build: {
    outputDir: 'dist',
    assetsDir: 'assets',
    sourcemap: process.env.NODE_ENV !== 'production',
    minify: process.env.NODE_ENV === 'production',
    target: 'es2020',
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          solana: ['@solana/web3.js', '@solana/spl-token'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          utils: ['date-fns', 'crypto-js', 'sonner'],
        },
      },
    },
  },

  // Security configurations
  security: {
    contentSecurityPolicy: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      'font-src': ["'self'", 'https://fonts.gstatic.com'],
      'img-src': ["'self'", 'data:', 'https:'],
      'connect-src': [
        "'self'",
        'https://api.mainnet-beta.solana.com',
        'https://api.devnet.solana.com',
        'https://quote-api.jup.ag',
        'https://price.jup.ag',
        'wss://api.mainnet-beta.solana.com',
        'wss://api.devnet.solana.com',
      ],
    },
    headers: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    },
  },

  // Performance configurations
  performance: {
    preload: [
      'fonts/inter.woff2',
    ],
    prefetch: [
      '/api/tokens/popular',
      '/api/user/settings',
    ],
    compression: {
      gzip: true,
      brotli: true,
    },
    caching: {
      static: '1y',
      api: '5m',
      html: '0',
    },
  },

  // Monitoring configurations
  monitoring: {
    analytics: {
      enabled: process.env.NODE_ENV === 'production',
      provider: 'custom', // Could be 'google', 'mixpanel', etc.
      trackingId: process.env.VITE_ANALYTICS_ID,
    },
    errorReporting: {
      enabled: process.env.NODE_ENV === 'production',
      provider: 'custom', // Could be 'sentry', 'bugsnag', etc.
      apiKey: process.env.VITE_ERROR_REPORTING_KEY,
    },
    performance: {
      enabled: true,
      sampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    },
  },

  // Feature flags
  features: {
    autoTrading: true,
    tokenDetection: true,
    portfolioTracking: true,
    multiWallet: true,
    advancedCharts: false, // Coming soon
    socialTrading: false, // Coming soon
    mobileApp: false, // Coming soon
  },

  // API configurations
  api: {
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
    rateLimit: {
      requests: 100,
      window: 60000, // 1 minute
    },
  },

  // Database configurations
  database: {
    connectionPoolSize: 10,
    queryTimeout: 30000,
    enableLogging: process.env.NODE_ENV === 'development',
  },
};

// Export configuration based on environment
const getConfig = (env = process.env.NODE_ENV || 'development') => {
  const baseConfig = deploymentConfig.environments[env] || deploymentConfig.environments.development;
  
  return {
    ...baseConfig,
    build: deploymentConfig.build,
    security: deploymentConfig.security,
    performance: deploymentConfig.performance,
    monitoring: deploymentConfig.monitoring,
    features: deploymentConfig.features,
    api: deploymentConfig.api,
    database: deploymentConfig.database,
  };
};

module.exports = {
  deploymentConfig,
  getConfig,
};

// Vite plugin for injecting config
export const configPlugin = () => {
  return {
    name: 'config-plugin',
    config(config, { mode }) {
      const deployConfig = getConfig(mode);
      
      // Inject environment variables
      config.define = {
        ...config.define,
        __APP_CONFIG__: JSON.stringify(deployConfig),
      };

      // Configure build options
      config.build = {
        ...config.build,
        ...deployConfig.build,
      };

      return config;
    },
  };
};
