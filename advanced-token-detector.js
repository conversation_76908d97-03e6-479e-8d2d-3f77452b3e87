/**
 * ADVANCED TOKEN DETECTOR
 * 
 * This is the ULTIMATE token detection system that:
 * 1. Filters for NEW pool creation events only
 * 2. Parses account data properly (when data size > 0)
 * 3. Identifies actual new token mints (specific event types)
 * 4. Uses advanced Raydium pool structure parsing
 */

import { Connection, PublicKey } from '@solana/web3.js';

// Advanced configuration
const CONFIG = {
  SOLANA_RPC: 'https://mainnet.helius-rpc.com/?api-key=7467031f-f99b-4f0d-b90e-21a70a6cdacf',
  SOLANA_WS: 'wss://mainnet.helius-rpc.com/?api-key=7467031f-f99b-4f0d-b90e-21a70a6cdacf',
  RAYDIUM_PROGRAM_ID: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
  
  // Pool size filters
  MIN_POOL_SIZE: 200,    // Minimum bytes for a real pool
  EXPECTED_POOL_SIZE: 752, // Expected size for full AMM pool
};

// Advanced connection
const connection = new Connection(CONFIG.SOLANA_RPC, {
  commitment: 'confirmed',
  wsEndpoint: CONFIG.SOLANA_WS,
});

console.log('🎯 ADVANCED TOKEN DETECTOR');
console.log('=' .repeat(50));
console.log('🌐 Network: MAINNET');
console.log('📡 RPC: Helius Premium');
console.log('🔍 Mode: NEW POOL CREATION ONLY');
console.log('🎯 Target: REAL TOKEN MINTS');
console.log('=' .repeat(50));

let totalActivity = 0;
let newPoolsDetected = 0;
let tokensFound = 0;
const detectedTokens = new Set();

/**
 * Check if account data represents a NEW pool creation
 */
function isNewPoolCreation(data) {
  if (!data || data.length === 0) {
    return false; // Empty data = deletion/update
  }

  // New pools have substantial data
  if (data.length < CONFIG.MIN_POOL_SIZE) {
    return false; // Too small to be a new pool
  }

  // Full AMM pools are around 752 bytes
  if (data.length >= CONFIG.EXPECTED_POOL_SIZE) {
    return true; // Likely a full new pool
  }

  // Partial pools during initialization
  if (data.length >= CONFIG.MIN_POOL_SIZE) {
    return true; // Likely a new pool being created
  }

  return false;
}

/**
 * Validate if an address is a valid token mint
 */
function isValidTokenMint(mintAddress) {
  try {
    if (!mintAddress || mintAddress.length !== 44) {
      return false;
    }

    // Exclude known system accounts
    const excludedPrefixes = [
      '********', // System Program
      'So********', // Wrapped SOL
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
      'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL', // Associated Token Program
    ];

    for (const prefix of excludedPrefixes) {
      if (mintAddress.startsWith(prefix)) {
        return false;
      }
    }

    // Validate base58
    try {
      new PublicKey(mintAddress);
      return true;
    } catch {
      return false;
    }
  } catch {
    return false;
  }
}

/**
 * Extract token mints from Raydium pool data
 */
function extractTokenMints(data) {
  const mints = [];

  try {
    console.log(`🔍 PARSING POOL DATA (${data.length} bytes)`);

    // Method 1: Known offsets for Raydium AMM pools
    if (data.length >= CONFIG.EXPECTED_POOL_SIZE) {
      const offsets = [400, 432, 464, 496]; // Common mint locations
      
      for (const offset of offsets) {
        if (data.length >= offset + 32) {
          try {
            const mintBytes = data.slice(offset, offset + 32);
            if (!mintBytes.every(byte => byte === 0)) {
              const mintAddress = new PublicKey(mintBytes).toBase58();
              if (isValidTokenMint(mintAddress) && !mints.includes(mintAddress)) {
                console.log(`🎯 TOKEN MINT (offset ${offset}): ${mintAddress}`);
                mints.push(mintAddress);
              }
            }
          } catch (error) {
            // Skip invalid mints
          }
        }
      }
    }

    // Method 2: Scan entire buffer if no mints found
    if (mints.length === 0) {
      console.log(`🔍 SCANNING ENTIRE BUFFER...`);
      
      for (let i = 0; i <= data.length - 32; i += 4) {
        try {
          const mintBytes = data.slice(i, i + 32);
          
          if (!mintBytes.every(byte => byte === 0) && 
              !mintBytes.every(byte => byte === 255)) {
            
            const mintAddress = new PublicKey(mintBytes).toBase58();
            if (isValidTokenMint(mintAddress) && !mints.includes(mintAddress)) {
              console.log(`🪙 SCANNED MINT (offset ${i}): ${mintAddress}`);
              mints.push(mintAddress);
              
              if (mints.length >= 5) break; // Limit results
            }
          }
        } catch (error) {
          // Skip invalid data
        }
      }
    }

    return mints;
  } catch (error) {
    console.error('Error extracting mints:', error);
    return [];
  }
}

/**
 * Start advanced token detection
 */
async function startAdvancedDetection() {
  try {
    console.log('\n🚀 Starting ADVANCED token detection...');
    
    // Monitor Raydium program for NEW POOL CREATION ONLY
    const subscription = connection.onProgramAccountChange(
      new PublicKey(CONFIG.RAYDIUM_PROGRAM_ID),
      (accountInfo, context) => {
        totalActivity++;
        
        try {
          // FILTER 1: Only process accounts with substantial data
          if (!isNewPoolCreation(accountInfo.data)) {
            return; // Skip updates/deletions
          }

          newPoolsDetected++;
          console.log(`\n🏊 NEW POOL DETECTED #${newPoolsDetected}`);
          console.log(`   📊 Slot: ${context.slot}`);
          console.log(`   📝 Data size: ${accountInfo.data.length} bytes`);
          console.log(`   ⏰ Time: ${new Date().toLocaleTimeString()}`);

          // FILTER 2: Extract actual token mints
          const tokenMints = extractTokenMints(accountInfo.data);

          if (tokenMints.length > 0) {
            console.log(`\n🎯 FOUND ${tokenMints.length} TOKEN MINTS:`);
            
            tokenMints.forEach((mint, index) => {
              if (!detectedTokens.has(mint)) {
                tokensFound++;
                detectedTokens.add(mint);
                
                console.log(`   ${index + 1}. 🆕 NEW TOKEN: ${mint}`);
                console.log(`      🔗 Explorer: https://solscan.io/token/${mint}`);
                console.log(`      📊 Total unique tokens found: ${tokensFound}`);
              } else {
                console.log(`   ${index + 1}. ♻️  Known token: ${mint}`);
              }
            });
          } else {
            console.log(`   ❌ No valid token mints found in pool data`);
          }

        } catch (error) {
          console.error('Error processing pool:', error.message);
        }
      },
      'confirmed'
    );

    console.log(`✅ Monitoring Raydium for NEW POOLS (Sub ID: ${subscription})`);
    console.log('🎯 Filtering for REAL token creation events...');

    // Status updates
    setInterval(() => {
      console.log(`\n📊 [${new Date().toLocaleTimeString()}] DETECTION STATUS:`);
      console.log(`   📈 Total activity: ${totalActivity}`);
      console.log(`   🏊 New pools detected: ${newPoolsDetected}`);
      console.log(`   🪙 Unique tokens found: ${tokensFound}`);
      console.log(`   ✅ Detection active...`);
      
      if (tokensFound > 0) {
        console.log(`\n🎯 RECENT TOKENS:`);
        const recentTokens = Array.from(detectedTokens).slice(-3);
        recentTokens.forEach((token, index) => {
          console.log(`   ${index + 1}. ${token}`);
        });
      }
    }, 60000); // Every 60 seconds

  } catch (error) {
    console.error('❌ Failed to start detection:', error);
  }
}

// Start the advanced detector
startAdvancedDetection();

// Keep process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping advanced detector...');
  console.log(`📊 Final stats: ${newPoolsDetected} pools, ${tokensFound} tokens`);
  process.exit(0);
});
