/**
 * FRONTEND LOG MONITOR
 * 
 * This script monitors the frontend detection logs
 * and extracts actual token information
 */

console.log('🎯 FRONTEND LOG MONITOR');
console.log('=' .repeat(40));
console.log('📊 Monitoring frontend detection logs...');
console.log('🔍 Looking for actual token addresses...');
console.log('=' .repeat(40));

let detectionCount = 0;
let tokenCount = 0;
let marketCount = 0;

// Monitor console logs from the browser
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

// Track detected activities
const detectedTokens = new Set();
const detectedMarkets = new Set();

function analyzeLogMessage(message) {
  const timestamp = new Date().toLocaleTimeString();
  
  // Look for Raydium market detection
  if (message.includes('New raydium market detected')) {
    marketCount++;
    const slotMatch = message.match(/slot (\d+)/);
    const slot = slotMatch ? slotMatch[1] : 'unknown';
    
    console.log(`\n🏊 RAYDIUM MARKET #${marketCount}`);
    console.log(`   📊 Slot: ${slot}`);
    console.log(`   ⏰ Time: ${timestamp}`);
    
    detectedMarkets.add(slot);
  }
  
  // Look for token mint addresses
  const tokenRegex = /[1-9A-HJ-NP-Za-km-z]{32,44}/g;
  const potentialTokens = message.match(tokenRegex);
  
  if (potentialTokens) {
    for (const token of potentialTokens) {
      if (token.length >= 32 && token.length <= 44 && !detectedTokens.has(token)) {
        tokenCount++;
        detectedTokens.add(token);
        
        console.log(`\n🪙 NEW TOKEN DETECTED #${tokenCount}`);
        console.log(`   🔑 Address: ${token}`);
        console.log(`   ⏰ Time: ${timestamp}`);
        console.log(`   📝 Source: ${message.slice(0, 100)}...`);
      }
    }
  }
  
  // Look for specific detection patterns
  if (message.includes('Potential token mint found')) {
    const mintMatch = message.match(/([1-9A-HJ-NP-Za-km-z]{32,44})/);
    if (mintMatch) {
      const mint = mintMatch[1];
      if (!detectedTokens.has(mint)) {
        tokenCount++;
        detectedTokens.add(mint);
        
        console.log(`\n🎯 POTENTIAL TOKEN #${tokenCount}`);
        console.log(`   🔑 Mint: ${mint}`);
        console.log(`   ⏰ Time: ${timestamp}`);
      }
    }
  }
  
  // Look for analysis results
  if (message.includes('Analyzing token from new')) {
    const mintMatch = message.match(/([1-9A-HJ-NP-Za-km-z]{32,44})/);
    if (mintMatch) {
      const mint = mintMatch[1];
      console.log(`\n🔬 ANALYZING TOKEN: ${mint}`);
      console.log(`   ⏰ Time: ${timestamp}`);
    }
  }
}

// Override console methods to capture logs
console.log = function(...args) {
  const message = args.join(' ');
  analyzeLogMessage(message);
  originalConsoleLog.apply(console, args);
};

console.warn = function(...args) {
  const message = args.join(' ');
  analyzeLogMessage(message);
  originalConsoleWarn.apply(console, args);
};

console.error = function(...args) {
  const message = args.join(' ');
  analyzeLogMessage(message);
  originalConsoleError.apply(console, args);
};

// Periodic status updates
setInterval(() => {
  console.log(`\n📊 [${new Date().toLocaleTimeString()}] MONITORING STATUS:`);
  console.log(`   🏊 Markets detected: ${marketCount}`);
  console.log(`   🪙 Unique tokens: ${detectedTokens.size}`);
  console.log(`   📈 Total detections: ${detectionCount}`);
  console.log(`   ✅ Monitor active...`);
  
  // Show recent tokens
  if (detectedTokens.size > 0) {
    console.log(`\n🎯 RECENT TOKENS:`);
    const recentTokens = Array.from(detectedTokens).slice(-3);
    recentTokens.forEach((token, index) => {
      console.log(`   ${index + 1}. ${token}`);
    });
  }
}, 30000); // Every 30 seconds

// Simulate some frontend activity for testing
setTimeout(() => {
  console.log('🏪 New raydium market detected at slot 359989100');
  console.log('🪙 Potential token mint found: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
  console.log('🔍 Analyzing token from new raydium market: 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU');
}, 5000);

console.log('\n✅ Frontend log monitor started');
console.log('📡 Listening for detection events...');
console.log('🎯 Will capture and analyze token addresses');

// Keep the monitor running
setInterval(() => {
  // Monitor heartbeat
}, 60000);
